#!/usr/bin/env python3
"""
Zone Creator - Standalone Application for Creating and Managing Trading Zones

A dedicated tool for creating, editing, and managing trading zones with an intuitive interface.
"""

import sys
import json
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

try:
    from PyQt6 import QtWidgets, QtCore, QtGui
    from PyQt6.QtCore import Qt, pyqtSignal
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QLabel, QSpinBox, QDoubleSpinBox,
                                QListWidget, QListWidgetItem, QGroupBox, QGridLayout,
                                QColorDialog, QMessageBox, QFileDialog, QComboBox,
                                QCheckBox, QLineEdit, QTextEdit)
    from PyQt6.QtGui import QColor, QPalette, QFont
except ImportError:
    print("PyQt6 not found, trying PyQt5...")
    from PyQt5 import QtWidgets, QtCore, QtGui
    from PyQt5.QtCore import Qt, pyqtSignal
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QLabel, QSpinBox, QDoubleSpinBox,
                                QListWidget, QListWidgetItem, QGroupBox, QGridLayout,
                                QColorDialog, QMessageBox, QFileDialog, QComboBox,
                                QCheckBox, QLineEdit, QTextEdit)
    from PyQt5.QtGui import QColor, QPalette, QFont

try:
    import pyqtgraph as pg
    pg.setConfigOptions(antialias=True)
except ImportError:
    print("PyQtGraph not found. Please install it: pip install pyqtgraph")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Theme colors
THEME_COLORS = {
    'background': '#1e1e1e',
    'surface': '#2d2d2d',
    'primary': '#3d3d3d',
    'accent': '#4a9eff',
    'secondary_accent': '#ff6b6b',
    'text': '#ffffff',
    'text_secondary': '#b0b0b0',
    'border': '#555555',
    'success': '#4caf50',
    'warning': '#ff9800',
    'error': '#f44336'
}

@dataclass
class Zone:
    """Data class representing a trading zone."""
    id: int
    name: str
    top: float
    bottom: float
    color: str
    zone_type: str
    description: str
    created_at: str
    is_active: bool = True

class ZoneItem(pg.GraphicsObject):
    """Visual representation of a zone on the plot."""
    
    def __init__(self, zone: Zone, plot_widget):
        super().__init__()
        self.zone = zone
        self.plot_widget = plot_widget
        self.rect_item = None
        self.text_item = None
        self.setup_graphics()
        
    def setup_graphics(self):
        """Set up the visual elements of the zone."""
        # Create rectangle
        width = 10  # Fixed width for zones
        height = self.zone.top - self.zone.bottom
        
        self.rect_item = pg.QtWidgets.QGraphicsRectItem(
            -5, self.zone.bottom, width, height
        )
        
        # Set colors
        color = QColor(self.zone.color)
        pen = pg.mkPen(color=color, width=2)
        brush = pg.mkBrush(color=color.lighter(150))
        brush.setStyle(Qt.BrushStyle.Dense4Pattern)
        
        self.rect_item.setPen(pen)
        self.rect_item.setBrush(brush)
        
        # Add text label
        self.text_item = pg.TextItem(
            text=self.zone.name,
            color=color,
            anchor=(0.5, 0.5)
        )
        self.text_item.setPos(0, (self.zone.top + self.zone.bottom) / 2)
        
    def paint(self, painter, option, widget):
        """Required by GraphicsObject but not used."""
        pass
        
    def boundingRect(self):
        """Return the bounding rectangle."""
        return QtCore.QRectF(-5, self.zone.bottom, 10, self.zone.top - self.zone.bottom)

class ZoneCreatorWidget(QMainWindow):
    """Main application window for the Zone Creator."""
    
    def __init__(self):
        super().__init__()
        self.zones: List[Zone] = []
        self.zone_counter = 1
        self.current_price = 100.0  # Default reference price
        self.zone_items: Dict[int, ZoneItem] = {}
        
        self.setup_ui()
        self.setup_plot()
        self.apply_theme()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Zone Creator - Trading Zone Management Tool")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel for controls
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # Right panel for plot
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 2)
        
    def create_left_panel(self):
        """Create the left control panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Zone Creation Group
        creation_group = QGroupBox("Create New Zone")
        creation_layout = QGridLayout(creation_group)
        
        # Zone name
        creation_layout.addWidget(QLabel("Name:"), 0, 0)
        self.zone_name_input = QLineEdit()
        self.zone_name_input.setPlaceholderText("Enter zone name...")
        creation_layout.addWidget(self.zone_name_input, 0, 1)
        
        # Zone top
        creation_layout.addWidget(QLabel("Top:"), 1, 0)
        self.zone_top_input = QDoubleSpinBox()
        self.zone_top_input.setRange(-999999, 999999)
        self.zone_top_input.setDecimals(2)
        self.zone_top_input.setValue(105.0)
        creation_layout.addWidget(self.zone_top_input, 1, 1)
        
        # Zone bottom
        creation_layout.addWidget(QLabel("Bottom:"), 2, 0)
        self.zone_bottom_input = QDoubleSpinBox()
        self.zone_bottom_input.setRange(-999999, 999999)
        self.zone_bottom_input.setDecimals(2)
        self.zone_bottom_input.setValue(95.0)
        creation_layout.addWidget(self.zone_bottom_input, 2, 1)
        
        # Zone type
        creation_layout.addWidget(QLabel("Type:"), 3, 0)
        self.zone_type_combo = QComboBox()
        self.zone_type_combo.addItems(["Support", "Resistance", "Supply", "Demand", "Custom"])
        creation_layout.addWidget(self.zone_type_combo, 3, 1)
        
        # Color selection
        creation_layout.addWidget(QLabel("Color:"), 4, 0)
        self.color_button = QPushButton()
        self.color_button.setFixedHeight(30)
        self.current_color = "#4a9eff"
        self.color_button.setStyleSheet(f"background-color: {self.current_color}; border: 1px solid #555;")
        self.color_button.clicked.connect(self.choose_color)
        creation_layout.addWidget(self.color_button, 4, 1)
        
        # Description
        creation_layout.addWidget(QLabel("Description:"), 5, 0)
        self.zone_description = QTextEdit()
        self.zone_description.setMaximumHeight(60)
        self.zone_description.setPlaceholderText("Optional description...")
        creation_layout.addWidget(self.zone_description, 5, 1)
        
        # Create button
        create_btn = QPushButton("Create Zone")
        create_btn.clicked.connect(self.create_zone)
        creation_layout.addWidget(create_btn, 6, 0, 1, 2)
        
        layout.addWidget(creation_group)
        
        # Quick Create Group
        quick_group = QGroupBox("Quick Create")
        quick_layout = QVBoxLayout(quick_group)
        
        # Current price input
        price_layout = QHBoxLayout()
        price_layout.addWidget(QLabel("Current Price:"))
        self.current_price_input = QDoubleSpinBox()
        self.current_price_input.setRange(0, 999999)
        self.current_price_input.setDecimals(2)
        self.current_price_input.setValue(self.current_price)
        self.current_price_input.valueChanged.connect(self.update_current_price)
        price_layout.addWidget(self.current_price_input)
        quick_layout.addLayout(price_layout)
        
        # Quick zone buttons
        quick_support_btn = QPushButton("Support Zone (+/- 2%)")
        quick_support_btn.clicked.connect(lambda: self.create_quick_zone("support"))
        quick_layout.addWidget(quick_support_btn)
        
        quick_resistance_btn = QPushButton("Resistance Zone (+/- 2%)")
        quick_resistance_btn.clicked.connect(lambda: self.create_quick_zone("resistance"))
        quick_layout.addWidget(quick_resistance_btn)
        
        layout.addWidget(quick_group)
        
        # Zone List Group
        list_group = QGroupBox("Existing Zones")
        list_layout = QVBoxLayout(list_group)
        
        self.zone_list = QListWidget()
        self.zone_list.itemClicked.connect(self.select_zone)
        list_layout.addWidget(self.zone_list)
        
        # Zone management buttons
        btn_layout = QHBoxLayout()
        edit_btn = QPushButton("Edit")
        edit_btn.clicked.connect(self.edit_selected_zone)
        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(self.delete_selected_zone)
        btn_layout.addWidget(edit_btn)
        btn_layout.addWidget(delete_btn)
        list_layout.addLayout(btn_layout)
        
        layout.addWidget(list_group)
        
        # File operations
        file_group = QGroupBox("File Operations")
        file_layout = QVBoxLayout(file_group)
        
        save_btn = QPushButton("Save Zones")
        save_btn.clicked.connect(self.save_zones)
        load_btn = QPushButton("Load Zones")
        load_btn.clicked.connect(self.load_zones)
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_all_zones)
        
        file_layout.addWidget(save_btn)
        file_layout.addWidget(load_btn)
        file_layout.addWidget(clear_btn)
        
        layout.addWidget(file_group)
        
        layout.addStretch()
        return panel

    def create_right_panel(self):
        """Create the right panel with the plot."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Plot title
        title = QLabel("Zone Visualization")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Price')
        self.plot_widget.setLabel('bottom', 'Time/Position')
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)

        layout.addWidget(self.plot_widget)

        # Instructions
        instructions = QLabel(
            "Double-click on the plot to create a zone at that price level.\n"
            "Use the controls on the left to customize zones."
        )
        instructions.setAlignment(Qt.AlignmentFlag.AlignCenter)
        instructions.setStyleSheet("color: #b0b0b0; font-style: italic;")
        layout.addWidget(instructions)

        return panel

    def setup_plot(self):
        """Set up the plot widget."""
        # Set plot range
        self.plot_widget.setXRange(-5, 5)
        self.plot_widget.setYRange(80, 120)

        # Add current price line
        self.current_price_line = pg.InfiniteLine(
            pos=self.current_price,
            angle=0,
            pen=pg.mkPen(color='yellow', width=2, style=Qt.PenStyle.DashLine)
        )
        self.plot_widget.addItem(self.current_price_line)

        # Connect double-click event
        self.plot_widget.mouseDoubleClickEvent = self.plot_double_click

    def apply_theme(self):
        """Apply dark theme to the application."""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
            }}
            QWidget {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['border']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['surface']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            QPushButton {{
                background-color: {THEME_COLORS['primary']};
                border: 1px solid {THEME_COLORS['border']};
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['accent']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['secondary_accent']};
            }}
            QLineEdit, QDoubleSpinBox, QSpinBox, QComboBox, QTextEdit {{
                background-color: {THEME_COLORS['surface']};
                border: 1px solid {THEME_COLORS['border']};
                border-radius: 4px;
                padding: 4px;
            }}
            QListWidget {{
                background-color: {THEME_COLORS['surface']};
                border: 1px solid {THEME_COLORS['border']};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 4px;
                border-bottom: 1px solid {THEME_COLORS['border']};
            }}
            QListWidget::item:selected {{
                background-color: {THEME_COLORS['accent']};
            }}
        """)

    def choose_color(self):
        """Open color picker dialog."""
        color = QColorDialog.getColor(QColor(self.current_color), self)
        if color.isValid():
            self.current_color = color.name()
            self.color_button.setStyleSheet(
                f"background-color: {self.current_color}; border: 1px solid #555;"
            )

    def update_current_price(self, value):
        """Update the current price and move the price line."""
        self.current_price = value
        self.current_price_line.setPos(value)

    def plot_double_click(self, event):
        """Handle double-click on plot to create zone."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Get the position in plot coordinates
            view_box = self.plot_widget.getPlotItem().getViewBox()
            plot_pos = view_box.mapSceneToView(event.pos())

            # Create zone centered on click position
            center_price = plot_pos.y()
            zone_height = 2.0  # Default zone height

            self.zone_top_input.setValue(center_price + zone_height/2)
            self.zone_bottom_input.setValue(center_price - zone_height/2)

            # Auto-generate name if empty
            if not self.zone_name_input.text():
                self.zone_name_input.setText(f"Zone {self.zone_counter}")

            # Create the zone
            self.create_zone()

    def create_zone(self):
        """Create a new zone with current input values."""
        try:
            name = self.zone_name_input.text() or f"Zone {self.zone_counter}"
            top = self.zone_top_input.value()
            bottom = self.zone_bottom_input.value()
            zone_type = self.zone_type_combo.currentText()
            description = self.zone_description.toPlainText()

            # Validate inputs
            if top <= bottom:
                QMessageBox.warning(self, "Invalid Zone", "Zone top must be higher than bottom!")
                return

            # Create zone object
            zone = Zone(
                id=self.zone_counter,
                name=name,
                top=top,
                bottom=bottom,
                color=self.current_color,
                zone_type=zone_type,
                description=description,
                created_at=datetime.now().isoformat(),
                is_active=True
            )

            # Add to zones list
            self.zones.append(zone)
            self.zone_counter += 1

            # Add to plot
            self.add_zone_to_plot(zone)

            # Update zone list
            self.update_zone_list()

            # Clear inputs for next zone
            self.clear_inputs()

            logger.info(f"Created zone: {zone.name} ({zone.bottom:.2f} - {zone.top:.2f})")

        except Exception as e:
            logger.error(f"Error creating zone: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create zone: {str(e)}")

    def create_quick_zone(self, zone_type: str):
        """Create a quick zone around current price."""
        try:
            price = self.current_price
            margin = price * 0.02  # 2% margin

            if zone_type == "support":
                top = price
                bottom = price - margin
                color = "#4caf50"  # Green
                name = f"Support {price:.2f}"
            else:  # resistance
                top = price + margin
                bottom = price
                color = "#f44336"  # Red
                name = f"Resistance {price:.2f}"

            zone = Zone(
                id=self.zone_counter,
                name=name,
                top=top,
                bottom=bottom,
                color=color,
                zone_type=zone_type.title(),
                description=f"Quick {zone_type} zone around {price:.2f}",
                created_at=datetime.now().isoformat(),
                is_active=True
            )

            self.zones.append(zone)
            self.zone_counter += 1

            self.add_zone_to_plot(zone)
            self.update_zone_list()

            logger.info(f"Created quick {zone_type} zone: {zone.name}")

        except Exception as e:
            logger.error(f"Error creating quick zone: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create quick zone: {str(e)}")

    def add_zone_to_plot(self, zone: Zone):
        """Add a zone to the plot visualization."""
        try:
            zone_item = ZoneItem(zone, self.plot_widget)

            # Add rectangle and text to plot
            self.plot_widget.addItem(zone_item.rect_item)
            self.plot_widget.addItem(zone_item.text_item)

            # Store reference
            self.zone_items[zone.id] = zone_item

        except Exception as e:
            logger.error(f"Error adding zone to plot: {str(e)}")

    def update_zone_list(self):
        """Update the zone list widget."""
        self.zone_list.clear()
        for zone in self.zones:
            if zone.is_active:
                item_text = f"{zone.name} ({zone.bottom:.2f} - {zone.top:.2f}) [{zone.zone_type}]"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, zone.id)

                # Set item color based on zone color
                color = QColor(zone.color)
                item.setForeground(color)

                self.zone_list.addItem(item)

    def clear_inputs(self):
        """Clear input fields for next zone creation."""
        self.zone_name_input.clear()
        self.zone_description.clear()
        # Keep the price values for convenience

    def select_zone(self, item):
        """Handle zone selection from list."""
        zone_id = item.data(Qt.ItemDataRole.UserRole)
        zone = next((z for z in self.zones if z.id == zone_id), None)
        if zone:
            # Populate inputs with selected zone data
            self.zone_name_input.setText(zone.name)
            self.zone_top_input.setValue(zone.top)
            self.zone_bottom_input.setValue(zone.bottom)
            self.zone_type_combo.setCurrentText(zone.zone_type)
            self.zone_description.setPlainText(zone.description)
            self.current_color = zone.color
            self.color_button.setStyleSheet(
                f"background-color: {self.current_color}; border: 1px solid #555;"
            )

    def edit_selected_zone(self):
        """Edit the currently selected zone."""
        current_item = self.zone_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a zone to edit.")
            return

        zone_id = current_item.data(Qt.ItemDataRole.UserRole)
        zone = next((z for z in self.zones if z.id == zone_id), None)
        if not zone:
            return

        try:
            # Update zone with current input values
            zone.name = self.zone_name_input.text() or zone.name
            zone.top = self.zone_top_input.value()
            zone.bottom = self.zone_bottom_input.value()
            zone.zone_type = self.zone_type_combo.currentText()
            zone.description = self.zone_description.toPlainText()
            zone.color = self.current_color

            # Validate
            if zone.top <= zone.bottom:
                QMessageBox.warning(self, "Invalid Zone", "Zone top must be higher than bottom!")
                return

            # Remove old visualization
            if zone.id in self.zone_items:
                old_item = self.zone_items[zone.id]
                self.plot_widget.removeItem(old_item.rect_item)
                self.plot_widget.removeItem(old_item.text_item)
                del self.zone_items[zone.id]

            # Add updated visualization
            self.add_zone_to_plot(zone)

            # Update list
            self.update_zone_list()

            logger.info(f"Updated zone: {zone.name}")
            QMessageBox.information(self, "Success", f"Zone '{zone.name}' updated successfully!")

        except Exception as e:
            logger.error(f"Error editing zone: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to edit zone: {str(e)}")

    def delete_selected_zone(self):
        """Delete the currently selected zone."""
        current_item = self.zone_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a zone to delete.")
            return

        zone_id = current_item.data(Qt.ItemDataRole.UserRole)
        zone = next((z for z in self.zones if z.id == zone_id), None)
        if not zone:
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, "Confirm Deletion",
            f"Are you sure you want to delete zone '{zone.name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # Remove from plot
                if zone.id in self.zone_items:
                    zone_item = self.zone_items[zone.id]
                    self.plot_widget.removeItem(zone_item.rect_item)
                    self.plot_widget.removeItem(zone_item.text_item)
                    del self.zone_items[zone.id]

                # Mark as inactive (soft delete)
                zone.is_active = False

                # Update list
                self.update_zone_list()

                # Clear inputs
                self.clear_inputs()

                logger.info(f"Deleted zone: {zone.name}")

            except Exception as e:
                logger.error(f"Error deleting zone: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to delete zone: {str(e)}")

    def save_zones(self):
        """Save zones to a JSON file."""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Save Zones", "zones.json", "JSON Files (*.json)"
            )
            if filename:
                zones_data = [asdict(zone) for zone in self.zones if zone.is_active]
                with open(filename, 'w') as f:
                    json.dump(zones_data, f, indent=2)

                logger.info(f"Saved {len(zones_data)} zones to {filename}")
                QMessageBox.information(self, "Success", f"Zones saved to {filename}")

        except Exception as e:
            logger.error(f"Error saving zones: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save zones: {str(e)}")

    def load_zones(self):
        """Load zones from a JSON file."""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "Load Zones", "", "JSON Files (*.json)"
            )
            if filename:
                with open(filename, 'r') as f:
                    zones_data = json.load(f)

                # Clear existing zones
                self.clear_all_zones(confirm=False)

                # Load zones
                for zone_data in zones_data:
                    zone = Zone(**zone_data)
                    # Update ID to avoid conflicts
                    zone.id = self.zone_counter
                    self.zone_counter += 1

                    self.zones.append(zone)
                    self.add_zone_to_plot(zone)

                self.update_zone_list()

                logger.info(f"Loaded {len(zones_data)} zones from {filename}")
                QMessageBox.information(self, "Success", f"Loaded {len(zones_data)} zones from {filename}")

        except Exception as e:
            logger.error(f"Error loading zones: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to load zones: {str(e)}")

    def clear_all_zones(self, confirm=True):
        """Clear all zones."""
        if confirm:
            reply = QMessageBox.question(
                self, "Confirm Clear",
                "Are you sure you want to clear all zones?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

        try:
            # Remove all zones from plot
            for zone_item in self.zone_items.values():
                self.plot_widget.removeItem(zone_item.rect_item)
                self.plot_widget.removeItem(zone_item.text_item)

            # Clear data
            self.zones.clear()
            self.zone_items.clear()
            self.zone_counter = 1

            # Update UI
            self.update_zone_list()
            self.clear_inputs()

            logger.info("Cleared all zones")

        except Exception as e:
            logger.error(f"Error clearing zones: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to clear zones: {str(e)}")

def main():
    """Main application entry point."""
    app = QApplication(sys.argv)
    app.setApplicationName("Zone Creator")
    app.setApplicationVersion("1.0")

    # Set application icon (if available)
    try:
        app.setWindowIcon(QtGui.QIcon("icon.png"))
    except:
        pass

    # Create and show main window
    window = ZoneCreatorWidget()
    window.show()

    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
