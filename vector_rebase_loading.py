"""
Vector Rebase Chart Loading Integration

This module provides implementations for integrating loading screens with VectorRebase<PERSON>hart.
It combines functionality from:
- vector_rebase_chart_loading_example.py
- vector_rebase_chart_loading_example_updated.py
- vector_rebase_chart_loading_example_compact.py
- vector_rebase_chart_with_external_loading.py
"""

from PyQt6 import QtWidgets, QtCore
import yfinance as yf
import pandas as pd

# Import loading screen options - user can choose which one to use
from loading_screen import LoadingScreen, LoadingScreenManager

class VectorRebaseChart(QtWidgets.QWidget):
    def __init__(self, use_external_loading=False, use_compact_loading=False):
        """
        Initialize the VectorRebaseChart with loading screen integration.

        Args:
            use_external_loading: Whether to use the external loading screen
            use_compact_loading: Whether to use the compact loading screen with data-driven icon
        """
        super().__init__()
        self.chart_colors = {
            'background': '#1e1e1e',
            'bullish': '#4CAF50',  # Material Design Green
            'bearish': '#F44336',  # Material Design Red
            'vector': '#9C27B0',   # Material Design Purple (changed from blue)
            'pivot': '#FFC107',    # Material Design Amber
            'text': '#E0E0E0',     # Light gray text
            'primary_accent': '#007acc'  # Added for the loading screen
            # ... other colors ...
        }

        # Initialize UI components
        # ... existing code ...

        # Set up loading screen based on options
        if use_external_loading:
            # Create loading screen manager for external loading with background mode enabled
            self.loading_manager = LoadingScreenManager(use_background_mode=True)
            self.use_external_loading = True
        else:
            # Create embedded loading screen
            self.loading_screen = LoadingScreen(self, "Loading chart data...", self.chart_colors,
                                               use_data_driven_icon=use_compact_loading)
            self.use_external_loading = False

    def fetch_data(self):
        """Fetch data for the chart with loading screen."""
        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            QtWidgets.QMessageBox.warning(self, "Error", "Please enter a symbol")
            self.update_timer.stop()
            self.iv_update_timer.stop()
            return

        try:
            # Show loading screen based on the selected option
            if self.use_external_loading:
                # Get chart widget position and size for the loading screen
                chart_rect = self.plot_widget.geometry()
                global_pos = self.plot_widget.mapToGlobal(QtCore.QPoint(0, 0))
                position = (global_pos.x(), global_pos.y())
                size = (chart_rect.width(), chart_rect.height())

                # Start the external loading screen
                self.loading_manager.start(
                    message=f"Loading data for {symbol}...",
                    position=position,
                    size=size
                )
            else:
                # Show embedded loading screen
                self.loading_screen.set_message(f"Loading data for {symbol}...")
                self.loading_screen.show()

            # Apply any pending preset before fetching data
            if hasattr(self, 'pending_preset') and self.pending_preset:
                preset_text = self.pending_preset
                print(f"Applying pending preset: {preset_text}")

                # Apply the appropriate preset settings
                if preset_text == "W10/15m":
                    self.apply_w10_15m_settings()
                elif preset_text == "W7/60m":
                    self.apply_w7_60m_settings()
                elif preset_text == "W7/Daily":
                    self.apply_w7_daily_settings()
                # ... other presets ...

                # Clear the pending preset
                self.pending_preset = None

            # Set wait cursor
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

            # Use data dispatcher instead of direct yfinance call
            from data_dispatcher import DataFetchThread

            days = self.days_spin.value()
            interval = self.timeframe_combo.currentText()

            # Update loading message
            if self.use_external_loading:
                self.loading_manager.update_message(f"Fetching {interval} data for {symbol}...")
            else:
                self.loading_screen.set_message(f"Fetching {interval} data for {symbol}...")

            # Create a data fetch thread to get Yahoo Finance data
            fetch_thread = DataFetchThread(symbol, interval, days, 'yfinance')
            self.data = fetch_thread._fetch_yfinance_data()

            if self.data.empty:
                from dialog_manager import warning
                warning(self, "Error", "No data returned for symbol")
                QtWidgets.QApplication.restoreOverrideCursor()

                # Hide loading screen
                if self.use_external_loading:
                    self.loading_manager.stop()
                else:
                    self.loading_screen.hide()
                return

            if self.data.index.tz is not None:
                self.data.index = self.data.index.tz_localize(None)

            # Update loading message
            if self.use_external_loading:
                self.loading_manager.update_message(f"Processing data for {symbol}...")
            else:
                self.loading_screen.set_message(f"Processing data for {symbol}...")

            # Process data
            # ... existing data processing code ...

            # Update loading message
            if self.use_external_loading:
                self.loading_manager.update_message(f"Plotting chart for {symbol}...")
            else:
                self.loading_screen.set_message(f"Plotting chart for {symbol}...")

            # Plot data
            self.plot_data()

            # Start update timer
            self.update_timer.start()

            # Restore cursor
            QtWidgets.QApplication.restoreOverrideCursor()

            # Hide loading screen
            if self.use_external_loading:
                self.loading_manager.stop()
            else:
                self.loading_screen.hide()

        except Exception as e:
            # Handle errors
            from dialog_manager import critical
            critical(self, "Error", f"Error fetching data: {str(e)}")
            QtWidgets.QApplication.restoreOverrideCursor()

            # Hide loading screen on error
            if self.use_external_loading:
                self.loading_manager.stop()
            else:
                self.loading_screen.hide()

    def update_data(self):
        """Update data periodically."""
        if not self.symbol_input.text().strip():
            return

        try:
            # For updates, we don't show the loading screen to avoid flickering
            # during regular updates, but we could show it for longer operations

            symbol = self.symbol_input.text().strip().upper()

            # Use data dispatcher instead of direct yfinance call
            from data_dispatcher import DataFetchThread

            interval = self.timeframe_combo.currentText()

            # Create a data fetch thread to get Yahoo Finance data for updates
            fetch_thread = DataFetchThread(symbol, interval, 1, 'yfinance')  # 1 day for updates
            new_data = fetch_thread._fetch_yfinance_data()

            if new_data.empty:
                return

            if new_data.index.tz is not None:
                new_data.index = new_data.index.tz_localize(None)

            if len(new_data) > 0 and (self.data is None or new_data.index[-1] > self.data.index[-1]):
                if self.data is not None:
                    combined = pd.concat([self.data, new_data])
                    self.data = combined[~combined.index.duplicated(keep='last')]
                else:
                    self.data = new_data

                self.plot_data()

        except Exception as e:
            print(f"Error updating data: {str(e)}")
            self.update_timer.stop()

    # Other methods remain unchanged
