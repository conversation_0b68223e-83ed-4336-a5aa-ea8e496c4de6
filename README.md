# Market Odds and Options Analyzer

A comprehensive machine learning-based market analysis tool for financial trading, featuring advanced signal processing, adaptive learning, and options analysis.

## Overview

The Market Odds and Options Analyzer is a sophisticated financial analysis application that combines traditional technical analysis with cutting-edge machine learning techniques. It helps traders identify potential market reversals and pullbacks with high accuracy by analyzing price patterns, vector crossings, and various technical indicators.

The application features an adaptive learning system that continuously improves its predictions by monitoring market conditions and detecting concept drift (when market behavior changes significantly). This ensures that the model stays accurate even as markets evolve over time.

## Key Features

### Market Odds Analysis
- **Vector-Based Signal Detection**: Identifies potential market turning points using enhanced vector calculations
- **Machine Learning Classification**: Distinguishes between pullbacks and reversals with high accuracy
- **Adaptive Learning**: Continuously improves predictions by adapting to changing market conditions
- **Concept Drift Detection**: Automatically detects when market behavior changes significantly
- **Signal Visualization**: Clear visual indicators for pullbacks and reversals on price charts

### Advanced Technical Analysis
- **Candlestick Charting**: Traditional candlestick charts with customizable appearance
- **Volume Profile Analysis**: Visualize volume distribution at different price levels
- **Volatility Analysis**: Comprehensive volatility metrics and visualization
- **Wavelet Denoising**: Reduce noise in price data for clearer signal detection

### Options Analysis
- **Options Strategy Evaluation**: Analyze various options strategies
- **Risk/Reward Visualization**: Visualize potential outcomes of options positions

### Machine Learning Capabilities
- **Multiple Model Types**: Support for Random Forest, Gradient Boosting, XGBoost, LightGBM, and neural network models
- **Feature Importance Analysis**: Identify which market factors have the most impact on predictions
- **Probability Calibration**: Ensure prediction probabilities accurately reflect real-world outcomes
- **Cross-Validation**: Rigorous validation to ensure model reliability

### Online Learning System
- **Parameter Optimization**: Automatically tune parameters for optimal performance
- **Performance Tracking**: Monitor model accuracy, precision, recall, and F1 score
- **Comprehensive Dashboard**: Visualize model performance and parameter evolution

## Technical Architecture

The application is built with a modular architecture that separates concerns and allows for easy extension:

### Core Components
- **Signal Processor**: Encapsulates all signal filtering and processing logic
- **Crossing Classifier**: Machine learning model for classifying vector crossings
- **Parameter Registry**: Centralized registry for all tunable parameters
- **Online Parameter Optimizer**: Automatically optimizes parameters based on performance
- **Adaptive Learning System**: Monitors model performance and adapts to changing market conditions
- **Feature Engineering**: Extracts relevant features from price data for machine learning

### Neural Network Models
- **GRU-like and LSTM-like Models**: Sequence-based models for temporal pattern recognition
- **Implemented using scikit-learn**: No TensorFlow dependency required

### User Interface
- **Multiple Tabs**: Organized interface with specialized tabs for different analysis types
- **Interactive Charts**: Real-time visualization of price data and signals
- **Customizable Appearance**: Adjust colors, indicators, and other visual elements

## Dependencies

The application requires the following Python libraries:
- PyQt6: For the graphical user interface
- NumPy & Pandas: For data manipulation
- scikit-learn: For machine learning models
- pyqtgraph: For interactive charts
- yfinance: For fetching financial data
- XGBoost & LightGBM: For advanced gradient boosting models
- PyWavelets: For wavelet-based signal processing
- matplotlib: For additional visualization
- joblib: For model serialization

## Getting Started

1. Ensure you have Python 3.8+ installed
2. Install the required dependencies:
   ```
   pip install numpy pandas scikit-learn pyqt6 pyqtgraph yfinance xgboost lightgbm pywavelets matplotlib joblib
   ```
3. Run the application:
   ```
   python MAIN.py
   ```

## Usage Guide

### Market Odds Tab
1. **Load Data**: Enter a ticker symbol and select a timeframe
2. **Generate Vector**: Calculate the enhanced vector for signal detection
3. **Identify Crossings**: The system will automatically identify vector crossings
4. **Train Classifier**: Train the machine learning model to predict pullbacks vs. reversals
5. **Analyze Signals**: Review the signals and their predicted outcomes

### Adaptive Learning Tab
1. **Monitor Performance**: Track the model's accuracy, precision, recall, and F1 score
2. **Check for Drift**: The system will alert you when market conditions change significantly
3. **Review Recommendations**: Get actionable recommendations based on current model performance
4. **View Performance History**: See how model performance has changed over time

### Online Learning Tab
1. **Dashboard**: View the current status of the online learning system
2. **Parameters**: Monitor and adjust parameter values that are being optimized
3. **Performance**: Track performance metrics over time with detailed charts
4. **Drift Detection**: Visualize concept drift detection and its impact on performance
5. **Settings**: Configure the online learning system's behavior
6. **Fetch Data**: Use the 'Fetch Data' button to load sample data for testing the system
7. **Parameter Optimization**: Monitor how parameters are being optimized over time
8. **Performance Tracking**: View detailed performance metrics for different parameter sets
9. **Exploration Control**: Adjust the exploration factor to balance exploration vs. exploitation
10. **Strategy Selection**: Choose between balanced, aggressive, or conservative optimization strategies

### Volatility Graph Tab
1. **Generate Graph**: Create a volatility tree visualization based on market data
2. **Analyze Patterns**: Identify volatility regimes and potential market turning points

## Detailed Component Definitions

### Volatility Graph Tab
The Volatility Graph Tab provides a sophisticated visualization of market volatility patterns and structures, helping traders identify potential market turning points and regime changes.

#### Key Components
- **Volatility Tree**: A hierarchical visualization that shows how volatility evolves across different market conditions
- **Volatility Metrics**:
  - **Historical Volatility**: Measures price fluctuations over specific time periods
  - **Relative Volatility**: Compares current volatility to historical norms
  - **Volatility Regimes**: Identifies distinct market phases based on volatility characteristics
  - **Volatility Skew**: Measures asymmetry in volatility distribution
  - **Volatility Clustering**: Identifies periods where high/low volatility tends to persist
- **Cycle Analysis**:
  - **Bullish Cycles**: Sequences of price movements above baseline
  - **Bearish Cycles**: Sequences of price movements below baseline
  - **Cycle Duration**: Measurement of typical cycle length
  - **Cycle Amplitude**: Measurement of typical cycle magnitude
- **Market Odds Calculation**:
  - **Directional Probability**: Likelihood of upward vs. downward movement
  - **Magnitude Probability**: Expected size of the next market move
  - **Time Horizon Analysis**: How probabilities change across different timeframes

#### Visualization Features
- **Interactive Tree Display**: Zoom, pan, and click on nodes for detailed information
- **Color Coding**: Different colors represent bullish/bearish conditions and volatility intensity
- **Node Size**: Represents the significance or frequency of specific volatility patterns
- **Connections**: Show relationships between different volatility states

### Online Learning Tab
The Online Learning Tab provides a comprehensive interface for monitoring and controlling the system's parameter optimization and learning processes.

#### What the Online Learning Tab Does

The online learning tab is a specialized interface that focuses on automatically optimizing the parameters of the trading system based on real-time performance feedback. It's different from the adaptive learning tab, which focuses on adapting the machine learning models themselves.

- **Parameter Optimization**: The online learning tab continuously monitors the performance of trading signals and automatically adjusts various parameters to improve future results.

- **Performance Tracking**: It tracks key performance metrics like accuracy, precision, recall, and F1 score to evaluate how well the current parameter settings are performing.

- **Exploration vs. Exploitation**: The tab manages the balance between exploring new parameter values (trying new settings) and exploiting known good values (sticking with what works).

- **Drift Detection**: Similar to the adaptive learning tab, it monitors for concept drift - when market conditions change significantly - but focuses on how these changes should affect parameter settings rather than model structure.

#### How It Differs from Adaptive Learning

While both tabs deal with adapting to changing market conditions, they focus on different aspects:

1. **Online Learning Tab**: Focuses on optimizing the parameters of the trading system (thresholds, weights, etc.) without changing the underlying models or algorithms.

2. **Adaptive Learning Tab**: Focuses on adapting the machine learning models themselves to changing market conditions, including retraining or incrementally updating the models.

#### Parameter Registry System
- **Parameter Categories**:
  - **RSI Parameters**: Period, overbought/oversold thresholds
  - **Confirmation Parameters**: Confirmation period, minimum magnitude
  - **Classifier Parameters**: Lookback/lookahead windows, reversal threshold
  - **Confidence Parameters**: Minimum confidence threshold
  - **Adaptive Learning Parameters**: Drift threshold, learning rate
  - **Component Weights**: Relative importance of different signal components
- **Parameter Properties**:
  - **Value Range**: Minimum and maximum allowed values
  - **Step Size**: Granularity of parameter adjustments
  - **Learning Rate**: How quickly parameters adapt to new information
  - **Exploration Noise**: Random variation to discover optimal settings

#### Optimization System
- **Optimization Strategies**:
  - **Balanced**: Equal focus on exploration and exploitation
  - **Aggressive**: Faster adaptation to recent performance
  - **Conservative**: More emphasis on historical performance
  - **Exploratory**: Greater parameter space exploration
- **Performance Metrics**:
  - **Accuracy**: Overall correctness of predictions
  - **Precision**: Accuracy of positive predictions
  - **Recall**: Ability to find all positive instances
  - **F1 Score**: Harmonic mean of precision and recall
  - **Profit Factor**: Ratio of gains to losses

#### Dashboard Elements
- **Parameter Evolution Charts**: Visualize how parameters change over time
- **Performance Tracking**: Monitor how changes affect prediction quality
- **Optimization Controls**: Adjust learning rates and strategies
- **Parameter Tables**: View and manually adjust current parameter values
- **Status Indicators**: Show the current state of the optimization process

#### Benefits for Trading

The online learning tab helps traders by:

1. Automatically fine-tuning system parameters without requiring manual intervention
2. Finding optimal parameter values that might not be obvious to human traders
3. Continuously adapting to changing market conditions by adjusting parameters
4. Providing transparency into which parameters are most important for performance
5. Allowing different optimization strategies based on risk tolerance (conservative vs. aggressive)

This feature is particularly valuable because finding the optimal parameters for a trading system is typically a time-consuming process that requires extensive backtesting. The online learning tab automates this process and continuously refines parameters based on real-world performance.

### Adaptive Learning Tab
The Adaptive Learning Tab provides tools for monitoring model health, detecting concept drift, and ensuring the system adapts to changing market conditions.

#### What the Adaptive Learning Tab Does

The adaptive learning tab is a specialized interface that monitors and manages the application's machine learning models that adapt to changing market conditions. Here's a detailed explanation of its functionality:

- **Continuous Model Improvement**: The adaptive learning system continuously updates the machine learning models based on new market data, allowing the models to adapt to changing market conditions without requiring complete retraining.

- **Concept Drift Detection**: The tab monitors for "concept drift" - when the statistical properties of the target variable change over time, indicating that market conditions have evolved and the model may need to adapt.

- **Performance Monitoring**: It tracks key performance metrics like accuracy, precision, recall, and F1 score to evaluate how well the models are performing over time.

- **Parameter Optimization**: The system can automatically adjust model parameters based on recent performance to maintain optimal prediction quality.

#### Adaptive Learning System
- **Performance Monitoring**:
  - **Accuracy History**: Tracks prediction accuracy over time
  - **Precision History**: Tracks precision metrics over time
  - **Recall History**: Tracks recall metrics over time
  - **F1 History**: Tracks F1 score over time
  - **Confidence History**: Tracks prediction confidence over time
  - **Robustness History**: Tracks model stability over time
- **Drift Detection**:
  - **Baseline Accuracy**: Reference point for normal performance
  - **Accuracy Drop**: Measures deviation from baseline
  - **Drift Threshold**: Sensitivity setting for drift detection
  - **Drift Count**: Number of detected drift events
  - **Drift Timestamps**: When drift events occurred

#### Adaptation Mechanisms
- **Model Updates**:
  - **Learning Rate Adjustment**: Increases during drift, decreases during stability
  - **Sample Weighting**: Gives more importance to recent data after drift
  - **Model Snapshots**: Saves model state at key points for comparison
  - **Adaptation Count**: Tracks how often the model has adapted
- **Synchronization**:
  - **Classifier Synchronization**: Ensures consistency with main classifier
  - **Feature Synchronization**: Maintains consistent feature extraction

#### Dashboard Elements
- **Model Status Display**: Shows current performance metrics
- **Drift Alerts**: Visual indicators when drift is detected
- **Performance Charts**: Visualize accuracy, precision, recall, and F1 score over time
- **Recommendations**: Actionable suggestions based on current model state
- **Data Summary**: Overview of the training data being used
- **Drift Information**: Details about detected drift events and adaptation actions

#### Benefits for Trading
The adaptive learning tab helps traders by:
1. Automatically adjusting to changing market conditions
2. Providing early warnings when market behavior changes significantly
3. Continuously improving prediction accuracy without manual intervention
4. Offering recommendations for strategy adjustments based on recent performance
5. Maintaining model quality over time even as markets evolve

This feature is particularly valuable in trading applications because financial markets are non-stationary systems that constantly change, requiring models that can adapt accordingly.

## Advanced Features

### Concept Drift Detection
The system continuously monitors prediction accuracy and statistical properties of recent data. When significant deviations are detected, it flags a potential drift event and takes adaptive measures to update the model accordingly.

### Adaptation Strategies
- Adjusting learning rates
- Reweighting recent vs. historical data
- Updating feature importance
- Modifying model parameters

### Parameter Registry
All tunable parameters are managed through a centralized registry, allowing for:
- Comprehensive tracking of parameter changes
- Automatic optimization based on performance feedback
- Persistence of optimized parameters between sessions

### Feature Engineering
The system extracts a rich set of features from price data, including:
- Price change metrics
- Volatility indicators
- Volume analysis
- Technical indicators (RSI, MACD, Stochastics, etc.)
- Market regime detection

## Contributing

Contributions to the Market Odds and Options Analyzer are welcome! Whether you're fixing bugs, adding features, or improving documentation, your help is appreciated.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
