"""
Signal Display Component for Fractal Tester

This module provides an enhanced visual display for trading signals with modern UI elements,
animations, and improved readability.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg

class EnhancedSignalDisplay(QtWidgets.QWidget):
    """
    Enhanced signal display component with modern UI and animations.

    This class provides a modern, visually appealing display for trading signals
    with smooth animations, visual feedback, and improved readability.
    """

    def __init__(self, parent=None):
        """
        Initialize the enhanced signal display.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Signal display colors with Material Design palette
        self.signal_colors = {
            'strong': '#4CAF50',  # Material Design Green
            'medium': '#FFC107',  # Material Design Amber
            'weak': '#FF9800',    # Material Design Orange
            'blocked': '#F44336', # Material Design Red
            'background': '#121620',  # Midnight Ocean background
            'text': '#FFFFFF',    # White text
            'border': '#333333',  # Dark border
            'highlight': '#2196F3' # Material Design Blue for highlights
        }

        # Signal icons for visual enhancement
        self.signal_icons = {
            'strong': '✓',  # Checkmark for strong signals
            'medium': '◉',  # Filled circle for medium signals
            'weak': '○',    # Empty circle for weak signals
            'blocked': '✗'  # X for blocked signals
        }

        # Initialize UI
        self.init_ui()

        # Animation properties
        self.animation_duration = 300  # milliseconds
        self.animation_steps = 10
        self.current_animation = None

    def init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QtWidgets.QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

        # Signal container with rounded corners and shadow
        self.signal_container = QtWidgets.QFrame()
        self.signal_container.setObjectName("signalContainer")
        self.signal_container.setStyleSheet(f"""
            #signalContainer {{
                background-color: {self.signal_colors['background']};
                border-radius: 8px;
                border: 1px solid {self.signal_colors['border']};
            }}
        """)

        # Add shadow effect
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QtGui.QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        self.signal_container.setGraphicsEffect(shadow)

        # Container layout
        container_layout = QtWidgets.QVBoxLayout(self.signal_container)
        container_layout.setContentsMargins(10, 10, 10, 10)
        container_layout.setSpacing(8)

        # Signal header (icon + type)
        self.header_layout = QtWidgets.QHBoxLayout()
        self.signal_icon = QtWidgets.QLabel()
        self.signal_icon.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.header_layout.addWidget(self.signal_icon)

        self.signal_type = QtWidgets.QLabel()
        self.signal_type.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.header_layout.addWidget(self.signal_type)
        self.header_layout.addStretch()

        # Signal direction indicator
        self.direction_indicator = QtWidgets.QLabel()
        self.direction_indicator.setStyleSheet("font-size: 14px;")
        self.header_layout.addWidget(self.direction_indicator)

        container_layout.addLayout(self.header_layout)

        # Separator line
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator.setStyleSheet(f"background-color: {self.signal_colors['border']};")
        container_layout.addWidget(separator)

        # Signal details
        self.details_layout = QtWidgets.QVBoxLayout()

        # Message
        self.message_label = QtWidgets.QLabel()
        self.message_label.setWordWrap(True)
        self.message_label.setStyleSheet("font-size: 12px;")
        self.details_layout.addWidget(self.message_label)

        # Confidence bar (for potential signals)
        self.confidence_layout = QtWidgets.QHBoxLayout()
        self.confidence_label = QtWidgets.QLabel("Confidence:")
        self.confidence_label.setStyleSheet("font-size: 11px;")
        self.confidence_layout.addWidget(self.confidence_label)

        self.confidence_bar = QtWidgets.QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setTextVisible(True)
        self.confidence_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555;
                border-radius: 4px;
                text-align: center;
                background-color: #2d2d2d;
                height: 12px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                                stop:0 #FF9800, stop:1 #4CAF50);
                border-radius: 3px;
            }
        """)
        self.confidence_layout.addWidget(self.confidence_bar)
        self.details_layout.addLayout(self.confidence_layout)

        # Additional metrics
        self.metrics_layout = QtWidgets.QGridLayout()
        self.metrics_layout.setColumnStretch(1, 1)

        # Magnitude
        self.magnitude_label = QtWidgets.QLabel("Magnitude:")
        self.magnitude_label.setStyleSheet("font-size: 11px;")
        self.magnitude_value = QtWidgets.QLabel()
        self.magnitude_value.setStyleSheet("font-size: 11px; font-weight: bold;")
        self.metrics_layout.addWidget(self.magnitude_label, 0, 0)
        self.metrics_layout.addWidget(self.magnitude_value, 0, 1)

        # RSI
        self.rsi_label = QtWidgets.QLabel("RSI:")
        self.rsi_label.setStyleSheet("font-size: 11px;")
        self.rsi_value = QtWidgets.QLabel()
        self.rsi_value.setStyleSheet("font-size: 11px; font-weight: bold;")
        self.metrics_layout.addWidget(self.rsi_label, 1, 0)
        self.metrics_layout.addWidget(self.rsi_value, 1, 1)

        # Confirmation
        self.confirmation_label = QtWidgets.QLabel("Confirmation:")
        self.confirmation_label.setStyleSheet("font-size: 11px;")
        self.confirmation_value = QtWidgets.QLabel()
        self.confirmation_value.setStyleSheet("font-size: 11px; font-weight: bold;")
        self.metrics_layout.addWidget(self.confirmation_label, 0, 2)
        self.metrics_layout.addWidget(self.confirmation_value, 0, 3)

        # Volatility
        self.volatility_label = QtWidgets.QLabel("Volatility:")
        self.volatility_label.setStyleSheet("font-size: 11px;")
        self.volatility_value = QtWidgets.QLabel()
        self.volatility_value.setStyleSheet("font-size: 11px; font-weight: bold;")
        self.metrics_layout.addWidget(self.volatility_label, 1, 2)
        self.metrics_layout.addWidget(self.volatility_value, 1, 3)

        self.details_layout.addLayout(self.metrics_layout)
        container_layout.addLayout(self.details_layout)

        # Add container to main layout
        self.layout.addWidget(self.signal_container)

        # Initially hide confidence and metrics
        self.confidence_bar.setVisible(False)
        self.confidence_label.setVisible(False)
        self.hide_metrics()

    def hide_metrics(self):
        """Hide all metric labels."""
        self.magnitude_label.setVisible(False)
        self.magnitude_value.setVisible(False)
        self.rsi_label.setVisible(False)
        self.rsi_value.setVisible(False)
        self.confirmation_label.setVisible(False)
        self.confirmation_value.setVisible(False)
        self.volatility_label.setVisible(False)
        self.volatility_value.setVisible(False)

    def show_metrics(self, metrics):
        """
        Show metrics with provided values.

        Args:
            metrics: Dictionary of metric values to display
        """
        # Show/hide magnitude
        if 'magnitude' in metrics:
            self.magnitude_label.setVisible(True)
            self.magnitude_value.setVisible(True)
            self.magnitude_value.setText(f"{metrics['magnitude']:.2f}%")
        else:
            self.magnitude_label.setVisible(False)
            self.magnitude_value.setVisible(False)

        # Show/hide RSI
        if 'rsi' in metrics:
            self.rsi_label.setVisible(True)
            self.rsi_value.setVisible(True)
            self.rsi_value.setText(f"{metrics['rsi']:.1f}")
        else:
            self.rsi_label.setVisible(False)
            self.rsi_value.setVisible(False)

        # Show/hide confirmation
        if 'confirmation' in metrics and 'required' in metrics:
            self.confirmation_label.setVisible(True)
            self.confirmation_value.setVisible(True)
            self.confirmation_value.setText(f"{metrics['confirmation']}/{metrics['required']}")
        else:
            self.confirmation_label.setVisible(False)
            self.confirmation_value.setVisible(False)

        # Show/hide volatility
        if 'volatility' in metrics:
            self.volatility_label.setVisible(True)
            self.volatility_value.setVisible(True)
            self.volatility_value.setText(f"{metrics['volatility']:.1f}%")
        else:
            self.volatility_label.setVisible(False)
            self.volatility_value.setVisible(False)

    def update_signal(self, signal_data):
        """
        Update the signal display with new signal data.

        Args:
            signal_data: Dictionary containing signal information
        """
        # Extract signal properties
        signal_type = signal_data.get('status', 'unknown')
        direction = signal_data.get('direction', '')
        message = signal_data.get('message', '')
        color = signal_data.get('color', '#FFFFFF')
        confidence = signal_data.get('confidence', 0)
        strength = signal_data.get('strength', 'weak')

        # Get components/metrics if available
        components = signal_data.get('components', {})

        # Determine icon based on signal type and strength
        if signal_type == 'confirmed':
            icon = self.signal_icons.get(strength, '•')
        elif signal_type == 'potential':
            icon = self.signal_icons['weak']
        elif signal_type == 'blocked' or signal_type == 'rejected':
            icon = self.signal_icons['blocked']
        else:
            icon = '•'

        # Update header
        self.signal_icon.setText(icon)
        self.signal_icon.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {color};")

        # Format signal type text
        if signal_type == 'confirmed':
            type_text = f"CONFIRMED {strength.upper()}"
        elif signal_type == 'potential':
            type_text = "POTENTIAL"
        elif signal_type == 'blocked':
            type_text = "BLOCKED"
        elif signal_type == 'rejected':
            type_text = "REJECTED"
        else:
            type_text = signal_type.upper()

        self.signal_type.setText(type_text)
        self.signal_type.setStyleSheet(f"font-size: 14px; font-weight: bold; color: {color};")

        # Update direction indicator
        if direction == 'up':
            self.direction_indicator.setText("▲ UP")
            self.direction_indicator.setStyleSheet("font-size: 14px; color: #4CAF50;")
        elif direction == 'down':
            self.direction_indicator.setText("▼ DOWN")
            self.direction_indicator.setStyleSheet("font-size: 14px; color: #F44336;")
        else:
            self.direction_indicator.setText("")

        # Update message
        self.message_label.setText(message)

        # Update confidence bar for potential signals
        if signal_type == 'potential' and confidence > 0:
            self.confidence_bar.setVisible(True)
            self.confidence_label.setVisible(True)
            self.confidence_bar.setValue(int(confidence))

            # Set color based on confidence level
            if confidence >= 75:
                bar_style = """
                    QProgressBar::chunk {
                        background-color: #4CAF50;
                        border-radius: 3px;
                    }
                """
            elif confidence >= 50:
                bar_style = """
                    QProgressBar::chunk {
                        background-color: #FFC107;
                        border-radius: 3px;
                    }
                """
            else:
                bar_style = """
                    QProgressBar::chunk {
                        background-color: #FF9800;
                        border-radius: 3px;
                    }
                """
            self.confidence_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #555;
                    border-radius: 4px;
                    text-align: center;
                    background-color: #2d2d2d;
                    height: 12px;
                }
            """ + bar_style)
        else:
            self.confidence_bar.setVisible(False)
            self.confidence_label.setVisible(False)

        # Prepare metrics to display
        metrics = {}

        # Add magnitude if available
        if 'magnitude' in signal_data:
            metrics['magnitude'] = signal_data['magnitude']
        elif hasattr(signal_data, 'details') and 'magnitude' in signal_data.get('details', {}):
            metrics['magnitude'] = signal_data['details']['magnitude']

        # Add RSI if available in components
        if 'rsi' in components:
            metrics['rsi'] = components['rsi'] * 100  # Convert from 0-1 to 0-100
        elif 'rsi' in signal_data:
            metrics['rsi'] = signal_data['rsi']

        # Add confirmation info for potential signals
        if signal_type == 'potential' and 'confirmation' in signal_data:
            metrics['confirmation'] = signal_data['confirmation']
            metrics['required'] = signal_data.get('required', 3)

        # Add volatility if available
        if 'volatility' in components:
            metrics['volatility'] = components['volatility'] * 100  # Convert from 0-1 to 0-100
        elif 'volatility' in signal_data:
            metrics['volatility'] = signal_data['volatility']

        # Update metrics display
        if metrics:
            self.show_metrics(metrics)
        else:
            self.hide_metrics()

        # Apply animation for new signal
        self._animate_signal()

    def update(self):
        """
        Update the signal display with the latest data.
        This method is called when the display needs to be refreshed without new signal data.
        """
        # Apply a subtle animation to indicate the update
        self._animate_signal(is_refresh=True)

    def _animate_signal(self, is_refresh=False):
        """
        Apply fade-in animation to the signal display.

        Args:
            is_refresh: If True, use a more subtle animation for refreshes
        """
        # Stop any current animation
        if self.current_animation is not None:
            self.current_animation.stop()

        # Create opacity effect
        self.opacity_effect = QtWidgets.QGraphicsOpacityEffect(self.signal_container)
        self.signal_container.setGraphicsEffect(self.opacity_effect)

        # Create animation
        self.current_animation = QtCore.QPropertyAnimation(self.opacity_effect, b"opacity")

        if is_refresh:
            # More subtle animation for refreshes
            self.current_animation.setDuration(self.animation_duration // 2)
            self.current_animation.setStartValue(0.8)
        else:
            # Full animation for new signals
            self.current_animation.setDuration(self.animation_duration)
            self.current_animation.setStartValue(0.0)

        self.current_animation.setEndValue(1.0)
        self.current_animation.setEasingCurve(QtCore.QEasingCurve.Type.OutCubic)

        # Start animation
        self.current_animation.start()
