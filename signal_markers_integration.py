"""
Integration module for enhanced signal markers.

This module provides functions to integrate the enhanced signal markers
with the existing codebase.
"""

import numpy as np
from PyQt6 import QtWidgets
import pyqtgraph as pg
import matplotlib.pyplot as plt
from matplotlib.backend_bases import <PERSON><PERSON><PERSON>on
from enhanced_signal_markers import create_signal_markers, SignalPopup

def integrate_with_matplotlib(ax, signal_times, signal_values, signal_types, signal_data_list=None):
    """
    Replace matplotlib scatter plots with enhanced signal markers.

    This function is meant to be used as a drop-in replacement for the existing
    matplotlib scatter plots in adaptive_learning.py.

    Args:
        ax: Matplotlib axes object
        signal_times: List of signal timestamps
        signal_values: List of signal values
        signal_types: List of signal types
        signal_data_list: List of signal data dictionaries (optional)

    Returns:
        Dictionary of matplotlib scatter objects by signal type
    """
    # Create separate lists for each signal type
    bullish_pullback_times = []
    bullish_pullback_values = []
    bullish_pullback_data = []

    bearish_pullback_times = []
    bearish_pullback_values = []
    bearish_pullback_data = []

    bullish_reversal_times = []
    bullish_reversal_values = []
    bullish_reversal_data = []

    bearish_reversal_times = []
    bearish_reversal_values = []
    bearish_reversal_data = []

    # Separate signals by type
    for i, signal_type in enumerate(signal_types):
        # Get signal data if available
        signal_data = None
        if signal_data_list and i < len(signal_data_list):
            signal_data = signal_data_list[i]
        else:
            # Create basic signal data
            signal_data = {
                'type': signal_type,
                'direction': 'up' if 'bullish' in signal_type.lower() else 'down',
                'strength': 'medium',
                'confidence': 75.0
            }

        # Add to appropriate list
        signal_type_lower = signal_type.lower().replace('_', '').replace('-', '')

        if 'bullishpullback' in signal_type_lower:
            bullish_pullback_times.append(signal_times[i])
            bullish_pullback_values.append(signal_values[i])
            bullish_pullback_data.append(signal_data)
        elif 'bearishpullback' in signal_type_lower:
            bearish_pullback_times.append(signal_times[i])
            bearish_pullback_values.append(signal_values[i])
            bearish_pullback_data.append(signal_data)
        elif 'bullishreversal' in signal_type_lower:
            bullish_reversal_times.append(signal_times[i])
            bullish_reversal_values.append(signal_values[i])
            bullish_reversal_data.append(signal_data)
        elif 'bearishreversal' in signal_type_lower:
            bearish_reversal_times.append(signal_times[i])
            bearish_reversal_values.append(signal_values[i])
            bearish_reversal_data.append(signal_data)

    # Create scatter plots with enhanced markers
    scatter_objects = {}

    # Create a dictionary to store signal data by position for click handling
    signal_data_by_position = {}

    # Define styles for different signal types
    signal_styles = {
        'BullishPullback': {
            'color': '#00BCD4',  # Cyan
            'marker': '^',       # Triangle up
            'size': 100,
            'glow_color': '#00BCD4',
            'glow_size': 150,    # More moderate glow size
            'glow_alpha': 0.4    # Less opaque
        },
        'BearishPullback': {
            'color': '#FF5722',  # Deep Orange
            'marker': 'v',       # Triangle down
            'size': 100,
            'glow_color': '#FF5722',
            'glow_size': 150,
            'glow_alpha': 0.4
        },
        'BullishReversal': {
            'color': '#4CAF50',  # Green
            'marker': 'o',       # Circle
            'size': 100,
            'glow_color': '#4CAF50',
            'glow_size': 150,
            'glow_alpha': 0.4
        },
        'BearishReversal': {
            'color': '#F44336',  # Red
            'marker': 'o',       # Circle
            'size': 100,
            'glow_color': '#F44336',
            'glow_size': 150,
            'glow_alpha': 0.4
        }
    }

    # Add bullish pullback markers with enhanced glow
    if bullish_pullback_times:
        style = signal_styles['BullishPullback']

        # Add simple glow effect
        ax.scatter(
            bullish_pullback_times, bullish_pullback_values,
            color=style['glow_color'], marker=style['marker'], s=style['glow_size'],
            alpha=style['glow_alpha'], zorder=19, edgecolor='none'
        )

        # Add main marker
        scatter = ax.scatter(
            bullish_pullback_times, bullish_pullback_values,
            color=style['color'], marker=style['marker'], s=style['size'],
            label='Bullish Pullback', zorder=20, edgecolor='white', linewidth=1,
            picker=True  # Enable picking for click events
        )
        scatter_objects['bullish_pullback'] = scatter

        # Store signal data for click handling
        for i, (x, y) in enumerate(zip(bullish_pullback_times, bullish_pullback_values)):
            if i < len(bullish_pullback_data):
                signal_data_by_position[(x, y)] = bullish_pullback_data[i]

    # Add bearish pullback markers with enhanced glow
    if bearish_pullback_times:
        style = signal_styles['BearishPullback']

        # Add simple glow effect
        ax.scatter(
            bearish_pullback_times, bearish_pullback_values,
            color=style['glow_color'], marker=style['marker'], s=style['glow_size'],
            alpha=style['glow_alpha'], zorder=19, edgecolor='none'
        )

        # Add main marker
        scatter = ax.scatter(
            bearish_pullback_times, bearish_pullback_values,
            color=style['color'], marker=style['marker'], s=style['size'],
            label='Bearish Pullback', zorder=20, edgecolor='white', linewidth=1,
            picker=True  # Enable picking for click events
        )
        scatter_objects['bearish_pullback'] = scatter

        # Store signal data for click handling
        for i, (x, y) in enumerate(zip(bearish_pullback_times, bearish_pullback_values)):
            if i < len(bearish_pullback_data):
                signal_data_by_position[(x, y)] = bearish_pullback_data[i]

    # Add bullish reversal markers with enhanced glow
    if bullish_reversal_times:
        style = signal_styles['BullishReversal']

        # Add simple glow effect
        ax.scatter(
            bullish_reversal_times, bullish_reversal_values,
            color=style['glow_color'], marker=style['marker'], s=style['glow_size'],
            alpha=style['glow_alpha'], zorder=19, edgecolor='none'
        )

        # Add main marker
        scatter = ax.scatter(
            bullish_reversal_times, bullish_reversal_values,
            color=style['color'], marker=style['marker'], s=style['size'],
            label='Bullish Reversal', zorder=20, edgecolor='white', linewidth=1,
            picker=True  # Enable picking for click events
        )
        scatter_objects['bullish_reversal'] = scatter

        # Store signal data for click handling
        for i, (x, y) in enumerate(zip(bullish_reversal_times, bullish_reversal_values)):
            if i < len(bullish_reversal_data):
                signal_data_by_position[(x, y)] = bullish_reversal_data[i]

    # Add bearish reversal markers with enhanced glow
    if bearish_reversal_times:
        style = signal_styles['BearishReversal']

        # Add simple glow effect
        ax.scatter(
            bearish_reversal_times, bearish_reversal_values,
            color=style['glow_color'], marker=style['marker'], s=style['glow_size'],
            alpha=style['glow_alpha'], zorder=19, edgecolor='none'
        )

        # Add main marker
        scatter = ax.scatter(
            bearish_reversal_times, bearish_reversal_values,
            color=style['color'], marker=style['marker'], s=style['size'],
            label='Bearish Reversal', zorder=20, edgecolor='white', linewidth=1,
            picker=True  # Enable picking for click events
        )
        scatter_objects['bearish_reversal'] = scatter

        # Store signal data for click handling
        for i, (x, y) in enumerate(zip(bearish_reversal_times, bearish_reversal_values)):
            if i < len(bearish_reversal_data):
                signal_data_by_position[(x, y)] = bearish_reversal_data[i]

    # No animation for glow effect - using static glow

    # Add click event handling for matplotlib
    if hasattr(ax.figure, 'canvas'):
        # Store signal data in the figure for access in the click handler
        ax.figure.signal_data_by_position = signal_data_by_position

        # Define click handler function for scatter points
        def on_pick(event):
            # Check if the picked artist is one of our scatter plots
            if event.artist in scatter_objects.values():
                # Get the index of the point that was picked
                ind = event.ind[0]

                # Get the coordinates of the picked point
                scatter = event.artist
                x = scatter.get_offsets()[ind, 0]
                y = scatter.get_offsets()[ind, 1]

                # Find the corresponding signal data
                for (pos_x, pos_y), signal_data in ax.figure.signal_data_by_position.items():
                    # Check if this is the point we clicked (using approximate comparison for floats)
                    if abs(pos_x - x) < 1e-6 and abs(pos_y - y) < 1e-6:
                        # Create and show popup
                        parent = ax.figure.canvas.parent()
                        popup = SignalPopup(signal_data, parent=parent)
                        popup.exec()
                        break

        # Connect pick handler
        ax.figure.canvas.mpl_connect('pick_event', on_pick)

        # Also keep the click handler for better usability
        def on_click(event):
            if event.inaxes != ax or event.button != MouseButton.LEFT:
                return

            # Check if click is near any signal point
            for (x, y), signal_data in ax.figure.signal_data_by_position.items():
                # Convert data coordinates to display coordinates
                display_coords = ax.transData.transform((x, y))
                click_coords = ax.transData.transform((event.xdata, event.ydata))

                # Calculate distance in pixels
                distance = np.sqrt((display_coords[0] - click_coords[0])**2 +
                                  (display_coords[1] - click_coords[1])**2)

                # If click is close to a signal point, show popup
                if distance < 15:  # 15 pixels threshold
                    # Create and show popup
                    parent = ax.figure.canvas.parent()
                    popup = SignalPopup(signal_data, parent=parent)
                    popup.exec()
                    break

        # Connect click handler
        ax.figure.canvas.mpl_connect('button_press_event', on_click)

    return scatter_objects


def integrate_with_pyqtgraph(plot_widget, signal_data_list, x_values, y_values):
    """
    Create enhanced signal markers on a pyqtgraph plot widget.

    This is a wrapper around create_signal_markers that can be used
    as a drop-in replacement for existing pyqtgraph scatter plots.

    Args:
        plot_widget: The pyqtgraph PlotWidget to add markers to
        signal_data_list: List of signal data dictionaries
        x_values: List of x-coordinates for the signals
        y_values: List of y-coordinates for the signals

    Returns:
        Dictionary of signal markers by type
    """
    return create_signal_markers(plot_widget, signal_data_list, x_values, y_values)


def convert_matplotlib_to_pyqtgraph(fig, ax):
    """
    Convert a matplotlib figure to a pyqtgraph plot widget.

    This function is useful for transitioning from matplotlib to pyqtgraph
    for better interactive features.

    Args:
        fig: Matplotlib figure
        ax: Matplotlib axes

    Returns:
        PyQtGraph PlotWidget with the same content
    """
    # Create a new plot widget
    plot_widget = pg.PlotWidget()
    plot_widget.setBackground('#1e2a3a')
    plot_widget.showGrid(x=True, y=True, alpha=0.3)

    # Get the lines from the matplotlib axes
    for line in ax.get_lines():
        x_data = line.get_xdata()
        y_data = line.get_ydata()
        color = line.get_color()

        # Add to pyqtgraph
        plot_widget.plot(x_data, y_data, pen=pg.mkPen(color=color, width=1))

    # Get the scatter plots from the matplotlib axes
    for collection in ax.collections:
        if hasattr(collection, 'get_offsets'):
            offsets = collection.get_offsets()
            if len(offsets) > 0:
                x_data = offsets[:, 0]
                y_data = offsets[:, 1]

                # Try to get color and size
                face_color = collection.get_facecolor()
                if len(face_color) > 0:
                    color = face_color[0]
                else:
                    color = (0, 0, 0, 1)

                sizes = collection.get_sizes()
                if len(sizes) > 0:
                    size = sizes[0]
                else:
                    size = 10

                # Add to pyqtgraph
                scatter = pg.ScatterPlotItem(
                    x=x_data, y=y_data,
                    pen=pg.mkPen(color=color, width=1),
                    brush=pg.mkBrush(color=color),
                    size=np.sqrt(size),  # Adjust size for pyqtgraph
                    symbol='o'
                )
                plot_widget.addItem(scatter)

    return plot_widget
