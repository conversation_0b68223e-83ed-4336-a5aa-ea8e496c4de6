"""
Signal Processor Module for Fractal Tester

This module contains the SignalProcessor class which encapsulates all signal filtering
and processing logic for the Fractal Tester application.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple, List, Union
import logging
from datetime import datetime

# Import parameter registry
from parameter_registry import ParameterRegistry, default_registry

# Note: Main logging configuration is in main.py
# This is just a fallback in case this module is used standalone
logging.basicConfig(
    level=logging.ERROR,  # Minimal verbosity, only errors
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("signal_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SignalProcessor")
logger.setLevel(logging.ERROR)

class SignalProcessor:
    """
    A class to handle all signal processing and filtering logic for trading signals.

    This class encapsulates the logic for:
    - Pivot constraint enforcement
    - Signal confirmation
    - Volatility-based filtering
    - RSI filtering
    - Signal strength calculation
    """

    def __init__(self, settings=None, parent=None, registry=None):
        """
        Initialize the SignalProcessor.

        Args:
            settings: Settings object containing filter parameters
            parent: Parent object (usually the main application) for accessing data
            registry: Parameter registry to use (default: global default_registry)
        """
        self.settings = settings
        self.parent = parent
        self.registry = registry if registry else default_registry

        # Initialize parameters from registry
        self.confirmation_period = self.registry.get_value('base_confirmation_period')
        self.min_crossing_magnitude = self.registry.get_value('min_magnitude')

        # Other instance variables
        self.current_potential_crossing = None
        self.signal_log = []
        self.max_log_entries = 100

        # Online learning has been removed

        logger.debug("SignalProcessor initialized")

        # Signal strength visualization settings with modern Material Design colors
        self.signal_strength_colors = {
            'strong': '#4CAF50',  # Material Design Green
            'medium': '#FFC107',  # Material Design Amber
            'weak': '#FF9800',    # Material Design Orange
            'blocked': '#F44336'  # Material Design Red
        }

        # Signal icons for visual enhancement
        self.signal_icons = {
            'strong': '✓',  # Checkmark for strong signals
            'medium': '◉',  # Filled circle for medium signals
            'weak': '○',    # Empty circle for weak signals
            'blocked': '✗'  # X for blocked signals
        }

    def enforce_pivot_constraint(self, row, vector_price, use_high_low=False):
        """
        Enforce the pivot constraint for a potential crossing.

        Args:
            row: DataFrame row containing OHLC data
            vector_price: The vector price to compare against
            use_high_low: Whether to use High/Low prices in the constraint

        Returns:
            bool: True if constraint is satisfied, False otherwise
        """
        logger.debug(f"Enforcing pivot: Open={row['Open']}, Close={row['Close']}, High={row['High']}, Low={row['Low']}, Vector={vector_price}")

        if use_high_low:
            # Enhanced logic using High/Low prices
            if row['Close'] < row['Open']:  # Bearish candle
                # For bearish candles, check if the Low has penetrated the vector
                if row['Low'] > vector_price:
                    logger.debug("Pivot constraint FAILED (bearish): Even Low price didn't penetrate Vector")
                    return False
                # If Low penetrated but Close didn't, check penetration percentage
                elif row['Close'] > vector_price:
                    penetration_pct = (row['Low'] - vector_price) / vector_price * 100
                    if abs(penetration_pct) < 0.1:  # Less than 0.1% penetration
                        logger.debug(f"Pivot constraint FAILED (bearish): Insufficient Low penetration ({penetration_pct:.3f}%)")
                        return False
                    logger.debug(f"Pivot constraint PASSED (bearish): Sufficient Low penetration ({penetration_pct:.3f}%)")
            else:  # Bullish candle
                # For bullish candles, check if the High has penetrated the vector
                if row['High'] < vector_price:
                    logger.debug("Pivot constraint FAILED (bullish): Even High price didn't penetrate Vector")
                    return False
                # If High penetrated but Close didn't, check penetration percentage
                elif row['Close'] < vector_price:
                    penetration_pct = (row['High'] - vector_price) / vector_price * 100
                    if abs(penetration_pct) < 0.1:  # Less than 0.1% penetration
                        logger.debug(f"Pivot constraint FAILED (bullish): Insufficient High penetration ({penetration_pct:.3f}%)")
                        return False
                    logger.debug(f"Pivot constraint PASSED (bullish): Sufficient High penetration ({penetration_pct:.3f}%)")
        else:
            # Original logic using only Open/Close
            if row['Close'] < row['Open']:  # Bearish candle
                if row['Close'] > vector_price:
                    logger.debug("Pivot constraint FAILED (bearish): Close > Vector")
                    return False
            else:  # Bullish candle
                if row['Close'] < vector_price:
                    logger.debug("Pivot constraint FAILED (bullish): Close < Vector")
                    return False

        logger.debug("Pivot constraint passed")
        return True

    def check_rsi_filter(self, rsi_value, direction, threshold_high=None, threshold_low=None):
        """
        Apply RSI filter to potential signals.

        Args:
            rsi_value: Current RSI value
            direction: Signal direction ('up' or 'down')
            threshold_high: Upper RSI threshold (default: from registry)
            threshold_low: Lower RSI threshold (default: from registry)

        Returns:
            bool: True if RSI filter passes, False if it blocks the signal
        """
        # Get thresholds from registry if not provided
        if threshold_high is None:
            threshold_high = self.registry.get_value('rsi_overbought')
        if threshold_low is None:
            threshold_low = self.registry.get_value('rsi_oversold')

        # Apply RSI filter
        if direction == 'down' and rsi_value < threshold_high:
            logger.debug(f"RSI filter blocked down signal: {rsi_value:.1f} < {threshold_high}")
            return False
        elif direction == 'up' and rsi_value > threshold_low:
            logger.debug(f"RSI filter blocked up signal: {rsi_value:.1f} > {threshold_low}")
            return False

        # Signal passed RSI filter
        logger.debug(f"RSI filter passed: {direction} signal with RSI {rsi_value:.1f}")
        return True

    def adjust_for_volatility(self, volatility):
        """
        Adjust signal filtering parameters based on market volatility.

        Args:
            volatility: Current market volatility (%)

        Returns:
            Tuple[int, float]: Updated (confirmation_period, min_magnitude)
        """
        if volatility is None:
            return self.confirmation_period, self.min_crossing_magnitude

        # Get base values from registry
        base_confirmation_period = self.registry.get_value('base_confirmation_period')
        base_min_magnitude = self.registry.get_value('min_magnitude')
        volatility_threshold_low = self.registry.get_value('volatility_threshold_low')
        volatility_threshold_high = self.registry.get_value('volatility_threshold_high')

        # Adjust confirmation period based on volatility
        # Higher volatility = more confirmation candles needed
        if volatility < volatility_threshold_low:  # Low volatility
            new_confirmation_period = max(1, base_confirmation_period - 1)
            new_min_magnitude = base_min_magnitude * 0.8  # Lower threshold for low volatility
        elif volatility > volatility_threshold_high:  # High volatility
            new_confirmation_period = min(5, base_confirmation_period + 1)
            new_min_magnitude = base_min_magnitude * 1.5  # Higher threshold for high volatility
        else:  # Medium volatility
            new_confirmation_period = base_confirmation_period
            new_min_magnitude = base_min_magnitude

        # Online learning code has been removed

        logger.debug(f"Adjusted for volatility {volatility:.1f}%: Confirmation={new_confirmation_period}, Magnitude={new_min_magnitude:.2f}%")
        return new_confirmation_period, new_min_magnitude

    def calculate_pivot_constraint_score(self, row, vector_price, use_high_low=False):
        """
        Calculate a score for pivot constraint instead of binary pass/fail.

        Args:
            row: DataFrame row containing OHLC data
            vector_price: The vector price to compare against
            use_high_low: Whether to use High/Low prices in the constraint

        Returns:
            float: Score between 0.0 and 1.0 representing pivot constraint quality
        """
        score = 0.5  # Start with neutral score

        if use_high_low:
            # Enhanced scoring using High/Low prices
            if row['Close'] < row['Open']:  # Bearish candle
                if row['Low'] <= vector_price:
                    # Calculate penetration percentage
                    penetration_pct = abs((row['Low'] - vector_price) / vector_price * 100)
                    # Score based on penetration depth
                    score = min(1.0, penetration_pct / 0.5)  # 0.5% penetration = max score

                    # Bonus if close also penetrated
                    if row['Close'] <= vector_price:
                        score = min(1.0, score + 0.2)
                else:
                    score = 0.0  # No penetration at all
            else:  # Bullish candle
                if row['High'] >= vector_price:
                    # Calculate penetration percentage
                    penetration_pct = abs((row['High'] - vector_price) / vector_price * 100)
                    # Score based on penetration depth
                    score = min(1.0, penetration_pct / 0.5)  # 0.5% penetration = max score

                    # Bonus if close also penetrated
                    if row['Close'] >= vector_price:
                        score = min(1.0, score + 0.2)
                else:
                    score = 0.0  # No penetration at all
        else:
            # Basic scoring using only Open/Close
            if row['Close'] < row['Open']:  # Bearish candle
                if row['Close'] <= vector_price:
                    # Calculate penetration percentage
                    penetration_pct = abs((row['Close'] - vector_price) / vector_price * 100)
                    # Score based on penetration depth
                    score = min(1.0, penetration_pct / 0.3)  # 0.3% penetration = max score
                else:
                    score = 0.0  # No penetration
            else:  # Bullish candle
                if row['Close'] >= vector_price:
                    # Calculate penetration percentage
                    penetration_pct = abs((row['Close'] - vector_price) / vector_price * 100)
                    # Score based on penetration depth
                    score = min(1.0, penetration_pct / 0.3)  # 0.3% penetration = max score
                else:
                    score = 0.0  # No penetration

        logger.debug(f"Pivot constraint score: {score:.2f}")
        return score

    def calculate_rsi_score(self, rsi_value, direction):
        """
        Calculate a score for RSI filter instead of binary pass/fail.

        Args:
            rsi_value: Current RSI value
            direction: Signal direction ('up' or 'down')

        Returns:
            float: Score between 0.0 and 1.0 representing RSI alignment quality
        """
        if direction == 'down':
            # For down signals, higher RSI = better score (overbought condition)
            # Ideal: RSI > 70 (score = 1.0)
            # Poor: RSI < 40 (score = 0.0)
            if rsi_value >= 70:
                score = 1.0
            elif rsi_value <= 40:
                score = 0.0
            else:
                score = (rsi_value - 40) / 30  # Linear scale between 40-70
        else:  # 'up' direction
            # For up signals, lower RSI = better score (oversold condition)
            # Ideal: RSI < 30 (score = 1.0)
            # Poor: RSI > 60 (score = 0.0)
            if rsi_value <= 30:
                score = 1.0
            elif rsi_value >= 60:
                score = 0.0
            else:
                score = (60 - rsi_value) / 30  # Linear scale between 30-60

        return score

    def calculate_volatility_score(self, volatility):
        """
        Calculate a score for volatility context.

        Args:
            volatility: Current market volatility (%)

        Returns:
            float: Score between 0.0 and 1.0 representing volatility context
        """
        # For volatility, moderate values are ideal
        # Too low = market not moving enough, too high = too risky
        # Ideal range: 10-20% volatility
        if volatility < 5:
            score = 0.3  # Too low volatility
        elif volatility < 10:
            score = 0.7  # Good low volatility
        elif volatility <= 20:
            score = 1.0  # Ideal volatility range
        elif volatility <= 30:
            score = 0.7  # Elevated but manageable volatility
        else:
            score = 0.3  # Too high volatility

        return score

    def calculate_signal_strength(self, price, vector_price, rsi_value=None,
                                 volatility=None, classifier_confidence=None):
        """
        Calculate overall signal strength based on multiple factors.

        Args:
            price: Current price
            vector_price: Vector price
            rsi_value: Current RSI value (optional)
            volatility: Current volatility (optional)
            classifier_confidence: ML classifier confidence (optional)

        Returns:
            Dict: Signal strength information including score and color
        """
        # Base score from price magnitude
        magnitude = abs((price / vector_price - 1) * 100)
        base_score = min(1.0, magnitude / 0.5)  # Normalize to 0-1, with 0.5% being max

        # Adjust for RSI if available
        rsi_score = 0.5  # Neutral by default
        if rsi_value is not None:
            if price > vector_price:  # Up signal
                rsi_score = (40 - rsi_value) / 40  # Higher score for lower RSI (oversold)
            else:  # Down signal
                rsi_score = (rsi_value - 60) / 40  # Higher score for higher RSI (overbought)
            rsi_score = max(0, min(1, rsi_score))

        # Adjust for volatility if available
        volatility_score = 0.5  # Neutral by default
        if volatility is not None:
            if volatility < 10:
                volatility_score = 0.8  # Low volatility is good for signal reliability
            elif volatility > 30:
                volatility_score = 0.2  # High volatility reduces signal reliability
            else:
                volatility_score = 0.5  # Medium volatility is neutral

        # Include classifier confidence if available
        classifier_score = 0.5  # Neutral by default
        if classifier_confidence is not None:
            classifier_score = classifier_confidence

        # Calculate weighted average
        weights = [0.4, 0.2, 0.2, 0.2]  # Base, RSI, Volatility, Classifier
        scores = [base_score, rsi_score, volatility_score, classifier_score]

        # Calculate final score (0-1)
        final_score = sum(w * s for w, s in zip(weights, scores))

        # Determine strength category and color
        if final_score >= 0.7:
            strength = "strong"
            color = self.signal_strength_colors['strong']
        elif final_score >= 0.5:
            strength = "medium"
            color = self.signal_strength_colors['medium']
        elif final_score >= 0.3:
            strength = "weak"
            color = self.signal_strength_colors['weak']
        else:
            strength = "blocked"
            color = self.signal_strength_colors['blocked']

        result = {
            'score': final_score,
            'strength': strength,
            'color': color,
            'components': {
                'magnitude': base_score,
                'rsi': rsi_score,
                'volatility': volatility_score,
                'classifier': classifier_score
            }
        }

        logger.debug(f"Signal strength: {final_score:.2f} ({strength})")
        return result

    def log_signal(self, signal_type, direction, details):
        """
        Log a signal event for later analysis.

        Args:
            signal_type: Type of signal (e.g., 'potential', 'confirmed', 'blocked')
            direction: Signal direction ('up' or 'down')
            details: Dictionary of signal details

        Returns:
            Dictionary containing the logged signal information
        """
        timestamp = pd.Timestamp.now()
        log_entry = {
            'timestamp': timestamp,
            'type': signal_type,
            'direction': direction,
            'details': details
        }

        self.signal_log.append(log_entry)

        # Trim log if it gets too large
        if len(self.signal_log) > self.max_log_entries:
            self.signal_log = self.signal_log[-self.max_log_entries:]

        # Online learning code has been removed

        # Only log important signals (confirmed or blocked) at INFO level, others at DEBUG
        if signal_type in ['confirmed', 'blocked']:
            logger.info(f"Signal logged: {signal_type} {direction} at {timestamp}")
        else:
            logger.debug(f"Signal logged: {signal_type} {direction} at {timestamp}")
        return log_entry

    def get_signal_log(self):
        """Get the signal log for analysis."""
        return self.signal_log

    def update_signal_outcome(self, log_entry, actual_outcome, performance_metrics=None):
        """
        Update a signal with its actual outcome and performance metrics.

        Args:
            log_entry: The signal entry to update (from log_signal)
            actual_outcome: Actual outcome of the signal (1=valid, 0=invalid)
            performance_metrics: Additional performance metrics for this signal

        Returns:
            bool: True if signal was updated successfully
        """
        # Update the signal entry
        if 'details' not in log_entry:
            log_entry['details'] = {}

        log_entry['details']['actual_outcome'] = actual_outcome

        if performance_metrics:
            if 'metrics' not in log_entry['details']:
                log_entry['details']['metrics'] = {}
            log_entry['details']['metrics'].update(performance_metrics)

        # Online learning code has been removed

        return True

    # Online learning method has been removed

    def process_potential_crossing(self, current_idx, price, vector_price, below_vector,
                                  data=None, rsi_value=None, volatility=None,
                                  classifier_result=None):
        """
        Process a potential crossing and apply all filtering logic.

        Args:
            current_idx: Current data index
            price: Current price
            vector_price: Vector price
            below_vector: Whether price was below vector in previous state
            data: DataFrame containing OHLC data
            rsi_value: Current RSI value (optional)
            volatility: Current volatility (optional)
            classifier_result: ML classifier result (optional)

        Returns:
            Dict: Result of signal processing including status and details
        """
        # Initialize result
        result = {
            'status': 'pending',
            'direction': None,
            'strength': None,
            'message': "",
            'color': "#FFFFFF",
            'confidence': 0.0,
            'components': {}
        }

        # Determine crossing direction
        new_direction = None
        if not below_vector and price < vector_price:
            new_direction = 'down'
        elif below_vector and price > vector_price:
            new_direction = 'up'

        # If no crossing detected, return early
        if new_direction is None:
            result['status'] = 'none'
            result['message'] = "No crossing detected"
            return result

        # Check if we're using probabilistic filtering
        use_probabilistic = False
        confidence_threshold = 70.0  # Default threshold
        if self.settings is not None and hasattr(self.settings, 'use_probabilistic_filtering'):
            use_probabilistic = self.settings.use_probabilistic_filtering.isChecked()
            if hasattr(self.settings, 'confidence_threshold_spin'):
                confidence_threshold = self.settings.confidence_threshold_spin.value()

        # Initialize component scores for probabilistic filtering
        pivot_score = 0.0
        rsi_score = 0.0
        classifier_score = 0.0
        volatility_score = 0.5  # Neutral default

        # Apply pivot constraint if data is provided
        if data is not None and current_idx < len(data):
            row = data.iloc[current_idx]
            use_high_low = (self.settings is not None and
                           hasattr(self.settings, 'use_high_low_constraint') and
                           self.settings.use_high_low_constraint.isChecked())

            if use_probabilistic:
                # Get a score instead of binary result
                pivot_score = self.calculate_pivot_constraint_score(row, vector_price, use_high_low)
                result['components']['pivot'] = pivot_score
                logger.info(f"Pivot constraint score: {pivot_score:.2f}")
            else:
                # Traditional binary filtering
                if not self.enforce_pivot_constraint(row, vector_price, use_high_low):
                    result['status'] = 'blocked'
                    result['direction'] = new_direction
                    icon = self.signal_icons['blocked']
                    result['message'] = f"{icon} Blocked {new_direction.upper()} cross: Pivot constraint not satisfied"
                    result['color'] = self.signal_strength_colors['blocked']
                    self.log_signal('blocked', new_direction, {'reason': 'pivot_constraint'})
                    return result

        # Apply RSI filter if enabled and RSI value is provided
        if rsi_value is not None and self.settings is not None and hasattr(self.settings, 'use_rsi_filter'):
            if self.settings.use_rsi_filter.isChecked():
                if use_probabilistic:
                    # Get a score instead of binary result
                    rsi_score = self.calculate_rsi_score(rsi_value, new_direction)
                    result['components']['rsi'] = rsi_score
                    logger.info(f"RSI score: {rsi_score:.2f} (RSI: {rsi_value:.1f}, Direction: {new_direction})")
                else:
                    # Traditional binary filtering
                    if not self.check_rsi_filter(rsi_value, new_direction):
                        result['status'] = 'blocked'
                        result['direction'] = new_direction
                        icon = self.signal_icons['blocked']
                        result['message'] = f"{icon} Blocked {new_direction.upper()} cross: RSI filter ({rsi_value:.1f}) not satisfied"
                        result['color'] = self.signal_strength_colors['blocked']
                        self.log_signal('blocked', new_direction, {'reason': 'rsi_filter', 'rsi': rsi_value})
                        return result

        # Apply classifier probability if available
        if classifier_result is not None and 'probability' in classifier_result:
            classifier_score = classifier_result['probability']
            result['components']['classifier'] = classifier_score
            logger.info(f"Classifier score: {classifier_score:.2f}")

        # Calculate volatility score if volatility adjustment is enabled
        if (volatility is not None and self.settings is not None and
            hasattr(self.settings, 'use_volatility_adjustment') and
            self.settings.use_volatility_adjustment.isChecked()):

            if use_probabilistic:
                volatility_score = self.calculate_volatility_score(volatility)
                result['components']['volatility'] = volatility_score
                logger.info(f"Volatility score: {volatility_score:.2f} (Volatility: {volatility:.1f}%)")
            else:
                # Traditional adjustment
                self.confirmation_period, self.min_crossing_magnitude = self.adjust_for_volatility(volatility)

        # Initialize or update potential crossing
        if self.current_potential_crossing is None or self.current_potential_crossing['direction'] != new_direction:
            # Start a new potential crossing
            self.current_potential_crossing = {
                'direction': new_direction,
                'start_index': current_idx,
                'max_magnitude': abs((price / vector_price - 1) * 100)
            }

            result['status'] = 'potential'
            result['direction'] = new_direction
            icon = self.signal_icons['weak']  # Use weak icon for potential signals
            result['message'] = f"{icon} Potential {new_direction.upper()} cross: 0/{self.confirmation_period} candles"
            result['color'] = self.signal_strength_colors['weak']  # Use weak color for potential

            self.log_signal('potential', new_direction, {
                'magnitude': self.current_potential_crossing['max_magnitude'],
                'confirmation': 0,
                'required': self.confirmation_period
            })

            return result

        # Update existing potential crossing
        elapsed = current_idx - self.current_potential_crossing['start_index']
        magnitude = abs((price / vector_price - 1) * 100)

        # Update max magnitude
        if magnitude > self.current_potential_crossing['max_magnitude']:
            self.current_potential_crossing['max_magnitude'] = magnitude

        # Check if confirmation period has passed
        if elapsed >= self.confirmation_period:
            # For probabilistic filtering
            if use_probabilistic:
                # Calculate final confidence score
                # Define component weights (must sum to 1.0)
                weights = {
                    'pivot': 0.25,
                    'rsi': 0.20,
                    'classifier': 0.35,
                    'volatility': 0.10,
                    'magnitude': 0.10
                }

                # Add magnitude as a component
                magnitude_score = min(1.0, self.current_potential_crossing['max_magnitude'] / self.min_crossing_magnitude)
                result['components']['magnitude'] = magnitude_score

                # Calculate weighted confidence score
                confidence = 0.0
                for component, score in result['components'].items():
                    confidence += score * weights.get(component, 0.0)

                # Scale to percentage
                confidence_pct = confidence * 100.0
                result['confidence'] = confidence_pct

                # Determine if confidence threshold is met
                if confidence_pct >= confidence_threshold:
                    # Signal is confirmed with sufficient confidence
                    result['status'] = 'confirmed'
                    result['direction'] = new_direction

                    # Set strength based on confidence level
                    if confidence_pct >= 90:
                        strength_level = 'strong'
                    elif confidence_pct >= 75:
                        strength_level = 'medium'
                    else:
                        strength_level = 'weak'

                    result['strength'] = strength_level
                    icon = self.signal_icons[strength_level]
                    result['message'] = f"{icon} Confirmed {new_direction.upper()} cross: {confidence_pct:.1f}% confidence ({strength_level.upper()})"
                    result['color'] = self.signal_strength_colors[strength_level]

                    # Log the confirmed signal with detailed component breakdown
                    self.log_signal('confirmed', new_direction, {
                        'confidence': confidence_pct,
                        'components': result['components'],
                        'magnitude': self.current_potential_crossing['max_magnitude'],
                        'classifier_result': classifier_result
                    })

                    # Reset potential crossing after confirmation
                    self.current_potential_crossing = None

                    return result
                else:
                    # Confidence threshold not met
                    result['status'] = 'rejected'
                    result['direction'] = new_direction
                    icon = self.signal_icons['blocked']
                    result['message'] = f"{icon} Rejected {new_direction.upper()} cross: confidence {confidence_pct:.1f}% < {confidence_threshold}%"
                    result['color'] = self.signal_strength_colors['blocked']

                    # Log the rejected signal with component breakdown
                    self.log_signal('rejected', new_direction, {
                        'reason': 'insufficient_confidence',
                        'confidence': confidence_pct,
                        'threshold': confidence_threshold,
                        'components': result['components']
                    })

                    # Reset potential crossing after rejection
                    self.current_potential_crossing = None

                    return result
            else:
                # Traditional binary filtering
                # Check if magnitude threshold is met
                if self.current_potential_crossing['max_magnitude'] >= self.min_crossing_magnitude:
                    # Calculate signal strength
                    strength = self.calculate_signal_strength(
                        price,
                        vector_price,
                        rsi_value,
                        volatility,
                        classifier_result['confidence'] if classifier_result and 'confidence' in classifier_result else None
                    )

                    result['status'] = 'confirmed'
                    result['direction'] = new_direction
                    result['strength'] = strength['strength']
                    icon = self.signal_icons[strength['strength']]
                    result['message'] = f"{icon} Confirmed {new_direction.upper()} cross: {strength['strength'].upper()}"
                    result['color'] = strength['color']

                    self.log_signal('confirmed', new_direction, {
                        'magnitude': self.current_potential_crossing['max_magnitude'],
                        'strength': strength,
                        'classifier_confidence': classifier_result['confidence'] if classifier_result and 'confidence' in classifier_result else None
                    })

                    # Reset potential crossing after confirmation
                    self.current_potential_crossing = None

                    return result
                else:
                    # Magnitude threshold not met
                    result['status'] = 'rejected'
                    result['direction'] = new_direction
                    icon = self.signal_icons['blocked']
                    result['message'] = f"{icon} Rejected {new_direction.upper()} cross: Magnitude {self.current_potential_crossing['max_magnitude']:.2f}% < {self.min_crossing_magnitude}% threshold"
                    result['color'] = self.signal_strength_colors['blocked']

                    self.log_signal('rejected', new_direction, {
                        'reason': 'insufficient_magnitude',
                        'actual': self.current_potential_crossing['max_magnitude'],
                        'required': self.min_crossing_magnitude
                    })

                    # Reset potential crossing after rejection
                    self.current_potential_crossing = None

                    return result
        else:
            # Still waiting for confirmation
            result['status'] = 'potential'
            result['direction'] = new_direction

            # For probabilistic filtering, show current confidence
            if use_probabilistic:
                # Calculate current confidence
                weights = {
                    'pivot': 0.25,
                    'rsi': 0.20,
                    'classifier': 0.35,
                    'volatility': 0.10,
                    'magnitude': 0.10
                }

                # Add magnitude as a component
                magnitude_score = min(1.0, self.current_potential_crossing['max_magnitude'] / self.min_crossing_magnitude)
                result['components']['magnitude'] = magnitude_score

                # Calculate weighted confidence score
                confidence = 0.0
                for component, score in result['components'].items():
                    confidence += score * weights.get(component, 0.0)

                # Scale to percentage
                confidence_pct = confidence * 100.0
                result['confidence'] = confidence_pct

                # Set color based on current confidence
                if confidence_pct >= 75:
                    result['color'] = "#FFC107"  # Amber for high potential
                else:
                    result['color'] = "#FFA500"  # Orange for medium potential

                # Choose icon based on confidence level
                if confidence_pct >= 75:
                    icon = self.signal_icons['medium']
                else:
                    icon = self.signal_icons['weak']

                result['message'] = f"{icon} Potential {new_direction.upper()} cross: {elapsed}/{self.confirmation_period} candles\nConfidence: {confidence_pct:.1f}%, Magnitude: {self.current_potential_crossing['max_magnitude']:.2f}%"

                self.log_signal('potential_update', new_direction, {
                    'magnitude': self.current_potential_crossing['max_magnitude'],
                    'confirmation': elapsed,
                    'required': self.confirmation_period,
                    'confidence': confidence_pct,
                    'components': result['components']
                })
            else:
                # Traditional visualization with improved formatting
                icon = self.signal_icons['weak']
                result['message'] = f"{icon} Potential {new_direction.upper()} cross: {elapsed}/{self.confirmation_period} candles\nMagnitude: {self.current_potential_crossing['max_magnitude']:.2f}%"
                result['color'] = self.signal_strength_colors['weak']

                self.log_signal('potential_update', new_direction, {
                    'magnitude': self.current_potential_crossing['max_magnitude'],
                    'confirmation': elapsed,
                    'required': self.confirmation_period
                })

            return result
