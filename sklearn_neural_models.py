"""
Neural network models using scikit-learn's MLPClassifier as alternatives to TensorFlow models.
These provide similar functionality to GRU and LSTM models without requiring TensorFlow.
This module now uses explicit sequence modeling for better temporal pattern recognition.
"""

import numpy as np
from sklearn.neural_network import MLPClassifier
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.preprocessing import StandardScaler
from sequence_modeling import GRULikeSequenceClassifier, LSTMLikeSequenceClassifier

# For backward compatibility, we'll keep the original class names but use our new sequence models
class GRUClassifier(GRULikeSequenceClassifier):
    """
    A GRU-like classifier using explicit sequence modeling.

    This model is optimized for shorter sequences with faster dynamics,
    similar to how GRU networks handle short-term dependencies.
    """
    pass


class LSTMClassifier(LSTMLikeSequenceClassifier):
    """
    An LSTM-like classifier using explicit sequence modeling.

    This model is optimized for longer sequences with memory requirements,
    similar to how LSTM networks maintain long-term dependencies.
    """
    pass


def get_model_for_timeframe(timeframe, vector_length):
    """Returns appropriate model based on timeframe"""
    lower_timeframes = ['1m', '5m', '15m', '30m', '1h']
    if timeframe in lower_timeframes and vector_length <= 20:
        return GRUClassifier(sequence_length=min(10, vector_length))
    return LSTMClassifier(sequence_length=min(20, vector_length))


def get_model_weights(model_type, accuracy):
    """Returns appropriate weight for model based on type and accuracy"""
    base_weights = {
        'gru': 1.2,  # Slightly higher weight for GRU on short timeframes
        'lstm': 1.3,  # Higher weight for LSTM on long timeframes
        'random_forest': 1.0,
        'gradient_boosting': 1.1,
        'xgboost': 1.15,
        'lightgbm': 1.15
    }

    # Adjust weight based on accuracy
    weight = base_weights.get(model_type, 1.0)
    accuracy_factor = max(0.5, min(1.5, accuracy * 1.5))  # Scale accuracy to 0.5-1.5 range

    return weight * accuracy_factor

