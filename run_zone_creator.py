#!/usr/bin/env python3
"""
Zone Creator Launcher

Simple launcher script for the Zone Creator application.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from zone_creator import main
    
    if __name__ == "__main__":
        print("Starting Zone Creator...")
        main()
        
except ImportError as e:
    print(f"Error importing zone_creator: {e}")
    print("Please make sure all dependencies are installed:")
    print("pip install PyQt6 pyqtgraph")
    print("or")
    print("pip install PyQt5 pyqtgraph")
    sys.exit(1)
except Exception as e:
    print(f"Error starting Zone Creator: {e}")
    sys.exit(1)
