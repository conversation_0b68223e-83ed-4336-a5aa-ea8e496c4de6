"""
Volatility Graph Tab for Market Odds and Options Analyzer

This module provides a UI tab for visualizing volatility patterns.
"""

from PyQt6 import QtWidgets, QtCore
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging

from crosshair_utility import add_price_only_crosshair

logger = logging.getLogger(__name__)

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
        'highlight': '#FFC107',            # Material Design Amber
        'selection': '#2979FF',            # Selection highlight color
        'button_radius': '4px',            # Button corner radius
        'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
    }

class VolatilityGraphTab(QtWidgets.QWidget):
    """
    Tab for visualizing volatility patterns.
    """
    def __init__(self, parent=None, data_tab=None):
        """
        Initialize the volatility graph tab.

        Args:
            parent: Parent widget
            data_tab: Reference to the Data tab
        """
        super().__init__(parent)
        self.parent = parent
        self.data_tab = data_tab
        self.chart_colors = {
            'background': THEME_COLORS['control_panel'],  # Use theme control panel color
            'bullish': THEME_COLORS['bullish'],           # Material Design Green
            'bearish': THEME_COLORS['bearish'],           # Material Design Red
            'text': THEME_COLORS['text'],                 # Light gray text
            'line': '#FFFFFF',                            # White lines
            'current_price': THEME_COLORS['highlight'],   # Amber for current price
            'highest_high': '#FFFFFF',                    # White for highest high
            'lowest_low': '#FFFFFF',                      # White for lowest low
            'average_high': '#FFFFFF',                    # White for average high
            'average_low': '#FFFFFF',                     # White for average low
            'lowest_high': '#FFFFFF',                     # White for lowest high
            'highest_low': '#FFFFFF',                     # White for highest low
            'avg_high_lowest_high': '#FFFFFF',            # White for average between average high and lowest high
            'avg_low_highest_low': '#FFFFFF',             # White for average between average low and highest low
            'apex': '#FFFFFF',                            # White for apex (median between highest high and lowest low)
            'median_apex_highest_high': '#FFFFFF',        # White for median between apex and highest high
            'median_apex_lowest_low': '#FFFFFF',          # White for median between apex and lowest low
            'long': '#00FF00',                            # Green for long theoretical prices
            'short': '#FF00FF'                            # Magenta for short theoretical prices
        }
        self.volatility_data = None
        self.tree_items = []

        # Initialize theoretical prices as empty list for data subtab
        self.theoretical_prices = []

        # Store the latest calculated price levels for external access
        self.latest_price_levels = {}

        # AK's weekly vol zones state and data
        self.ak_weekly_vol_zones_active = False
        self.ak_fwl5_levels = {}
        self.ak_weekday_levels = {}

        # AK's daily vol zones state and data
        self.ak_daily_vol_zones_active = False
        self.ak_daily_vol_zones_data = {}
        self.ak_daily_vol_zones_cache = {}

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Create a horizontal layout for status and zoom controls
        top_layout = QtWidgets.QHBoxLayout()

        # Status label
        self.status_label = QtWidgets.QLabel("Volatility Graph - Ready")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        top_layout.addWidget(self.status_label)

        # Add stretch to push zoom buttons to the right
        top_layout.addStretch()

        # Create zoom control buttons
        zoom_button_style = f"""
            QPushButton {{
                color: {THEME_COLORS['text']};
                background-color: {THEME_COLORS['control_panel']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 60px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['borders']};
                border: 1px solid white;
            }}
            QPushButton:pressed {{
                background-color: white;
                color: {THEME_COLORS['background']};
            }}
        """

        # Zoom In button
        self.zoom_in_button = QtWidgets.QPushButton("Zoom In")
        self.zoom_in_button.setStyleSheet(zoom_button_style)
        self.zoom_in_button.clicked.connect(self.zoom_in)
        top_layout.addWidget(self.zoom_in_button)

        # Zoom Out button
        self.zoom_out_button = QtWidgets.QPushButton("Zoom Out")
        self.zoom_out_button.setStyleSheet(zoom_button_style)
        self.zoom_out_button.clicked.connect(self.zoom_out)
        top_layout.addWidget(self.zoom_out_button)

        # Reset Zoom button
        self.reset_zoom_button = QtWidgets.QPushButton("Reset Zoom")
        self.reset_zoom_button.setStyleSheet(zoom_button_style)
        self.reset_zoom_button.clicked.connect(self.reset_zoom)
        top_layout.addWidget(self.reset_zoom_button)



        main_layout.addLayout(top_layout)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(self.chart_colors['background'])
        # Empty axis labels to remove confusion
        self.plot_widget.setLabel('left', '', color=self.chart_colors['text'])
        self.plot_widget.setLabel('bottom', '', color=self.chart_colors['text'])
        main_layout.addWidget(self.plot_widget)

        # Store the initial view state for reset functionality
        self.initial_view_state = None

        # Enable mouse interaction for zooming
        self.plot_widget.setMouseEnabled(x=True, y=True)
        self.plot_widget.setMenuEnabled(False)  # Still disable context menu

        # Enable mouse wheel zooming but disable rectangular region selection
        view_box = self.plot_widget.getPlotItem().getViewBox()
        view_box.enableAutoRange(enable=False)  # Disable auto-range to maintain manual zoom control
        view_box.setMouseMode(pg.ViewBox.PanMode)  # Use pan mode instead of rect mode to disable left-click zoom

        # Initialize crosshair (will be properly set up in generate_volatility_graph)
        self.crosshair = None

        # Set background color
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

    def get_data_from_data_tab(self):
        """
        Get data from the Data tab.

        Returns:
            pandas.DataFrame: The data from the Data tab, or None if not available
        """
        if self.data_tab is None:
            logger.warning("No data tab reference available")
            return None

        # Check if the data tab has a table model with data
        if hasattr(self.data_tab, 'table_model') and hasattr(self.data_tab.table_model, '_data'):
            data = self.data_tab.table_model._data
            if data is not None and not data.empty:
                logger.info(f"Retrieved data from data tab: {len(data)} rows")
                return data

        logger.warning("No data available from data tab")
        return None

    def get_latest_category(self, data):
        """
        Get the category from the latest row in the data.

        Args:
            data: DataFrame with the data

        Returns:
            str: The category from the latest row, or None if not available
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return None

        # Get the category from the latest row
        latest_category = data['Category'].iloc[-1]
        logger.info(f"Latest category: {latest_category}")
        return latest_category

    def get_rows_with_same_weekday(self, data):
        """
        Get all rows with the same weekday as the latest row.

        Args:
            data: DataFrame with the data

        Returns:
            DataFrame: Subset of data with matching weekday, or empty DataFrame if no match
        """
        if data is None or data.empty:
            logger.warning("Invalid data for weekday matching")
            return pd.DataFrame()

        try:
            # Log all columns for debugging
            logger.info(f"Available columns in data: {data.columns.tolist()}")

            # Create a copy of the data to avoid modifying the original
            data_copy = data.copy()

            # Try to extract date information from the data
            # Method 1: Look for date-related columns
            date_column = None
            for col in data.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    logger.info(f"Found potential date column: {col}")
                    break

            # Method 2: Check if the index is a DatetimeIndex
            if date_column is None and isinstance(data.index, pd.DatetimeIndex):
                logger.info("Using DatetimeIndex for weekday matching")
                data_copy['_temp_date'] = data.index
                date_column = '_temp_date'

            # Method 3: Try to parse the first column as a date
            if date_column is None:
                logger.info(f"Trying to parse first column as date: {data.columns[0]}")
                try:
                    data_copy['_temp_date'] = pd.to_datetime(data.iloc[:, 0])
                    date_column = '_temp_date'
                    logger.info("Successfully parsed first column as date")
                except:
                    logger.warning("Could not parse first column as date")

            # Method 4: If all else fails, create a date column from the row index
            if date_column is None:
                logger.info("Creating date column from row index")
                # Get the current date
                import datetime
                today = datetime.datetime.now()

                # Create dates going back from today based on row index
                # This assumes the data is in chronological order with the most recent at the end
                dates = []
                for i in range(len(data)):
                    # Go back i days from today
                    date = today - datetime.timedelta(days=i)
                    dates.append(date)

                # Reverse the list so the oldest date is first
                dates.reverse()

                # Add the dates as a new column
                data_copy['_temp_date'] = dates
                date_column = '_temp_date'
                logger.info("Created date column from row index")

            # Convert date column to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(data_copy[date_column]):
                try:
                    data_copy[date_column] = pd.to_datetime(data_copy[date_column])
                    logger.info(f"Converted {date_column} to datetime")
                except Exception as e:
                    logger.error(f"Error converting date column to datetime: {str(e)}")
                    return pd.DataFrame()

            # Get the weekday of the latest row
            latest_date = data_copy[date_column].iloc[-1]
            latest_weekday = latest_date.weekday()  # 0=Monday, 1=Tuesday, ..., 6=Sunday
            weekday_name = latest_date.strftime('%A')  # Full weekday name

            logger.info(f"Latest date: {latest_date}, weekday: {weekday_name} ({latest_weekday})")

            # Filter rows with the same weekday
            matching_rows = data_copy[data_copy[date_column].dt.weekday == latest_weekday]

            if matching_rows.empty:
                logger.warning(f"No rows found with weekday '{weekday_name}'")
            else:
                logger.info(f"Found {len(matching_rows)} rows with weekday '{weekday_name}'")

            # Exclude the latest row to avoid using the latest high/low prices
            if len(matching_rows) > 1:
                # Find the index of the latest row in the matching_rows DataFrame
                latest_row_index = matching_rows.index.max()
                # Drop the latest row
                matching_rows = matching_rows.drop(latest_row_index)
                logger.info(f"Excluded latest row (index {latest_row_index}) from display")

            # Check if we need to limit the number of occurrences
            occurrence_count = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_count = self.parent.get_occurrence_count()

            if occurrence_count > 0 and len(matching_rows) > occurrence_count:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
                # Take only the specified number of occurrences
                matching_rows = matching_rows.head(occurrence_count)
                logger.info(f"Limited to {occurrence_count} most recent occurrences")

            # Drop the temporary date column if we added it
            if date_column == '_temp_date':
                matching_rows = matching_rows.drop('_temp_date', axis=1)

            return matching_rows

        except Exception as e:
            logger.error(f"Error in get_rows_with_same_weekday: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def get_rows_with_same_category(self, data, category):
        """
        Get rows with the same category as the specified category.

        Args:
            data: DataFrame with the data
            category: The category to match

        Returns:
            pandas.DataFrame: Rows with the same category, or an empty DataFrame if none found
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return pd.DataFrame()

        # Get rows with the same category
        matching_rows = data[data['Category'] == category]
        logger.info(f"Found {len(matching_rows)} rows with category '{category}'")

        # Exclude the latest row to avoid using the latest high/low prices
        if len(matching_rows) > 1:
            # Find the index of the latest row in the matching_rows DataFrame
            latest_row_index = matching_rows.index.max()
            # Drop the latest row
            matching_rows = matching_rows.drop(latest_row_index)
            logger.info(f"Excluded latest row (index {latest_row_index}) from display")

        # Check if we need to limit the number of occurrences
        occurrence_count = 0
        if hasattr(self.parent, 'get_occurrence_count'):
            occurrence_count = self.parent.get_occurrence_count()

        if occurrence_count > 0 and len(matching_rows) > occurrence_count:
            # Try to find a date column for sorting
            date_column = None
            for col in matching_rows.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    break

            if date_column is not None:
                # Convert to datetime if needed
                if not pd.api.types.is_datetime64_any_dtype(matching_rows[date_column]):
                    try:
                        matching_rows[date_column] = pd.to_datetime(matching_rows[date_column])
                    except:
                        date_column = None

            if date_column is not None:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
            else:
                # If no date column, sort by index in descending order (assuming higher index = more recent)
                matching_rows = matching_rows.sort_index(ascending=False)

            # Take only the specified number of occurrences
            matching_rows = matching_rows.head(occurrence_count)
            logger.info(f"Limited to {occurrence_count} most recent occurrences")

        return matching_rows

    def calculate_price_levels(self, data):
        """
        Calculate various price levels from the data.

        Args:
            data: DataFrame with the data

        Returns:
            dict: Dictionary with calculated price levels
        """
        if data is None or data.empty:
            logger.warning("No data available for price level calculations")
            return {}



        try:
            # Check if we're in current price mode and if theoretical high/low columns exist
            is_current_price_mode = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_current_price_mode = self.data_tab.calculation_mode == "current_price"
                logger.info(f"Current calculation mode: {self.data_tab.calculation_mode}")

            # Extract high and low values based on the mode
            if is_current_price_mode and 'Projected High' in data.columns and 'Projected Low' in data.columns:
                logger.info("Using Projected High/Low values for volatility statistics")
                high_values = data['Projected High'].values
                low_values = data['Projected Low'].values

                # Log the data columns and a sample of the data
                logger.info(f"DATA COLUMNS: {data.columns.tolist()}")
                logger.info(f"PROJECTED HIGH SAMPLE: {data['Projected High'].head().tolist()}")
                logger.info(f"PROJECTED LOW SAMPLE: {data['Projected Low'].head().tolist()}")
                logger.info(f"DATA TYPES - PROJECTED HIGH: {data['Projected High'].dtype}, PROJECTED LOW: {data['Projected Low'].dtype}")
            else:
                logger.info("Using standard High/Low values for volatility statistics")
                high_values = data['High'].values if 'High' in data.columns else []
                low_values = data['Low'].values if 'Low' in data.columns else []

                # Log the data columns and a sample of the data
                if 'High' in data.columns and 'Low' in data.columns:
                    logger.info(f"DATA COLUMNS: {data.columns.tolist()}")
                    logger.info(f"HIGH SAMPLE: {data['High'].head().tolist()}")
                    logger.info(f"LOW SAMPLE: {data['Low'].head().tolist()}")
                    logger.info(f"DATA TYPES - HIGH: {data['High'].dtype}, LOW: {data['Low'].dtype}")
                else:
                    logger.info(f"Missing High or Low columns. Available columns: {data.columns.tolist()}")

            # Calculate basic price levels
            price_levels = {
                'highest_high': np.max(high_values) if len(high_values) > 0 else None,
                'lowest_low': np.min(low_values) if len(low_values) > 0 else None,
                'average_high': np.mean(high_values) if len(high_values) > 0 else None,
                'average_low': np.mean(low_values) if len(low_values) > 0 else None,
                'lowest_high': np.min(high_values) if len(high_values) > 0 else None,
                'highest_low': np.max(low_values) if len(low_values) > 0 else None,
                # Include the raw high and low values for count calculations
                'high_values': high_values.tolist() if len(high_values) > 0 else [],
                'low_values': low_values.tolist() if len(low_values) > 0 else []
            }

            # Calculate maxavg for highs (average of top 50% highs/highest highs)
            if len(high_values) > 0:
                # Convert to pandas Series for robust data validation
                high_series = pd.Series(high_values)

                # Convert to numeric values, coercing errors to NaN
                high_series = pd.to_numeric(high_series, errors='coerce')

                # Drop any NaN or infinite values
                high_series = high_series.dropna()
                high_series = high_series[np.isfinite(high_series)]

                if len(high_series) > 0:
                    # Sort high values in descending order for top percentile calculation
                    sorted_high_values = high_series.sort_values(ascending=False).values
                    # Take the top 50% (use ceiling to ensure at least 1 value for small datasets)
                    top_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                    top_half_highs = sorted_high_values[:top_half_count]
                    # Calculate robust average using numpy's mean with ddof for sample statistics
                    price_levels['maxavg_high'] = np.mean(top_half_highs)
                    logger.info(f"Calculated maxavg_high from {top_half_count}/{len(high_values)} values: {price_levels['maxavg_high']:.6f}")
                else:
                    price_levels['maxavg_high'] = None
                    logger.warning("No valid high values after data cleaning for maxavg_high")
            else:
                price_levels['maxavg_high'] = None

            # Calculate maxavg for lows (average of bottom 50% lowest lows)
            if len(low_values) > 0:
                # Convert to pandas Series for robust data validation
                low_series = pd.Series(low_values)

                # Convert to numeric values, coercing errors to NaN
                low_series = pd.to_numeric(low_series, errors='coerce')

                # Drop any NaN or infinite values
                low_series = low_series.dropna()
                low_series = low_series[np.isfinite(low_series)]

                if len(low_series) > 0:
                    # Sort low values in ascending order for bottom percentile calculation
                    sorted_low_values = low_series.sort_values(ascending=True).values
                    # Take the bottom 50% (lowest lows) - use ceiling to ensure at least 1 value
                    bottom_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                    bottom_half_lows = sorted_low_values[:bottom_half_count]
                    # Calculate robust average
                    price_levels['maxavg_low'] = np.mean(bottom_half_lows)
                    logger.info(f"Calculated maxavg_low from {bottom_half_count}/{len(low_values)} values: {price_levels['maxavg_low']:.6f}")
                else:
                    price_levels['maxavg_low'] = None
                    logger.warning("No valid low values after data cleaning for maxavg_low")
            else:
                price_levels['maxavg_low'] = None

            # Calculate minavg for highs (average of bottom 50% highs/lowest highs)
            if len(high_values) > 0:
                # Convert to pandas Series for robust data validation
                high_series = pd.Series(high_values)

                # Convert to numeric values, coercing errors to NaN
                high_series = pd.to_numeric(high_series, errors='coerce')

                # Drop any NaN or infinite values
                high_series = high_series.dropna()
                high_series = high_series[np.isfinite(high_series)]

                if len(high_series) > 0:
                    # Sort high values in ascending order for bottom percentile calculation
                    sorted_high_values = high_series.sort_values(ascending=True).values
                    # Take the bottom 50% (lowest highs) - use ceiling to ensure at least 1 value
                    bottom_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                    bottom_half_highs = sorted_high_values[:bottom_half_count]
                    # Calculate robust average
                    price_levels['minavg_high'] = np.mean(bottom_half_highs)
                    logger.info(f"Calculated minavg_high from {bottom_half_count}/{len(high_values)} values: {price_levels['minavg_high']:.6f}")
                else:
                    price_levels['minavg_high'] = None
                    logger.warning("No valid high values after data cleaning for minavg_high")
            else:
                price_levels['minavg_high'] = None

            # Calculate minavg for lows (average of top 50% of lows/highest lows)
            if len(low_values) > 0:
                # Convert to pandas Series for robust data validation
                low_series = pd.Series(low_values)

                # Convert to numeric values, coercing errors to NaN
                low_series = pd.to_numeric(low_series, errors='coerce')

                # Drop any NaN or infinite values
                low_series = low_series.dropna()
                low_series = low_series[np.isfinite(low_series)]

                if len(low_series) > 0:
                    # Sort low values in descending order for top percentile calculation
                    sorted_low_values = low_series.sort_values(ascending=False).values
                    # Take the top 50% (highest lows) - use ceiling to ensure at least 1 value
                    top_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                    top_half_lows = sorted_low_values[:top_half_count]
                    # Calculate robust average
                    price_levels['minavg_low'] = np.mean(top_half_lows)
                    logger.info(f"Calculated minavg_low from {top_half_count}/{len(low_values)} values: {price_levels['minavg_low']:.6f}")
                else:
                    price_levels['minavg_low'] = None
                    logger.warning("No valid low values after data cleaning for minavg_low")
            else:
                price_levels['minavg_low'] = None

            # Calculate true average (mean) of all highs - State-of-the-art implementation
            if len(high_values) > 0:
                # Convert to pandas Series for robust data validation
                high_series = pd.Series(high_values)

                # Convert to numeric values, coercing errors to NaN
                high_series = pd.to_numeric(high_series, errors='coerce')

                # Drop any NaN or infinite values for robust calculation
                high_series = high_series.dropna()
                high_series = high_series[np.isfinite(high_series)]

                if len(high_series) > 0:
                    # Calculate the arithmetic mean with high precision
                    true_avg_high = np.mean(high_series.values)

                    # Calculate additional robust statistics for validation
                    std_high = np.std(high_series.values, ddof=1)  # Sample standard deviation
                    sem_high = std_high / np.sqrt(len(high_series))  # Standard error of mean

                    logger.info(f"HIGH AVERAGE: {true_avg_high:.6f} ± {sem_high:.6f} (n={len(high_series)}, σ={std_high:.6f})")

                    # Store for use in the white line labels
                    price_levels['true_avg_high'] = true_avg_high
                else:
                    logger.warning("No valid high values after data cleaning for average calculation")
                    price_levels['true_avg_high'] = None
            else:
                price_levels['true_avg_high'] = None

            # Calculate true average (mean) of all lows - State-of-the-art implementation
            if len(low_values) > 0:
                # Convert to pandas Series for robust data validation
                low_series = pd.Series(low_values)

                # Convert to numeric values, coercing errors to NaN
                low_series = pd.to_numeric(low_series, errors='coerce')

                # Drop any NaN or infinite values for robust calculation
                low_series = low_series.dropna()
                low_series = low_series[np.isfinite(low_series)]

                if len(low_series) > 0:
                    # Calculate the arithmetic mean with high precision
                    true_avg_low = np.mean(low_series.values)

                    # Calculate additional robust statistics for validation
                    std_low = np.std(low_series.values, ddof=1)  # Sample standard deviation
                    sem_low = std_low / np.sqrt(len(low_series))  # Standard error of mean

                    logger.info(f"LOW AVERAGE: {true_avg_low:.6f} ± {sem_low:.6f} (n={len(low_series)}, σ={std_low:.6f})")

                    # Store for use in the white line labels
                    price_levels['true_avg_low'] = true_avg_low
                else:
                    logger.warning("No valid low values after data cleaning for average calculation")
                    price_levels['true_avg_low'] = None
            else:
                price_levels['true_avg_low'] = None

            # Calculate median of all highs (long median) - Custom implementation for high median
            if len(high_values) > 0:
                # Convert to pandas Series for robust data validation
                high_series = pd.Series(high_values)

                # Convert to numeric values, coercing errors to NaN
                high_series = pd.to_numeric(high_series, errors='coerce')

                # Drop any NaN or infinite values for robust calculation
                high_series = high_series.dropna()
                high_series = high_series[np.isfinite(high_series)]

                if len(high_series) > 0:
                    # Custom median calculation for high values:
                    # - Odd count: returns middle value
                    # - Even count: returns the HIGHER of the two middle values (not average)
                    sorted_values = np.sort(high_series.values)
                    n = len(sorted_values)

                    if n % 2 == 1:
                        # Odd count - middle element
                        median_value = sorted_values[n // 2]
                        logger.info(f"High median (odd count): {median_value:.6f}")
                    else:
                        # Even count - use the HIGHER of the two middle elements
                        mid1, mid2 = sorted_values[n // 2 - 1], sorted_values[n // 2]
                        median_value = mid2  # Use the higher value
                        logger.info(f"High median (even count): {median_value:.6f} (higher of {mid1:.6f} and {mid2:.6f})")

                    # Store the median value
                    price_levels['avg_high_lowest_high'] = median_value

                    # Enhanced logging with statistical validation
                    logger.info(f"HIGH VALUES: count={n}, min={sorted_values[0]:.6f}, max={sorted_values[-1]:.6f}")
                    logger.info(f"HIGH MEDIAN (custom): {median_value:.6f}")
                    logger.info(f"MEAN: {np.mean(sorted_values):.6f}")
                    logger.info(f"STD: {np.std(sorted_values, ddof=1):.6f}")

                    # Compare with true average calculation
                    if price_levels['true_avg_high'] is not None:
                        diff = median_value - price_levels['true_avg_high']
                        logger.info(f"Median-Mean difference: {diff:.6f} ({diff/price_levels['true_avg_high']*100:.2f}%)")
                else:
                    logger.warning("No valid high values after data cleaning for median calculation")
                    price_levels['avg_high_lowest_high'] = None
            else:
                price_levels['avg_high_lowest_high'] = None

            # Calculate median of all lows (short median) - Custom implementation for low median
            if len(low_values) > 0:
                # Convert to pandas Series for robust data validation
                low_series = pd.Series(low_values)

                # Convert to numeric values, coercing errors to NaN
                low_series = pd.to_numeric(low_series, errors='coerce')

                # Drop any NaN or infinite values for robust calculation
                low_series = low_series.dropna()
                low_series = low_series[np.isfinite(low_series)]

                if len(low_series) > 0:
                    # Custom median calculation for low values:
                    # - Odd count: returns middle value
                    # - Even count: returns the LOWER of the two middle values (not average)
                    sorted_values = np.sort(low_series.values)
                    n = len(sorted_values)

                    if n % 2 == 1:
                        # Odd count - middle element
                        median_value = sorted_values[n // 2]
                        logger.info(f"Low median (odd count): {median_value:.6f}")
                    else:
                        # Even count - use the LOWER of the two middle elements
                        mid1, mid2 = sorted_values[n // 2 - 1], sorted_values[n // 2]
                        median_value = mid1  # Use the lower value
                        logger.info(f"Low median (even count): {median_value:.6f} (lower of {mid1:.6f} and {mid2:.6f})")

                    # Store the median value
                    price_levels['avg_low_highest_low'] = median_value

                    # Enhanced logging with statistical validation
                    logger.info(f"LOW VALUES: count={n}, min={sorted_values[0]:.6f}, max={sorted_values[-1]:.6f}")
                    logger.info(f"LOW MEDIAN (custom): {median_value:.6f}")
                    logger.info(f"MEAN: {np.mean(sorted_values):.6f}")
                    logger.info(f"STD: {np.std(sorted_values, ddof=1):.6f}")

                    # Compare with true average calculation
                    if price_levels['true_avg_low'] is not None:
                        diff = median_value - price_levels['true_avg_low']
                        logger.info(f"Median-Mean difference: {diff:.6f} ({diff/price_levels['true_avg_low']*100:.2f}%)")
                else:
                    logger.warning("No valid low values after data cleaning for median calculation")
                    price_levels['avg_low_highest_low'] = None
            else:
                price_levels['avg_low_highest_low'] = None

            # Calculate apex (average of max/highest for projected high and max/lowest of projected low)
            # Use the same high/low values that were used for the other calculations
            if len(high_values) > 0 and len(low_values) > 0:
                # For apex calculation:
                # - Use the maximum (highest) value from the high_values
                # - Use the minimum (lowest) value from the low_values
                # These are already the correct values (either regular High/Low or Projected High/Low)
                max_high_value = np.max(high_values)  # Max/highest for projected high
                min_low_value = np.min(low_values)    # Max/lowest (minimum) of projected low
                price_levels['apex'] = (max_high_value + min_low_value) / 2
                logger.info(f"Calculated apex from max high value ({max_high_value}) and min low value ({min_low_value}): {price_levels['apex']}")
            else:
                price_levels['apex'] = None
                logger.warning("Could not calculate apex - no high/low values available")

            # Calculate median between apex and highest high
            if price_levels['apex'] is not None and len(high_values) > 0:
                highest_value = np.max(high_values)
                price_levels['median_apex_highest_high'] = (price_levels['apex'] + highest_value) / 2
                logger.info(f"Calculated median between apex ({price_levels['apex']}) and highest high ({highest_value}): {price_levels['median_apex_highest_high']}")
            else:
                price_levels['median_apex_highest_high'] = None

            # Calculate median between apex and lowest low
            if price_levels['apex'] is not None and len(low_values) > 0:
                lowest_value = np.min(low_values)
                price_levels['median_apex_lowest_low'] = (price_levels['apex'] + lowest_value) / 2
                logger.info(f"Calculated median between apex ({price_levels['apex']}) and lowest low ({lowest_value}): {price_levels['median_apex_lowest_low']}")
            else:
                price_levels['median_apex_lowest_low'] = None

            # Add wall_long and wall_short values
            if price_levels['maxavg_high'] is not None:
                price_levels['wall_long'] = price_levels['maxavg_high']
            else:
                price_levels['wall_long'] = None

            if price_levels['maxavg_low'] is not None:
                price_levels['wall_short'] = price_levels['maxavg_low']
            else:
                price_levels['wall_short'] = None

            logger.info(f"Calculated price levels: {price_levels}")
            return price_levels

        except Exception as e:
            logger.error(f"Error calculating price levels: {str(e)}", exc_info=True)
            return {}

    def get_reference_price(self):
        """
        Get the appropriate reference price based on the calculation mode.

        Returns:
            float: The reference price value or None if not available
        """
        try:
            # Try to get the market odds tab from the parent
            market_odds_tab = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab
                logger.info("Retrieved market odds tab from data tab")

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        logger.info("Retrieved market odds tab from parent hierarchy")
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # If we found the market odds tab, get the current price
            if market_odds_tab is not None:
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Check if we're viewing historical data and should use historical cutoff
                    if hasattr(market_odds_tab, 'historical_cutoff_index'):
                        historical_cutoff_index = market_odds_tab.historical_cutoff_index
                        current_price = market_odds_tab.data['Close'].iloc[historical_cutoff_index]
                        logger.info(f"Using historical reference price (index {historical_cutoff_index}): {current_price}")
                        return current_price
                    else:
                        current_price = market_odds_tab.data['Close'].iloc[-1]
                        logger.info(f"Using current price as reference: {current_price}")
                        return current_price

            logger.warning("Could not determine reference price, using None")
            return None

        except Exception as e:
            logger.error(f"Error getting reference price: {str(e)}", exc_info=True)
            return None

    def get_volatility_levels(self):
        """
        Get the latest calculated volatility levels.
        If AK's weekly vol zones is active, returns the combined AK levels.
        Otherwise returns normal volatility levels.

        Returns:
            dict: Dictionary containing the volatility levels with keys:
                - 'highest_high': Max level
                - 'maxavg_high': MaxAvg high level
                - 'avg_high_lowest_high': High Median level
                - 'minavg_high': MinAvg high level
                - 'true_avg_high': Avg high level
                - 'apex': Apex level
                - 'true_avg_low': Avg low level
                - 'minavg_low': MinAvg low level
                - 'avg_low_highest_low': Low Median level
                - 'maxavg_low': MaxAvg low level
                - 'lowest_low': Max low level
        """
        if self.ak_weekly_vol_zones_active and (self.ak_fwl5_levels or self.ak_weekday_levels):
            # Return AK's weekly vol zones levels - combine ALL levels from both calculations
            combined_levels = {}

            # Add H/L FWL5 levels with prefix to distinguish them
            if self.ak_fwl5_levels:
                for key, value in self.ak_fwl5_levels.items():
                    if value is not None and key not in ['high_values', 'low_values']:
                        # Use original key names for H/L FWL5 levels
                        combined_levels[key] = value

            # Add weekday FWL5 levels with prefix to distinguish them
            if self.ak_weekday_levels:
                for key, value in self.ak_weekday_levels.items():
                    if value is not None and key not in ['high_values', 'low_values']:
                        # Add weekday levels with 'weekday_' prefix to avoid conflicts
                        weekday_key = f"weekday_{key}"
                        combined_levels[weekday_key] = value



            logger.info(f"Returning AK's weekly vol zones levels: {len(combined_levels)} total levels")
            logger.info(f"H/L FWL5 levels: {len([k for k in combined_levels.keys() if not k.startswith('weekday_')])}")
            logger.info(f"Weekday FWL5 levels: {len([k for k in combined_levels.keys() if k.startswith('weekday_')])}")
            return combined_levels.copy()
        else:
            # Return normal volatility levels (empty if AK mode is active but no levels calculated)
            if self.ak_weekly_vol_zones_active:
                logger.info("AK's weekly vol zones active but no levels calculated, returning empty")
                return {}
            else:
                return self.latest_price_levels.copy()

    def generate_volatility_graph(self):
        """Generate volatility graph with dots at 3x axis for various price levels."""
        try:
            # Clear the plot and reset crosshair
            self.plot_widget.clear()
            self.crosshair = None

            # Clear parent's statistics box if available
            if hasattr(self.parent, 'clear_statistics_box'):
                self.parent.clear_statistics_box()

            # Get data from the Data tab
            data = self.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available from data tab")
                self.status_label.setText("Volatility Graph - No data available")
                return

            # Get the matching mode from the parent (Volatility_Statistics_tab)
            matching_mode = 'hl'  # Default to H/L matching
            if hasattr(self.parent, 'get_matching_mode'):
                matching_mode = self.parent.get_matching_mode()
                logger.info(f"Using matching mode: {matching_mode}")

            # Get matching rows based on the selected mode
            if matching_mode == 'weekday':
                # Use weekday matching
                matching_rows = self.get_rows_with_same_weekday(data)
                if matching_rows.empty:
                    logger.warning("No rows found with the same weekday")
                    self.status_label.setText("Volatility Graph - No rows found with the same weekday")
                    return

                # Get the weekday name for display
                # Find the date column or use the first column as a fallback
                date_column = None
                for col in data.columns:
                    if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                        date_column = col
                        break

                if date_column is None:
                    # Use the index if it's a DatetimeIndex
                    if isinstance(data.index, pd.DatetimeIndex):
                        latest_date = data.index[-1]
                    else:
                        # Use the first column as a last resort
                        try:
                            latest_date = pd.to_datetime(data.iloc[-1, 0])
                        except:
                            # If all else fails, use current date
                            import datetime
                            latest_date = datetime.datetime.now()
                else:
                    # Use the identified date column
                    try:
                        latest_date = pd.to_datetime(data[date_column].iloc[-1])
                    except:
                        # If conversion fails, use current date
                        import datetime
                        latest_date = datetime.datetime.now()

                weekday_name = latest_date.strftime('%A')
                latest_category = weekday_name  # Use weekday name as the category for display

                logger.info(f"Using weekday matching for {weekday_name}")
            else:
                # Use H/L category matching (default)
                # Get the latest category
                latest_category = self.get_latest_category(data)
                if latest_category is None:
                    logger.warning("No category found in latest row")
                    self.status_label.setText("Volatility Graph - No category found in latest row")
                    return

                logger.info(f"Latest category: {latest_category}")

                # Get rows with the same category
                matching_rows = self.get_rows_with_same_category(data, latest_category)
                if matching_rows.empty:
                    logger.warning(f"No rows found with category '{latest_category}'")
                    self.status_label.setText(f"Volatility Graph - No rows found with category '{latest_category}'")
                    return

            # Log information about the matching rows
            logger.info(f"MATCHING ROWS COUNT: {len(matching_rows)}")
            logger.info(f"MATCHING ROWS COLUMNS: {matching_rows.columns.tolist()}")
            logger.info(f"MATCHING ROWS SAMPLE: {matching_rows.head().to_dict()}")

            # Calculate price levels
            price_levels = self.calculate_price_levels(matching_rows)
            if not price_levels:
                logger.warning("Could not calculate price levels")
                self.status_label.setText("Volatility Graph - Could not calculate price levels")
                return

            # Log the calculated price levels
            logger.info(f"CALCULATED PRICE LEVELS: {price_levels}")

            # Store the price levels for external access
            self.latest_price_levels = price_levels.copy()

            # Get reference price (current price)
            reference_price = self.get_reference_price()

            # Calculate min and max values for y-axis
            all_values = []
            # Only include scalar values, not lists or arrays
            for key, value in price_levels.items():
                if value is not None and key not in ['high_values', 'low_values'] and not isinstance(value, (list, tuple, np.ndarray)):
                    all_values.append(value)

            if reference_price is not None:
                all_values.append(reference_price)

            if not all_values:
                logger.warning("No price values available for plotting")
                self.status_label.setText("Volatility Graph - No price values available for plotting")
                return

            min_val = min(all_values)
            max_val = max(all_values)

            # Calculate padding for y-axis - fixed at 5% above highest high and 5% below lowest low
            y_range = max_val - min_val
            padding_percentage = 0.05  # 5%
            padding_high = max_val * padding_percentage
            padding_low = min_val * padding_percentage
            # For display purposes, we'll still use a padding variable for label positioning
            padding = y_range * 0.005 if y_range > 0 else 0.5

            # Add white connecting lines FIRST (behind everything else)
            # These lines connect labels to the 2x axis and should be drawn before box and vertical line

            # Add white line from Apex label to 2x axis (if apex exists)
            if price_levels.get('apex') is not None:
                apex_line = pg.PlotDataItem(
                    x=[1.0, 2.0],
                    y=[price_levels['apex'], price_levels['apex']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(apex_line)

            # Add white line from Max high label to 2x axis (if highest_high exists)
            if price_levels.get('highest_high') is not None:
                max_high_line = pg.PlotDataItem(
                    x=[2.75, 2.0],
                    y=[price_levels['highest_high'], price_levels['highest_high']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(max_high_line)

            # Add white line from Max low label to 2x axis (if lowest_low exists)
            if price_levels.get('lowest_low') is not None:
                max_low_line = pg.PlotDataItem(
                    x=[2.75, 2.0],
                    y=[price_levels['lowest_low'], price_levels['lowest_low']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(max_low_line)

            # Add white line from High Median label to 2x axis (if avg_high_lowest_high exists)
            if price_levels.get('avg_high_lowest_high') is not None:
                high_median_line = pg.PlotDataItem(
                    x=[2.75, 2.0],
                    y=[price_levels['avg_high_lowest_high'], price_levels['avg_high_lowest_high']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(high_median_line)

            # Add white line from Low Median label to 2x axis (if avg_low_highest_low exists)
            if price_levels.get('avg_low_highest_low') is not None:
                low_median_line = pg.PlotDataItem(
                    x=[2.75, 2.0],
                    y=[price_levels['avg_low_highest_low'], price_levels['avg_low_highest_low']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(low_median_line)

            # Add MaxAvg horizontal lines BEHIND labels (if maxavg values exist)
            if price_levels.get('maxavg_high') is not None:
                maxavg_high_line = pg.PlotDataItem(
                    x=[2.5, 3.4],
                    y=[price_levels['maxavg_high'], price_levels['maxavg_high']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(maxavg_high_line)

            if price_levels.get('maxavg_low') is not None:
                maxavg_low_line = pg.PlotDataItem(
                    x=[2.5, 3.4],
                    y=[price_levels['maxavg_low'], price_levels['maxavg_low']],
                    pen=pg.mkPen(color='white', width=1),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(maxavg_low_line)

            # Add horizontal line for current price if available
            if reference_price is not None:
                # Create a white dotted line from x=0 to x=3.45 for current price
                try:
                    # Create a straight white dotted line
                    line = pg.PlotDataItem(
                        x=[0, 3.45],
                        y=[reference_price, reference_price],
                        pen=pg.mkPen(color='white', width=2, style=QtCore.Qt.PenStyle.DotLine),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(line)

                    # Add label for current price with white fill and black text
                    current_price_label = pg.TextItem(
                        text=f"Current Price: {reference_price:.2f}",
                        color='black',
                        fill='white',
                        anchor=(0, 0.5)  # Left-center aligned
                    )
                    current_price_label.setPos(0.1, reference_price)  # Position at left side
                    self.plot_widget.addItem(current_price_label)
                except Exception as e:
                    logger.warning(f"Failed to create current price line: {str(e)}")

            # Add vertical line at 2x axis connecting both Max labels (highest high and lowest low) - BEHIND labels
            if (price_levels.get('highest_high') is not None and
                price_levels.get('lowest_low') is not None and
                reference_price is not None):
                try:
                    # Create green line above current price (from current price to highest high)
                    if price_levels['highest_high'] > reference_price:
                        green_line = pg.PlotDataItem(
                            x=[2.0, 2.0],
                            y=[reference_price, price_levels['highest_high']],
                            pen=pg.mkPen(color='green', width=3),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(green_line)

                    # Create red line below current price (from current price to lowest low)
                    if price_levels['lowest_low'] < reference_price:
                        red_line = pg.PlotDataItem(
                            x=[2.0, 2.0],
                            y=[reference_price, price_levels['lowest_low']],
                            pen=pg.mkPen(color='red', width=3),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(red_line)

                except Exception as e:
                    logger.warning(f"Failed to create vertical line at 2x axis: {str(e)}")



            # Add box between 1.75x and 2.25x with maxavg high/low as boundaries
            if (price_levels.get('maxavg_high') is not None and
                price_levels.get('maxavg_low') is not None and
                reference_price is not None):
                try:
                    # Determine box color based on position relative to current price
                    maxavg_high = price_levels['maxavg_high']
                    maxavg_low = price_levels['maxavg_low']

                    # Create split-filled box: green above current price, red below current price
                    # Using PlotDataItem with fillLevel for better visibility

                    # Create green fill above current price (if any)
                    if maxavg_high > reference_price:
                        green_top = maxavg_high
                        green_bottom = max(maxavg_low, reference_price)  # Don't go below current price

                        # Create filled area using PlotDataItem with fillLevel
                        green_x = [1.75, 2.25, 2.25, 1.75, 1.75]
                        green_y = [green_bottom, green_bottom, green_top, green_top, green_bottom]

                        green_fill = pg.PlotDataItem(
                            x=green_x,
                            y=green_y,
                            pen=None,
                            brush=pg.mkBrush(color='green', alpha=76),  # 30% of 255
                            fillLevel=green_bottom
                        )
                        self.plot_widget.addItem(green_fill)

                    # Create red fill below current price (if any)
                    if maxavg_low < reference_price:
                        red_top = min(maxavg_high, reference_price)  # Don't go above current price
                        red_bottom = maxavg_low

                        # Create filled area using PlotDataItem with fillLevel
                        red_x = [1.75, 2.25, 2.25, 1.75, 1.75]
                        red_y = [red_bottom, red_bottom, red_top, red_top, red_bottom]

                        red_fill = pg.PlotDataItem(
                            x=red_x,
                            y=red_y,
                            pen=None,
                            brush=pg.mkBrush(color='red', alpha=76),  # 30% of 255
                            fillLevel=red_bottom
                        )
                        self.plot_widget.addItem(red_fill)

                    # Add horizontal white lines for Avg and MinAvg to box borders (above box fills, below borders)
                    # These lines extend from the labels at 2x to the box borders at 1.75x and 2.25x

                    # Add Avg lines to box borders (if avg values exist)
                    if price_levels.get('true_avg_high') is not None:
                        # Left line from 2x to left box border
                        avg_high_left_line = pg.PlotDataItem(
                            x=[2.0, 1.75],
                            y=[price_levels['true_avg_high'], price_levels['true_avg_high']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(avg_high_left_line)

                        # Right line from 2x to right box border
                        avg_high_right_line = pg.PlotDataItem(
                            x=[2.0, 2.25],
                            y=[price_levels['true_avg_high'], price_levels['true_avg_high']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(avg_high_right_line)

                    if price_levels.get('true_avg_low') is not None:
                        # Left line from 2x to left box border
                        avg_low_left_line = pg.PlotDataItem(
                            x=[2.0, 1.75],
                            y=[price_levels['true_avg_low'], price_levels['true_avg_low']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(avg_low_left_line)

                        # Right line from 2x to right box border
                        avg_low_right_line = pg.PlotDataItem(
                            x=[2.0, 2.25],
                            y=[price_levels['true_avg_low'], price_levels['true_avg_low']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(avg_low_right_line)

                    # Add MinAvg lines to box borders (if minavg values exist)
                    if price_levels.get('minavg_high') is not None:
                        # Left line from 2x to left box border
                        minavg_high_left_line = pg.PlotDataItem(
                            x=[2.0, 1.75],
                            y=[price_levels['minavg_high'], price_levels['minavg_high']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(minavg_high_left_line)

                        # Right line from 2x to right box border
                        minavg_high_right_line = pg.PlotDataItem(
                            x=[2.0, 2.25],
                            y=[price_levels['minavg_high'], price_levels['minavg_high']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(minavg_high_right_line)

                    if price_levels.get('minavg_low') is not None:
                        # Left line from 2x to left box border
                        minavg_low_left_line = pg.PlotDataItem(
                            x=[2.0, 1.75],
                            y=[price_levels['minavg_low'], price_levels['minavg_low']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(minavg_low_left_line)

                        # Right line from 2x to right box border
                        minavg_low_right_line = pg.PlotDataItem(
                            x=[2.0, 2.25],
                            y=[price_levels['minavg_low'], price_levels['minavg_low']],
                            pen=pg.mkPen(color='white', width=2),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(minavg_low_right_line)

                    # Add black border lines
                    # Top border
                    top_border = pg.PlotDataItem(
                        x=[1.75, 2.25],
                        y=[maxavg_high, maxavg_high],
                        pen=pg.mkPen(color='black', width=2)
                    )
                    self.plot_widget.addItem(top_border)

                    # Bottom border
                    bottom_border = pg.PlotDataItem(
                        x=[1.75, 2.25],
                        y=[maxavg_low, maxavg_low],
                        pen=pg.mkPen(color='black', width=2)
                    )
                    self.plot_widget.addItem(bottom_border)

                    # Left border
                    left_border = pg.PlotDataItem(
                        x=[1.75, 1.75],
                        y=[maxavg_low, maxavg_high],
                        pen=pg.mkPen(color='black', width=2)
                    )
                    self.plot_widget.addItem(left_border)

                    # Right border
                    right_border = pg.PlotDataItem(
                        x=[2.25, 2.25],
                        y=[maxavg_low, maxavg_high],
                        pen=pg.mkPen(color='black', width=2)
                    )
                    self.plot_widget.addItem(right_border)

                except Exception as e:
                    logger.warning(f"Failed to create maxavg box: {str(e)}")



            # We'll keep track of the positions for labels but make dots invisible
            dot_size = 0  # Set dot size to 0 to make them invisible

            # Dictionary to store the dots for each price level
            dots = {}

            # Add dot for highest high
            if price_levels.get('highest_high') is not None:
                dots['highest_high'] = pg.ScatterPlotItem(
                    [3], [price_levels['highest_high']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['highest_high']),
                    brush=pg.mkBrush(self.chart_colors['highest_high'])
                )
                self.plot_widget.addItem(dots['highest_high'])

                # Add label for highest high (Max) with white fill and black text at 2.75x axis
                highest_high_label = pg.TextItem(
                    text=f"Max: {price_levels['highest_high']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                highest_high_label.setPos(2.75, price_levels['highest_high'])
                self.plot_widget.addItem(highest_high_label)

            # Add dot for lowest low
            if price_levels.get('lowest_low') is not None:
                dots['lowest_low'] = pg.ScatterPlotItem(
                    [3], [price_levels['lowest_low']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['lowest_low']),
                    brush=pg.mkBrush(self.chart_colors['lowest_low'])
                )
                self.plot_widget.addItem(dots['lowest_low'])

                # Add label for lowest low (Max) with white fill and black text at 2.75x axis
                lowest_low_label = pg.TextItem(
                    text=f"Max: {price_levels['lowest_low']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                lowest_low_label.setPos(2.75, price_levels['lowest_low'])
                self.plot_widget.addItem(lowest_low_label)

            # Add dot for maxavg high (average of top 50% highs)
            if price_levels.get('maxavg_high') is not None:
                dots['maxavg_high'] = pg.ScatterPlotItem(
                    [3], [price_levels['maxavg_high']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['average_high']),
                    brush=pg.mkBrush(self.chart_colors['average_high'])
                )
                self.plot_widget.addItem(dots['maxavg_high'])

                # Add label for maxavg high (MaxAvg) with white fill and black text at 2.5x axis
                maxavg_high_label = pg.TextItem(
                    text=f"MaxAvg: {price_levels['maxavg_high']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                maxavg_high_label.setPos(2.5, price_levels['maxavg_high'])
                self.plot_widget.addItem(maxavg_high_label)

            # Add dot for maxavg low (average of top 50% lowest lows)
            if price_levels.get('maxavg_low') is not None:
                dots['maxavg_low'] = pg.ScatterPlotItem(
                    [3], [price_levels['maxavg_low']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['average_low']),
                    brush=pg.mkBrush(self.chart_colors['average_low'])
                )
                self.plot_widget.addItem(dots['maxavg_low'])

                # Add label for maxavg low (MaxAvg) with white fill and black text at 2.5x axis
                maxavg_low_label = pg.TextItem(
                    text=f"MaxAvg: {price_levels['maxavg_low']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                maxavg_low_label.setPos(2.5, price_levels['maxavg_low'])
                self.plot_widget.addItem(maxavg_low_label)

            # Add connector lines connecting MaxAvg high and low lines
            if (price_levels.get('maxavg_high') is not None and
                price_levels.get('maxavg_low') is not None):
                try:
                    maxavg_high = price_levels['maxavg_high']
                    maxavg_low = price_levels['maxavg_low']
                    maxavg_mid = (maxavg_high + maxavg_low) / 2

                    # Add connecting line from high line to midpoint
                    high_connector = pg.PlotDataItem(
                        x=[3.4, 3.45],
                        y=[maxavg_high, maxavg_mid],
                        pen=pg.mkPen(color='white', width=1),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(high_connector)

                    # Add connecting line from low line to midpoint
                    low_connector = pg.PlotDataItem(
                        x=[3.4, 3.45],
                        y=[maxavg_low, maxavg_mid],
                        pen=pg.mkPen(color='white', width=1),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(low_connector)

                    # Add label at the midpoint/connection point
                    volatility_label = pg.TextItem(
                        text="75% Volatility",
                        color='black',
                        fill='white',
                        anchor=(0, 0.5)  # Left-center aligned
                    )
                    # Position the label at the convergence point
                    volatility_label.setPos(3.45, maxavg_mid)
                    self.plot_widget.addItem(volatility_label)

                except Exception as e:
                    logger.warning(f"Failed to create MaxAvg connector lines: {str(e)}")

            # We still calculate average_high and average_low values but don't display them
            # They're used in other calculations but not shown on the graph to avoid duplication

            # Add dot for minavg high (average of bottom 50% highs/lowest highs)
            if price_levels.get('minavg_high') is not None:
                dots['minavg_high'] = pg.ScatterPlotItem(
                    [3], [price_levels['minavg_high']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['lowest_high']),
                    brush=pg.mkBrush(self.chart_colors['lowest_high'])
                )
                self.plot_widget.addItem(dots['minavg_high'])

                # Add label for minavg high (MinAvg) with white fill and black text at 2x axis
                minavg_high_label = pg.TextItem(
                    text=f"MinAvg: {price_levels['minavg_high']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0.5, 0.5)  # Center-center aligned
                )
                minavg_high_label.setPos(2.0, price_levels['minavg_high'])
                self.plot_widget.addItem(minavg_high_label)

            # Add dot for minavg low (average of bottom 50% of lows/highest lows)
            if price_levels.get('minavg_low') is not None:
                dots['minavg_low'] = pg.ScatterPlotItem(
                    [3], [price_levels['minavg_low']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['highest_low']),
                    brush=pg.mkBrush(self.chart_colors['highest_low'])
                )
                self.plot_widget.addItem(dots['minavg_low'])

                # Add label for minavg low (MinAvg) with white fill and black text at 2x axis
                minavg_low_label = pg.TextItem(
                    text=f"MinAvg: {price_levels['minavg_low']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0.5, 0.5)  # Center-center aligned
                )
                minavg_low_label.setPos(2.0, price_levels['minavg_low'])
                self.plot_widget.addItem(minavg_low_label)

            # We still calculate lowest_high and highest_low values but don't display them
            # They're used in other calculations but not shown on the graph

            # Add dot for true average of all highs
            if price_levels.get('avg_high_lowest_high') is not None:
                dots['avg_high_lowest_high'] = pg.ScatterPlotItem(
                    [3], [price_levels['true_avg_high']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['avg_high_lowest_high']),
                    brush=pg.mkBrush(self.chart_colors['avg_high_lowest_high'])
                )
                self.plot_widget.addItem(dots['avg_high_lowest_high'])

                # Add label for true average of all highs (Avg) with white fill and black text at 2x axis
                avg_high_lowest_high_label = pg.TextItem(
                    text=f"Avg: {price_levels['true_avg_high']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0.5, 0.5)  # Center-center aligned
                )
                avg_high_lowest_high_label.setPos(2.0, price_levels['true_avg_high'])
                self.plot_widget.addItem(avg_high_lowest_high_label)

            # Add dot for true average of all lows
            if price_levels.get('avg_low_highest_low') is not None:
                dots['avg_low_highest_low'] = pg.ScatterPlotItem(
                    [3], [price_levels['true_avg_low']],
                    size=dot_size,
                    pen=pg.mkPen(self.chart_colors['avg_low_highest_low']),
                    brush=pg.mkBrush(self.chart_colors['avg_low_highest_low'])
                )
                self.plot_widget.addItem(dots['avg_low_highest_low'])

                # Add label for true average of all lows (Avg) with white fill and black text at 2x axis
                avg_low_highest_low_label = pg.TextItem(
                    text=f"Avg: {price_levels['true_avg_low']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0.5, 0.5)  # Center-center aligned
                )
                avg_low_highest_low_label.setPos(2.0, price_levels['true_avg_low'])
                self.plot_widget.addItem(avg_low_highest_low_label)

            # Add invisible dot for apex (median between highest high and lowest low) at x=1
            if price_levels.get('apex') is not None:
                # We're keeping the dot object but with size 0 to make it invisible
                dots['apex'] = pg.ScatterPlotItem(
                    [1], [price_levels['apex']],
                    size=0,  # Set size to 0 to make it invisible
                    pen=pg.mkPen(self.chart_colors['apex']),
                    brush=pg.mkBrush(self.chart_colors['apex'])
                )
                self.plot_widget.addItem(dots['apex'])

                # Add label for apex with white fill and black text
                apex_label = pg.TextItem(
                    text=f"Apex: {price_levels['apex']:.2f}",
                    color='black',
                    fill='white',
                    anchor=(0, 0.5)  # Left-center aligned
                )
                apex_label.setPos(1.0, price_levels['apex'])
                self.plot_widget.addItem(apex_label)



            # Add invisible dot for median between apex and highest high at x=2
            if price_levels.get('median_apex_highest_high') is not None:
                # We're keeping the dot object but with size 0 to make it invisible
                dots['median_apex_highest_high'] = pg.ScatterPlotItem(
                    [2], [price_levels['median_apex_highest_high']],
                    size=0,  # Set size to 0 to make it invisible
                    pen=pg.mkPen(self.chart_colors['median_apex_highest_high']),
                    brush=pg.mkBrush(self.chart_colors['median_apex_highest_high'])
                )
                self.plot_widget.addItem(dots['median_apex_highest_high'])



            # Add invisible dot for median between apex and lowest low at x=2
            if price_levels.get('median_apex_lowest_low') is not None:
                # We're keeping the dot object but with size 0 to make it invisible
                dots['median_apex_lowest_low'] = pg.ScatterPlotItem(
                    [2], [price_levels['median_apex_lowest_low']],
                    size=0,  # Set size to 0 to make it invisible
                    pen=pg.mkPen(self.chart_colors['median_apex_lowest_low']),
                    brush=pg.mkBrush(self.chart_colors['median_apex_lowest_low'])
                )
                self.plot_widget.addItem(dots['median_apex_lowest_low'])









            # Add label for high median (keeping only the label, removing all lines)
            if price_levels.get('avg_high_lowest_high') is not None:
                try:
                    # Add label for median of all highs (long median) with white fill and black text at 2.75x axis
                    median_long_label = pg.TextItem(
                        text=f"High Median: {price_levels['avg_high_lowest_high']:.2f}",
                        color='black',
                        fill='white',
                        anchor=(0, 0.5)  # Left-center aligned
                    )
                    # Position the label at 2.75x axis
                    median_long_label.setPos(2.75, price_levels['avg_high_lowest_high'])
                    self.plot_widget.addItem(median_long_label)
                except Exception as e:
                    logger.warning(f"Failed to create high median label: {str(e)}")

            # Add label for low median (keeping only the label, removing all lines)
            if price_levels.get('avg_low_highest_low') is not None:
                try:
                    # Add label for median of all lows (short median) with white fill and black text at 2.75x axis
                    median_short_label = pg.TextItem(
                        text=f"Low Median: {price_levels['avg_low_highest_low']:.2f}",
                        color='black',
                        fill='white',
                        anchor=(0, 0.5)  # Left-center aligned
                    )
                    # Position the label at 2.75x axis
                    median_short_label.setPos(2.75, price_levels['avg_low_highest_low'])
                    self.plot_widget.addItem(median_short_label)
                except Exception as e:
                    logger.warning(f"Failed to create low median label: {str(e)}")

            # Set axis ranges
            self.plot_widget.setXRange(0, 5, padding=0)  # x-axis from 0 to 5 as requested, no padding
            self.plot_widget.setYRange(min_val - padding_low, max_val + padding_high, padding=0)  # y-axis with fixed 5% padding, no additional padding

            # Store the initial view state for reset functionality (only if not already set)
            if self.initial_view_state is None:
                self.initial_view_state = (0, 5, min_val - padding_low, max_val + padding_high)

            # Enable mouse interaction for zooming (already set in init_ui, but ensure it's enabled)
            self.plot_widget.setMouseEnabled(x=True, y=True)
            self.plot_widget.setMenuEnabled(False)  # Still disable context menu

            # Enable mouse wheel zooming but disable rectangular region selection
            view_box = self.plot_widget.getPlotItem().getViewBox()
            view_box.enableAutoRange(enable=False)  # Disable auto-range to maintain manual zoom control
            view_box.setMouseMode(pg.ViewBox.PanMode)  # Use pan mode instead of rect mode to disable left-click zoom

            # Add historical day range line if viewing historical data
            if hasattr(self.parent, 'is_viewing_historical_data') and self.parent.is_viewing_historical_data():
                historical_high, historical_low = self.parent.get_historical_day_range()
                if historical_high is not None and historical_low is not None:
                    try:
                        # Create white vertical line at x=2.35 from historical low to historical high
                        historical_range_line = pg.PlotDataItem(
                            x=[2.35, 2.35],
                            y=[historical_low, historical_high],
                            pen=pg.mkPen(color='white', width=3),
                            symbolPen=None,
                            symbolBrush=None,
                            symbol=None
                        )
                        self.plot_widget.addItem(historical_range_line)

                        # Add labels for the next day's high and low
                        next_day_high_label = pg.TextItem(
                            text=f"Next Day High: {historical_high:.2f}",
                            color='black',
                            fill='white',
                            anchor=(0, 0.5)  # Left-center aligned
                        )
                        next_day_high_label.setPos(2.4, historical_high)
                        self.plot_widget.addItem(next_day_high_label)

                        next_day_low_label = pg.TextItem(
                            text=f"Next Day Low: {historical_low:.2f}",
                            color='black',
                            fill='white',
                            anchor=(0, 0.5)  # Left-center aligned
                        )
                        next_day_low_label.setPos(2.4, historical_low)
                        self.plot_widget.addItem(next_day_low_label)

                        logger.info(f"Added next day range line at x=2.35 from {historical_low:.2f} to {historical_high:.2f}")
                    except Exception as e:
                        logger.warning(f"Failed to create historical day range line: {str(e)}")

            # Add price-only crosshair to the plot
            self.crosshair = add_price_only_crosshair(self.plot_widget, parent=self.parent)

            # Update status label
            occurrences = len(matching_rows)
            total_rows = len(data) if data is not None else 0

            # Get the occurrence count limit if set
            occurrence_limit = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_limit = self.parent.get_occurrence_count()

            # Check if we're using theoretical values
            is_using_theoretical = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                       'Projected High' in matching_rows.columns and
                                       'Projected Low' in matching_rows.columns)

            # Get the matching mode for display
            matching_mode_display = "Weekday Matching" if (hasattr(self.parent, 'get_matching_mode') and self.parent.get_matching_mode() == 'weekday') else "H/L Matching"

            # Add indicators to status message
            theoretical_indicator = " (Using Projected High/Low)" if is_using_theoretical else ""

            # Create status message based on whether occurrence limit is set
            if occurrence_limit > 0:
                # If occurrence limit is set, don't show days to load
                status_message = f"Occurrences: {occurrences} (Limited to {occurrence_limit}) Attribute: {latest_category} - {matching_mode_display}{theoretical_indicator}"
            else:
                # If no occurrence limit, show days to load
                status_message = f"Occurrences: {occurrences} Attribute: {latest_category} days to load: {total_rows} - {matching_mode_display}{theoretical_indicator}"

            self.status_label.setText(status_message)

            # Extract theoretical price information if available
            theoretical_prices = []
            if is_using_theoretical and 'Projected High' in matching_rows.columns and 'Projected Low' in matching_rows.columns:
                try:
                    # Group by Category and Index to get unique entries
                    for idx, row in matching_rows.iterrows():
                        if pd.notna(row.get('Projected High')):
                            theoretical_prices.append({
                                'idx': idx,
                                'type': 'high',
                                'price': float(row['Projected High'])
                            })
                        if pd.notna(row.get('Projected Low')):
                            theoretical_prices.append({
                                'idx': idx,
                                'type': 'low',
                                'price': float(row['Projected Low'])
                            })
                except Exception as e:
                    logger.warning(f"Error extracting theoretical prices: {str(e)}")

            # Store theoretical prices as an attribute for the data subtab
            self.theoretical_prices = theoretical_prices

            # Update the parent's statistics box if available
            if hasattr(self.parent, 'update_statistics_box'):
                # Get ETH zones data from multiple sources
                eth_zones_data = {}

                # First try to get from density graph tab
                if hasattr(self.parent, 'density_graph_tab') and hasattr(self.parent.density_graph_tab, 'eth_zones_data'):
                    eth_zones_data = getattr(self.parent.density_graph_tab, 'eth_zones_data', {})

                # If not found, try to get from parent's stored data
                if not eth_zones_data and hasattr(self.parent, 'eth_zones_data'):
                    eth_zones_data = getattr(self.parent, 'eth_zones_data', {})

                # Store theoretical prices as projected_prices for the data subtab
                self.projected_prices = theoretical_prices

                self.parent.update_statistics_box(price_levels, latest_category, occurrences, theoretical_prices, eth_zones_data=eth_zones_data)

            # Plot toggled statistics if available
            if hasattr(self.parent, 'get_toggled_stats_data'):
                toggled_stats = self.parent.get_toggled_stats_data()
                if toggled_stats:
                    self.plot_toggled_statistics(toggled_stats, reference_price)

        except Exception as e:
            logger.error(f"Error generating volatility graph: {str(e)}", exc_info=True)
            self.status_label.setText(f"Error generating volatility graph: {str(e)}")
            self.plot_widget.clear()
            # Clear parent's statistics box if available
            if hasattr(self.parent, 'clear_statistics_box'):
                self.parent.clear_statistics_box()

    def eventFilter(self, obj, event):
        """Event filter to handle resize events for the plot_widget"""
        if obj == self.plot_widget and event.type() == QtCore.QEvent.Type.Resize:
            # No need to update right_box height anymore
            pass
        return super().eventFilter(obj, event)

    def plot_toggled_statistics(self, toggled_stats, reference_price):
        """Plot toggled statistics on the graph

        Args:
            toggled_stats (list): List of dictionaries with statistics data
            reference_price (float): Current reference price
        """
        try:
            if not toggled_stats:
                return

            logger.info(f"Plotting {len(toggled_stats)} toggled statistics")

            # Define colors for different types of stats
            bear_color = self.chart_colors['bearish']
            bull_color = self.chart_colors['bullish']
            long_color = self.chart_colors.get('long', '#00FF00')  # Green for long theoretical
            short_color = self.chart_colors.get('short', '#FF00FF')  # Magenta for short theoretical

            # Plot each toggled statistic
            for stat in toggled_stats:
                if 'price' not in stat or 'type' not in stat:
                    logger.warning(f"Skipping stat without price or type: {stat}")
                    continue

                price = stat['price']
                stat_type = stat['type']

                # Determine color and label based on type
                if stat_type == 'bear':
                    color = bear_color
                    label_prefix = "Bear"
                elif stat_type == 'bull':
                    color = bull_color
                    label_prefix = "Bull"
                elif stat_type == 'long':
                    color = long_color
                    label_prefix = "Long"
                elif stat_type == 'short':
                    color = short_color
                    label_prefix = "Short"
                else:
                    logger.warning(f"Unknown stat type: {stat_type}")
                    continue

                # Add a label for the statistic with win rate if available (removing all lines)
                winrate_str = stat.get('winrate_str', '')
                label_text = f"{label_prefix}: {price:.2f} ({winrate_str})" if winrate_str else f"{label_prefix}: {price:.2f}"
                label = pg.TextItem(
                    text=label_text,
                    color=color,
                    anchor=(0, 0.5)  # Left-center aligned
                )
                # Position the label at the right edge (x=4.5) at the price level
                label.setPos(4.5, price)
                self.plot_widget.addItem(label)



            logger.info("Successfully plotted toggled statistics")
        except Exception as e:
            logger.error(f"Error plotting toggled statistics: {str(e)}", exc_info=True)

    def zoom_in(self):
        """Zoom in on the plot by a factor of 0.8 (reducing the visible range by 20%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (80% of current range)
        x_range = (x_max - x_min) * 0.8
        y_range = (y_max - y_min) * 0.8

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Volatility Graph - Zoomed in")

    def zoom_out(self):
        """Zoom out on the plot by a factor of 1.25 (increasing the visible range by 25%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (125% of current range)
        x_range = (x_max - x_min) * 1.25
        y_range = (y_max - y_min) * 1.25

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Volatility Graph - Zoomed out")

    def reset_zoom(self):
        """Reset the zoom to 0x-4x view to show all main elements."""
        # Always reset to 0x-4x range to show all main volatility elements
        self.plot_widget.setXRange(0, 4)  # X-axis range from 0 to 4

        # Keep the Y-axis range from initial view state if available, otherwise use current range
        if self.initial_view_state:
            _, _, y_min, y_max = self.initial_view_state
            self.plot_widget.setYRange(y_min, y_max)
        else:
            # If initial view state is not set, keep current Y range or use reasonable default
            current_y_range = self.plot_widget.getViewBox().viewRange()[1]
            if current_y_range[0] != current_y_range[1]:  # Valid range exists
                self.plot_widget.setYRange(current_y_range[0], current_y_range[1])
            else:
                self.plot_widget.setYRange(-10, 10)  # Fallback default

        # Update status
        self.status_label.setText(f"Volatility Graph - Reset to 0x-4x view")

    def get_data_tab(self):
        """Get the Data tab reference."""
        return self.data_tab

    def get_density_graph_tab(self):
        """Get the density graph tab reference."""
        try:
            # Try to find the density graph tab through the parent
            if hasattr(self, 'parent') and self.parent is not None:
                if hasattr(self.parent, 'density_graph_tab'):
                    return self.parent.density_graph_tab
            return None
        except Exception as e:
            logger.error(f"Error getting density graph tab: {str(e)}", exc_info=True)
            return None

    def get_main_volatility_tab(self):
        """Get the main volatility statistics tab reference."""
        try:
            # The parent should be the main Volatility Statistics tab
            if hasattr(self, 'parent') and self.parent is not None:
                return self.parent
            return None
        except Exception as e:
            logger.error(f"Error getting main volatility tab: {str(e)}", exc_info=True)
            return None

    def update_candlestick_volatility_levels(self):
        """Trigger update of volatility levels on candlestick charts"""
        try:
            # Find and update all candlestick charts
            for widget in QtWidgets.QApplication.topLevelWidgets():
                if hasattr(widget, 'update_chart'):
                    # This could be a candlestick chart
                    widget.update_chart()
                    logger.info("Updated candlestick chart volatility levels")
        except Exception as e:
            logger.error(f"Error updating candlestick volatility levels: {str(e)}", exc_info=True)




