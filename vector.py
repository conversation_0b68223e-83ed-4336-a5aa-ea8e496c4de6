import pandas as pd
import matplotlib.pyplot as plt

# Import LRUCache if available, otherwise define a simple cache
try:
    from lru_cache import LRUCache
except ImportError:
    # Simple LRU cache implementation if the external one is not available
    class LRUCache:
        def __init__(self, capacity=20):
            self.capacity = capacity
            self._cache = {}
            self._order = []

        def get(self, key):
            if key in self._cache:
                # Move to end (most recently used)
                self._order.remove(key)
                self._order.append(key)
                return self._cache[key]
            return None

        def put(self, key, value):
            if key in self._cache:
                # Update existing item
                self._cache[key] = value
                self._order.remove(key)
                self._order.append(key)
            else:
                # Add new item
                if len(self._cache) >= self.capacity:
                    # Remove least recently used
                    oldest = self._order.pop(0)
                    del self._cache[oldest]
                self._cache[key] = value
                self._order.append(key)

# Global cache for vector calculations
_vector_cache = LRUCache(20)

def compute_wave_line(data: pd.DataFrame, wave: int = 10, min_periods: int = 1, column: str = 'close') -> pd.Series:
    """
    Compute the midpoint of the Donchian Channel over the close prices.

    Parameters
    ----------
    data : pd.DataFrame
        Must contain a price column (default: 'close').
    wave : int
        Lookback period for the Donchian Channel.
    min_periods : int
        Minimum number of observations required to calculate the statistic.
    column : str
        Column name to use for calculations (default: 'close', but can be 'Close' for different conventions).

    Returns
    -------
    pd.Series
        The stepped-line midpoints.
    """
    # Handle case sensitivity for column names
    price_col = column
    if column.lower() == 'close' and column not in data.columns and 'Close' in data.columns:
        price_col = 'Close'
    elif column.lower() == 'close' and column not in data.columns:
        raise ValueError(f"Column '{column}' not found in data. Available columns: {data.columns.tolist()}")

    # Calculate the Donchian channel
    hi = data[price_col].rolling(window=wave, min_periods=min_periods).max()
    lo = data[price_col].rolling(window=wave, min_periods=min_periods).min()
    wave_price = (hi + lo) / 2

    # Fill NaN values at the beginning with the first valid value
    wave_price = wave_price.bfill()

    return wave_price

def calculate_vector(data: pd.DataFrame, length: int = 20, column: str = 'close') -> pd.Series:
    """
    Calculate the vector using Donchian channel midpoint with caching.

    This is the main entry point for vector calculation. It uses compute_wave_line
    but adds caching for better performance.

    Parameters
    ----------
    data : pd.DataFrame
        The price data with a close/Close column.
    length : int
        The lookback period for the Donchian channel.
    column : str
        Column name to use for calculations (default: 'close', but can be 'Close' for different conventions).

    Returns
    -------
    pd.Series
        The calculated vector.
    """
    if len(data) < length:
        return pd.Series(index=data.index)

    # Create cache key
    cache_key = (id(data), length, column)

    # Check if result is in cache
    cached_result = _vector_cache.get(cache_key)
    if cached_result is not None:
        return cached_result

    # Calculate vector using compute_wave_line
    result = compute_wave_line(data, wave=length, column=column)

    # Cache the result
    _vector_cache.put(cache_key, result)

    return result

if __name__ == "__main__":
    # Example: load your OHLC data into a DataFrame with a 'close' column
    # For demonstration, we'll simulate some data:
    dates = pd.date_range(start="2025-01-01", periods=100, freq="D")
    close_prices = pd.Series(100 + pd.np.random.randn(100).cumsum(), index=dates)
    df = pd.DataFrame({'close': close_prices})

    # Compute the line
    wave_length = 10
    df['wave_line'] = compute_wave_line(df, wave=wave_length)

    # Plot as a stepped line
    plt.figure(figsize=(12, 6))
    plt.plot(df.index, df['close'], label='Close', alpha=0.5)
    plt.step(df.index, df['wave_line'], where='post',
             linewidth=2, color='purple', label=f'Wave Line ({wave_length})')
    plt.title("Donchian Channel Midpoint (Stepped Line)")
    plt.xlabel("Date")
    plt.ylabel("Price")
    plt.legend()
    plt.show()
