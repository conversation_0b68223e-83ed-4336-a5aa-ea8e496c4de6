"""
Sequence modeling for the Crossing Classifier.
This module provides tools for preparing and processing sequence data
to enable explicit sequence modeling without requiring TensorFlow.
"""

import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPClassifier
from sklearn.base import BaseEstimator, ClassifierMixin


def prepare_sequence_data(data, idx, lookback_window, feature_extractor=None):
    """
    Prepare a sequence of data points leading up to the crossing.

    Parameters:
    - data: DataFrame containing OHLCV data
    - idx: Index of the crossing point
    - lookback_window: Number of time steps to include in the sequence
    - feature_extractor: Optional feature extractor to use for each time step

    Returns:
    - Sequence of features as a 2D array (time_steps × features)
    """
    sequence = []
    start_idx = max(0, idx - lookback_window)

    # If we have a feature extractor, use it to extract features for each time step
    if feature_extractor is not None:
        for i in range(start_idx, idx + 1):
            # Extract features for this time step
            features = feature_extractor(data, i)
            if features is not None:
                # Flatten if needed
                if len(features.shape) > 1:
                    features = features.flatten()
                sequence.append(features)
    else:
        # Otherwise, just use the OHLCV data directly
        for i in range(start_idx, idx + 1):
            if i < len(data):
                row = data.iloc[i]
                features = np.array([
                    row['Open'], row['High'], row['Low'], row['Close'],
                    row['Volume'] if 'Volume' in row else 0
                ])
                sequence.append(features)

    # Convert to numpy array
    if sequence:
        sequence = np.array(sequence)

        # Pad sequence if needed
        if len(sequence) < lookback_window + 1:
            padding_shape = (lookback_window + 1 - len(sequence), sequence.shape[1])
            padding = np.zeros(padding_shape)
            sequence = np.vstack([padding, sequence])
    else:
        # Return empty sequence with correct shape if no data
        feature_dim = 5  # Default OHLCV dimension
        if feature_extractor is not None and len(sequence) > 0:
            feature_dim = len(sequence[0])
        sequence = np.zeros((lookback_window + 1, feature_dim))

    return sequence


class SequenceMLPClassifier(BaseEstimator, ClassifierMixin):
    """
    Base class for sequence-based classifiers using MLPClassifier.
    This provides explicit sequence modeling capabilities without requiring TensorFlow.
    """

    def __init__(self, sequence_length=10, hidden_layer_sizes=(64, 32), activation='relu',
                 solver='adam', alpha=0.0001, batch_size='auto', learning_rate='adaptive',
                 max_iter=500, random_state=42):
        self.sequence_length = sequence_length
        self.hidden_layer_sizes = hidden_layer_sizes
        self.activation = activation
        self.solver = solver
        self.alpha = alpha
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.max_iter = max_iter
        self.random_state = random_state

        self.model = MLPClassifier(
            hidden_layer_sizes=hidden_layer_sizes,
            activation=activation,
            solver=solver,
            alpha=alpha,
            batch_size=batch_size,
            learning_rate=learning_rate,
            max_iter=max_iter,
            random_state=random_state
        )

        self.scaler = StandardScaler()
        self.is_fitted = False

    def fit(self, X, y, epochs=None, batch_size=None, verbose=None):
        """
        Fit the model to sequence data.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)
        - y: Target labels
        - epochs: Number of epochs (used for API compatibility, maps to max_iter)
        - batch_size: Batch size (used for API compatibility)
        - verbose: Verbosity level (used for API compatibility)

        Returns:
        - self
        """
        # Handle epochs parameter for compatibility with TensorFlow API
        if epochs is not None and epochs > 0:
            self.model.max_iter = epochs

        # Handle batch_size parameter for compatibility with TensorFlow API
        if batch_size is not None and batch_size > 0:
            self.model.batch_size = min(batch_size, X.shape[0])

        # Flatten sequences for MLPClassifier
        X_flat = self._flatten_sequences(X)

        # Scale features
        X_scaled = self.scaler.fit_transform(X_flat)

        # Train the model
        self.model.fit(X_scaled, y)
        self.is_fitted = True

        return self

    def predict(self, X):
        """
        Predict class labels for sequences.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)

        Returns:
        - Array of predicted class labels
        """
        if not self.is_fitted:
            raise ValueError("Model has not been fitted yet.")

        # Handle single sequence
        if len(X.shape) == 2:
            X = X.reshape(1, X.shape[0], X.shape[1])

        # Flatten sequences
        X_flat = self._flatten_sequences(X)

        # Scale features
        X_scaled = self.scaler.transform(X_flat)

        # Make predictions
        return self.model.predict(X_scaled)

    def predict_proba(self, X):
        """
        Predict class probabilities for sequences.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)

        Returns:
        - Array of predicted class probabilities
        """
        if not self.is_fitted:
            raise ValueError("Model has not been fitted yet.")

        # Handle single sequence
        if len(X.shape) == 2:
            X = X.reshape(1, X.shape[0], X.shape[1])

        # Flatten sequences
        X_flat = self._flatten_sequences(X)

        # Scale features
        X_scaled = self.scaler.transform(X_flat)

        # Make predictions
        return self.model.predict_proba(X_scaled)

    def _flatten_sequences(self, X):
        """
        Flatten 3D sequence data (samples × time_steps × features) to 2D (samples × (time_steps*features))
        for use with MLPClassifier.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)

        Returns:
        - Flattened array, shape (n_samples, n_timesteps*n_features)
        """
        n_samples = X.shape[0]
        n_timesteps = X.shape[1]
        n_features = X.shape[2]

        # Reshape to (samples, time_steps*features)
        return X.reshape(n_samples, n_timesteps * n_features)


class GRULikeSequenceClassifier(SequenceMLPClassifier):
    """
    A GRU-like sequence classifier using MLPClassifier.
    Optimized for shorter sequences with faster dynamics.
    """

    def __init__(self, sequence_length=10, random_state=42):
        # Use a network architecture that mimics GRU behavior
        super().__init__(
            sequence_length=sequence_length,
            hidden_layer_sizes=(64, 32, 16),
            activation='relu',
            solver='adam',
            alpha=0.0001,
            batch_size='auto',
            learning_rate='adaptive',
            max_iter=300,
            random_state=random_state
        )

    def fit(self, X, y, epochs=None, batch_size=None, verbose=0):
        """
        Fit the model with special handling for GRU-like behavior.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)
        - y: Target labels
        - epochs: Number of epochs
        - batch_size: Batch size
        - verbose: Verbosity level

        Returns:
        - self
        """
        # Apply recency weighting to emphasize more recent time steps
        X_weighted = self._apply_recency_weighting(X)

        # Call parent fit method
        return super().fit(X_weighted, y, epochs, batch_size, verbose)

    def predict(self, X):
        """
        Predict with recency weighting.
        """
        X_weighted = self._apply_recency_weighting(X)
        return super().predict(X_weighted)

    def predict_proba(self, X):
        """
        Predict probabilities with recency weighting.
        """
        X_weighted = self._apply_recency_weighting(X)
        return super().predict_proba(X_weighted)

    def _apply_recency_weighting(self, X):
        """
        Apply recency weighting to emphasize more recent time steps.
        This mimics the recency bias of GRU networks.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)

        Returns:
        - Weighted array with same shape
        """
        # Create a copy to avoid modifying the original
        X_weighted = X.copy()

        # Create recency weights that increase for more recent time steps
        n_timesteps = X.shape[1]
        recency_weights = np.linspace(0.5, 1.0, n_timesteps)

        # Apply weights to each time step
        for t in range(n_timesteps):
            X_weighted[:, t, :] *= recency_weights[t]

        return X_weighted


class LSTMLikeSequenceClassifier(SequenceMLPClassifier):
    """
    An LSTM-like sequence classifier using MLPClassifier.
    Optimized for longer sequences with memory requirements.
    """

    def __init__(self, sequence_length=20, random_state=42):
        # Use a network architecture that mimics LSTM behavior
        super().__init__(
            sequence_length=sequence_length,
            hidden_layer_sizes=(128, 64, 32),
            activation='tanh',
            solver='adam',
            alpha=0.0001,
            batch_size='auto',
            learning_rate='adaptive',
            max_iter=800,  # More iterations for complex patterns
            random_state=random_state
        )

    def fit(self, X, y, epochs=None, batch_size=None, verbose=0):
        """
        Fit the model with special handling for LSTM-like behavior.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)
        - y: Target labels
        - epochs: Number of epochs
        - batch_size: Batch size
        - verbose: Verbosity level

        Returns:
        - self
        """
        # Apply memory-like processing to better capture long-term dependencies
        X_processed = self._apply_memory_processing(X)

        # Call parent fit method
        return super().fit(X_processed, y, epochs, batch_size, verbose)

    def predict(self, X):
        """
        Predict with memory processing.
        """
        X_processed = self._apply_memory_processing(X)
        return super().predict(X_processed)

    def predict_proba(self, X):
        """
        Predict probabilities with memory processing.
        """
        X_processed = self._apply_memory_processing(X)
        return super().predict_proba(X_processed)

    def _apply_memory_processing(self, X):
        """
        Apply memory-like processing to better capture long-term dependencies.
        This mimics the memory cell behavior of LSTM networks.

        Parameters:
        - X: Array of sequences, shape (n_samples, n_timesteps, n_features)

        Returns:
        - Processed array with same shape
        """
        # Calculate cumulative features to mimic memory cells
        n_samples = X.shape[0]
        n_timesteps = X.shape[1]
        n_features = X.shape[2]

        # Add cumulative moving averages as additional features
        X_with_memory = np.zeros((n_samples, n_timesteps, n_features * 2))

        for i in range(n_samples):
            # Copy original features
            X_with_memory[i, :, :n_features] = X[i]

            # Add cumulative moving averages (memory-like features)
            for t in range(n_timesteps):
                if t == 0:
                    X_with_memory[i, t, n_features:] = X[i, t]
                else:
                    # Exponential moving average with 0.7 weight for history
                    X_with_memory[i, t, n_features:] = 0.7 * X_with_memory[i, t-1, n_features:] + 0.3 * X[i, t]

        return X_with_memory


def get_sequence_model_for_timeframe(timeframe, sequence_length):
    """
    Returns appropriate sequence model based on timeframe.

    Parameters:
    - timeframe: String representing the timeframe (e.g., '1m', '5m', '1h')
    - sequence_length: Length of the sequence

    Returns:
    - Appropriate sequence model instance
    """
    lower_timeframes = ['1m', '5m', '15m', '30m', '1h']
    if timeframe in lower_timeframes and sequence_length <= 20:
        return GRULikeSequenceClassifier(sequence_length=min(10, sequence_length))
    return LSTMLikeSequenceClassifier(sequence_length=min(20, sequence_length))
