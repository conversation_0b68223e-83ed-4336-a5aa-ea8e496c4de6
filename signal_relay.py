"""
Signal Relay for Discord Bot

This module provides a class to relay trading signals from the Market Odds tab
to the Discord bot.
"""

import json
import os
from datetime import datetime
import logging

# Note: Main logging configuration is in main.py
# This is just a fallback in case this module is used standalone
logging.basicConfig(
    level=logging.ERROR,  # Minimal verbosity, only errors
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("signal_relay.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SignalRelay")
logger.setLevel(logging.ERROR)

class SignalRelay:
    """Class to relay trading signals to the Discord bot."""

    def __init__(self, signal_file="discord_signals.json", max_signals=50):
        """
        Initialize the signal relay.

        Args:
            signal_file: Path to the file where signals will be saved
            max_signals: Maximum number of signals to keep in the file
        """
        self.signal_file = signal_file
        self.max_signals = max_signals
        self.signals = []

        # Load existing signals if file exists
        self._load_signals()

    def _load_signals(self):
        """Load existing signals from file."""
        try:
            if os.path.exists(self.signal_file):
                with open(self.signal_file, 'r') as f:
                    self.signals = json.load(f)
                logger.debug(f"Loaded {len(self.signals)} signals from {self.signal_file}")
        except Exception as e:
            logger.error(f"Error loading signals: {str(e)}")
            self.signals = []

    def _save_signals(self):
        """Save signals to file."""
        try:
            with open(self.signal_file, 'w') as f:
                json.dump(self.signals, f, indent=2)
            logger.debug(f"Saved {len(self.signals)} signals to {self.signal_file}")
        except Exception as e:
            logger.error(f"Error saving signals: {str(e)}")

    def relay_signal(self, signal_data):
        """
        Relay a signal to the Discord bot.

        Args:
            signal_data: Dictionary containing signal information
        """
        try:
            # Extract relevant information
            signal_type = signal_data.get('status')
            direction = signal_data.get('direction', '')
            strength = signal_data.get('strength', '')
            message = signal_data.get('message', '')
            confidence = signal_data.get('confidence', 0)

            # Determine the signal type based on direction and strength
            if signal_type == 'confirmed':
                if direction == 'up':
                    if strength == 'strong':
                        discord_signal_type = 'BullishReversal'
                    else:
                        discord_signal_type = 'BullishPullback'
                else:  # down
                    if strength == 'strong':
                        discord_signal_type = 'BearishReversal'
                    else:
                        discord_signal_type = 'BearishPullback'
            else:
                # Skip non-confirmed signals
                return

            # Create signal record
            signal_record = {
                'type': discord_signal_type,
                'direction': direction,
                'strength': strength,
                'message': message,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'symbol': self._get_current_symbol(),
                'timeframe': self._get_current_timeframe(),
                'magnitude': signal_data.get('magnitude', 0)
            }

            # Add to signals list
            self.signals.append(signal_record)

            # Trim signals list if needed
            if len(self.signals) > self.max_signals:
                self.signals = self.signals[-self.max_signals:]

            # Save signals to file
            self._save_signals()

            logger.info(f"Relayed {discord_signal_type} signal to Discord bot")

        except Exception as e:
            logger.error(f"Error relaying signal: {str(e)}")

    def _get_current_symbol(self):
        """Get the current symbol being analyzed."""
        # This should be implemented to get the actual symbol from your application
        # For now, return a placeholder
        return "UNKNOWN"

    def _get_current_timeframe(self):
        """Get the current timeframe being analyzed."""
        # This should be implemented to get the actual timeframe from your application
        # For now, return a placeholder
        return "UNKNOWN"