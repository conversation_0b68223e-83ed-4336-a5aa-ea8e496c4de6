#!/usr/bin/env python3
"""
Demo script showing how to use the Schwab API integration

This script demonstrates the basic usage patterns for the Schwab API
integration in both authenticated and non-authenticated modes.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def demo_data_dispatcher():
    """Demonstrate using the enhanced data dispatcher"""
    print("=== Data Dispatcher Demo ===")

    from data_dispatcher import DataDispatcher

    # Get the singleton instance
    dispatcher = DataDispatcher.get_instance()

    # Show available data sources
    print(f"Available data sources: {dispatcher.get_available_sources()}")
    print(f"Current data source: {dispatcher.get_data_source()}")

    # Test different data source settings
    print("\nTesting data source switching:")

    # Set to yfinance
    dispatcher.set_data_source('yfinance')
    print(f"Set to yfinance: {dispatcher.get_data_source()}")

    # Set to auto (will use Schwab if available, otherwise yfinance)
    dispatcher.set_data_source('auto')
    print(f"Set to auto: {dispatcher.get_data_source()}")

    # Try to set to <PERSON>hwab (will work if authenticated)
    dispatcher.set_data_source('schwab')
    print(f"Set to schwab: {dispatcher.get_data_source()}")

    print()

def demo_schwab_api_basic():
    """Demonstrate basic Schwab API usage"""
    print("=== Schwab API Basic Demo ===")

    from schwab_api import schwab_api

    # Check connection status
    print(f"Connected to Schwab API: {schwab_api.is_connected()}")

    if not schwab_api.is_connected():
        print("Not connected to Schwab API. To connect:")
        print("1. Run the main application: python main.py")
        print("2. Go to the 'Schwab API' tab")
        print("3. Enter your credentials and authenticate")
        print("4. Then run this demo again")
        return

    # If connected, demonstrate data fetching
    print("\nFetching sample data...")

    # Get a quote
    print("Getting quote for AAPL...")
    quote = schwab_api.get_quote("AAPL")
    if quote:
        print(f"AAPL Quote: ${quote.get('lastPrice', 'N/A')}")
    else:
        print("Failed to get quote")

    # Get price history - DISABLED
    print("Historical data loading from Schwab API is DISABLED")
    print("Skipping price history demo...")

    print()

def demo_user_management():
    """Demonstrate user management functionality"""
    print("=== User Management Demo ===")

    from user_management import get_user_manager

    # Get the global user manager
    user_mgr = get_user_manager()

    # Check login status
    print(f"User logged in: {user_mgr.is_logged_in()}")

    # Get current user info
    user_info = user_mgr.get_current_user()
    if user_info:
        print(f"Current user API key: {user_info.get('api_key_preview', 'Unknown')}")
        print(f"Login time: {user_info.get('login_time', 'Unknown')}")
    else:
        print("No user currently logged in")

    # Get session info
    session_info = user_mgr.get_session_info()
    print(f"Session info: {session_info}")

    print()

def demo_data_fetching():
    """Demonstrate data fetching with different sources"""
    print("=== Data Fetching Demo ===")

    from data_dispatcher import DataDispatcher

    dispatcher = DataDispatcher.get_instance()

    # Set up signal handlers
    def on_data_ready(symbol, data):
        print(f"Data ready for {symbol}: {len(data)} rows")
        if hasattr(data, 'attrs') and 'data_source' in data.attrs:
            print(f"Data source: {data.attrs['data_source']}")
        print(f"Latest close: ${data['Close'].iloc[-1]:.2f}")

    def on_progress(percent, message):
        print(f"Progress: {percent}% - {message}")

    def on_error(error_message):
        print(f"Error: {error_message}")

    # Connect signals
    dispatcher.data_fetched.connect(on_data_ready)
    dispatcher.progress.connect(on_progress)
    dispatcher.error.connect(on_error)

    # Test fetching with different data sources
    symbols = ["AAPL", "MSFT"]

    for symbol in symbols:
        print(f"\nFetching data for {symbol}...")

        # Fetch with auto source selection
        dispatcher.fetch_data(symbol, "1m", 1)

        # Give it a moment to process
        time.sleep(2)

    print()

def main():
    """Run the demo"""
    print("Schwab API Integration Demo")
    print("=" * 50)

    # Create QApplication for Qt components
    app = QApplication(sys.argv)

    # Run demos
    demo_data_dispatcher()
    demo_schwab_api_basic()
    demo_user_management()

    print("=" * 50)
    print("Demo completed!")
    print("\nTo see the full integration in action:")
    print("1. Run: python main.py")
    print("2. Go to the 'Schwab API' tab to login")
    print("3. Use the data source selector in Universal Controls")
    print("4. Fetch data and see live Schwab API data!")

    # Don't start the event loop, just exit
    return 0

if __name__ == "__main__":
    sys.exit(main())
