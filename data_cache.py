"""
Data Cache Module

This module provides a centralized caching mechanism for financial data
to avoid redundant fetches and improve application performance.
"""

import threading
from typing import Tuple, Any, Optional
from lru_cache import LRUCache
import pandas as pd
import time

class DataCache:
    """
    A thread-safe cache for financial data.
    
    This cache stores data fetched from financial APIs to avoid redundant
    network requests and improve application performance.
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the DataCache."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DataCache()
            return cls._instance
    
    def __init__(self, capacity=20):
        """
        Initialize the data cache.
        
        Args:
            capacity: Maximum number of datasets to cache
        """
        self.cache = LRUCache(capacity=capacity, adaptive=True, 
                             min_capacity=10, max_capacity=50)
        self.metadata = {}  # Store metadata about cached items
        self._lock = threading.RLock()
    
    def get_data(self, symbol: str, timeframe: str, days_to_load: int) -> Optional[pd.DataFrame]:
        """
        Get data from the cache if available.
        
        Args:
            symbol: Stock symbol
            timeframe: Data timeframe (e.g., '1m', '5m', '1d')
            days_to_load: Number of days of data to load
            
        Returns:
            Cached DataFrame or None if not in cache
        """
        key = self._make_key(symbol, timeframe, days_to_load)
        with self._lock:
            data = self.cache.get(key)
            if data is not None:
                # Check if data is still fresh (less than 5 minutes old for intraday)
                metadata = self.metadata.get(key, {})
                timestamp = metadata.get('timestamp', 0)
                
                # For intraday timeframes, cache expires faster
                if timeframe in ['1m', '2m', '5m', '15m', '30m', '60m', '90m']:
                    max_age = 300  # 5 minutes for intraday data
                else:
                    max_age = 3600  # 1 hour for daily data
                
                if time.time() - timestamp > max_age:
                    # Data is stale, remove from cache
                    del self.metadata[key]
                    return None
                
                return data
            return None
    
    def store_data(self, symbol: str, timeframe: str, days_to_load: int, data: pd.DataFrame) -> None:
        """
        Store data in the cache.
        
        Args:
            symbol: Stock symbol
            timeframe: Data timeframe
            days_to_load: Number of days of data
            data: DataFrame to cache
        """
        if data is None or data.empty:
            return
            
        key = self._make_key(symbol, timeframe, days_to_load)
        with self._lock:
            self.cache.put(key, data.copy())
            self.metadata[key] = {
                'timestamp': time.time(),
                'rows': len(data),
                'symbol': symbol,
                'timeframe': timeframe,
                'days_to_load': days_to_load
            }
    
    def clear(self) -> None:
        """Clear all cached data."""
        with self._lock:
            self.cache = LRUCache(capacity=self.cache.capacity)
            self.metadata = {}
    
    def clear_symbol(self, symbol: str) -> None:
        """
        Clear all cached data for a specific symbol.
        
        Args:
            symbol: Stock symbol to clear from cache
        """
        with self._lock:
            keys_to_remove = []
            for key in self.metadata:
                if self.metadata[key]['symbol'] == symbol:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self.cache._cache.pop(key, None)
                del self.metadata[key]
    
    def get_stats(self) -> dict:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        with self._lock:
            return {
                'size': len(self.cache),
                'capacity': self.cache.capacity,
                'hits': self.cache._hits,
                'misses': self.cache._misses,
                'items': list(self.metadata.values())
            }
    
    def _make_key(self, symbol: str, timeframe: str, days_to_load: int) -> Tuple[str, str, int]:
        """
        Create a cache key from the parameters.
        
        Args:
            symbol: Stock symbol
            timeframe: Data timeframe
            days_to_load: Number of days of data
            
        Returns:
            Tuple to use as cache key
        """
        return (symbol.upper(), timeframe, days_to_load)
