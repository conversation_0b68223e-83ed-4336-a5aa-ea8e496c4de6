import numpy as np
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg

class EnhancedVectorDisplay:
    """
    Enhanced vector display component with modern UI and animations.

    This class provides a modern, visually appealing display for vector values
    in the imprints widget, with smooth animations and visual feedback.
    """

    def __init__(self, plot_widget, chart_colors=None):
        """
        Initialize the enhanced vector display.

        Args:
            plot_widget: The pyqtgraph PlotWidget to display the vector in
            chart_colors: Dictionary of colors to use for the display
        """
        self.plot_widget = plot_widget
        self.chart_colors = chart_colors or {
            'background': '#121620',  # Midnight Ocean background
            'vector': '#9C27B0',  # Material Design Purple
            'text': '#E0E0E0',    # Light gray text
            'bullish': '#4CAF50', # Material Design Green
            'bearish': '#F44336', # Material Design Red
        }

        # Store items for later removal/update
        self.vector_lines = []
        self.vector_labels = []
        self.animation_timers = []

        # Animation properties
        self.animation_duration = 300  # milliseconds
        self.animation_steps = 10

    def clear(self):
        """Clear all vector display items from the plot widget."""
        # Stop any running animations
        for timer in self.animation_timers:
            if timer.isActive():
                timer.stop()
        self.animation_timers = []

        # Remove vector lines
        for line in self.vector_lines:
            self.plot_widget.removeItem(line)
        self.vector_lines = []

        # Remove vector labels
        for label in self.vector_labels:
            self.plot_widget.removeItem(label)
        self.vector_labels = []

    def draw_vector_line(self, x_range, level, cycle_type=None, is_closed=False, animate=True):
        """
        Draw a vector line with modern styling and optional animation.

        Args:
            x_range: Tuple of (left_edge, right_edge) for the line
            level: Y-coordinate for the line
            cycle_type: 'bullish' or 'bearish' to determine color
            is_closed: Whether this vector level is closed
            animate: Whether to animate the line appearance

        Returns:
            The created line item
        """
        left_edge, right_edge = x_range

        # Determine line color based on cycle type
        if cycle_type == 'bullish':
            color = self.chart_colors['bullish'] if is_closed else '#90EE90'  # Light green if not closed
        elif cycle_type == 'bearish':
            color = self.chart_colors['bearish'] if is_closed else '#FF6666'  # Light red if not closed
        else:
            color = self.chart_colors['vector']

        # Create gradient for the line
        gradient = QtGui.QLinearGradient(left_edge, level, right_edge, level)
        gradient.setColorAt(0, QtGui.QColor(color).darker(120))
        gradient.setColorAt(0.5, QtGui.QColor(color))
        gradient.setColorAt(1, QtGui.QColor(color).darker(120))

        # Create pen with gradient
        pen = pg.mkPen(color=color, width=3)

        if animate:
            # Start with a short line and animate to full width
            mid_x = (left_edge + right_edge) / 2
            start_x = mid_x - (right_edge - left_edge) * 0.1
            end_x = mid_x + (right_edge - left_edge) * 0.1

            line = self.plot_widget.plot(
                [start_x, end_x], [level, level],
                pen=pen
            )

            # Store original and target coordinates for animation
            line.original_coords = ([start_x, end_x], [level, level])
            line.target_coords = ([left_edge, right_edge], [level, level])
            line.current_step = 0

            # Create and start animation timer
            timer = QtCore.QTimer()
            timer.timeout.connect(lambda line=line, timer=timer: self._animate_line(line, timer))
            timer.start(self.animation_duration / self.animation_steps)
            self.animation_timers.append(timer)
        else:
            # Create line without animation
            line = self.plot_widget.plot(
                [left_edge, right_edge], [level, level],
                pen=pen
            )

        self.vector_lines.append(line)
        return line

    def _animate_line(self, line, timer):
        """
        Animate a line from its original to target coordinates.

        Args:
            line: The line item to animate
            timer: The QTimer controlling the animation
        """
        line.current_step += 1
        progress = line.current_step / self.animation_steps

        if progress >= 1.0:
            # Animation complete, set final coordinates
            line.setData(line.target_coords[0], line.target_coords[1])
            timer.stop()
            return

        # Calculate intermediate coordinates
        orig_x, orig_y = line.original_coords
        target_x, target_y = line.target_coords

        # Use easing function for smoother animation
        t = self._ease_out_cubic(progress)

        current_x = [
            orig_x[0] + (target_x[0] - orig_x[0]) * t,
            orig_x[1] + (target_x[1] - orig_x[1]) * t
        ]

        # Update line coordinates
        line.setData(current_x, target_y)

    def _ease_out_cubic(self, t):
        """
        Cubic easing function for smoother animations.

        Args:
            t: Progress value from 0 to 1

        Returns:
            Eased value
        """
        return 1 - (1 - t) ** 3

    def add_vector_label(self, position, price, cycle_type=None, is_closed=False, animate=True):
        """
        Add a modern styled label for the vector price.

        Args:
            position: (x, y) coordinates for the label
            price: The price value to display
            cycle_type: 'bullish' or 'bearish' to determine color
            is_closed: Whether this vector level is closed
            animate: Whether to animate the label appearance

        Returns:
            The created label item
        """
        x, y = position

        # Determine text color based on cycle type
        if cycle_type == 'bullish':
            text_color = "#00FF00" if is_closed else "#90EE90"
        elif cycle_type == 'bearish':
            text_color = "#FF6666" if is_closed else "#FF3333"
        else:
            text_color = self.chart_colors['text']

        # Format price string
        price_str = f"{price:,.2f}"

        # Create modern styled label with rounded corners and shadow
        label = pg.TextItem(
            html=f'''
            <div style="
                background-color: rgba(0,0,0,0.7);
                padding: 3px 6px;
                border-radius: 4px;
                font-size: 9pt;
                color: {text_color};
                border: 1px solid rgba(255,255,255,0.2);
                box-shadow: 0px 2px 3px rgba(0,0,0,0.3);
            ">
                {price_str}
            </div>
            ''',
            anchor=(0.5, 0.5)
        )

        if animate:
            # Start with opacity 0 and animate to full opacity
            label.setOpacity(0)
            label.setPos(x, y)
            self.plot_widget.addItem(label)

            # Create and start fade-in animation
            label.current_step = 0
            timer = QtCore.QTimer()
            timer.timeout.connect(lambda label=label, timer=timer: self._animate_label(label, timer))
            timer.start(self.animation_duration / self.animation_steps)
            self.animation_timers.append(timer)
        else:
            # Add label without animation
            label.setPos(x, y)
            self.plot_widget.addItem(label)

        self.vector_labels.append(label)
        return label

    def _animate_label(self, label, timer):
        """
        Animate a label's opacity from 0 to 1.

        Args:
            label: The label item to animate
            timer: The QTimer controlling the animation
        """
        label.current_step += 1
        progress = label.current_step / self.animation_steps

        if progress >= 1.0:
            # Animation complete, set final opacity
            label.setOpacity(1.0)
            timer.stop()
            return

        # Use easing function for smoother animation
        opacity = self._ease_out_cubic(progress)
        label.setOpacity(opacity)

    def draw_vector_cluster(self, x_data, y_data, min_level, max_level, avg_level,
                           cluster_size, min_price=None, max_price=None, color="yellow", opacity=50):
        """
        Draw a vector cluster with modern styling.

        Args:
            x_data: X-coordinates for the cluster polygon
            y_data: Y-coordinates for the cluster polygon
            min_level: Minimum level in the cluster
            max_level: Maximum level in the cluster
            avg_level: Average level in the cluster
            cluster_size: Number of levels in the cluster
            min_price: Minimum price in the cluster
            max_price: Maximum price in the cluster
            color: Color name for the cluster
            opacity: Opacity percentage (0-100)

        Returns:
            Tuple of (cluster_rect, cluster_label)
        """
        # Color mapping
        color_map = {
            "yellow": (255, 255, 0),
            "red": (255, 0, 0),
            "green": (0, 255, 0),
            "blue": (0, 0, 255),
            "cyan": (0, 255, 255),
            "magenta": (255, 0, 255)
        }
        rgb = color_map.get(color, (255, 255, 0))

        # Create cluster rectangle with gradient fill
        cluster_rect = pg.PlotDataItem(
            x=x_data,
            y=y_data,
            pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine),
            fillLevel=min_level,
            brush=pg.mkBrush(color=(*rgb, opacity))
        )

        # Add cluster to plot
        self.plot_widget.addItem(cluster_rect)
        self.vector_lines.append(cluster_rect)

        # Create cluster label if prices are provided
        if min_price is not None and max_price is not None:
            cluster_text = f"Cluster ({cluster_size}): {min_price:.2f} - {max_price:.2f}"

            # Create modern styled label
            cluster_label = pg.TextItem(
                html=f'''
                <div style="
                    background-color: rgba(0,0,0,0.7);
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 9pt;
                    color: {color};
                    border: 1px solid rgba(255,255,255,0.2);
                    box-shadow: 0px 2px 3px rgba(0,0,0,0.3);
                ">
                    {cluster_text}
                </div>
                ''',
                anchor=(0, 0.5)
            )

            # Position label at the right edge of the cluster at average level
            cluster_label.setPos(x_data[1] * 0.95, avg_level)
            self.plot_widget.addItem(cluster_label)
            self.vector_labels.append(cluster_label)

            return cluster_rect, cluster_label

        return cluster_rect, None
