"""
LRU Cache Implementation

This module provides a thread-safe LRU (Least Recently Used) cache implementation
that can be used throughout the application for efficient caching with automatic
eviction of least recently used items when the cache reaches its capacity.
"""

import threading
from collections import OrderedDict
from typing import Any, Dict, Generic, Optional, Tuple, TypeVar

K = TypeVar('K')  # Key type
V = TypeVar('V')  # Value type

class LRUCache(Generic[K, V]):
    """
    A thread-safe Least Recently Used (LRU) cache implementation.

    This cache has a fixed maximum size and will automatically evict
    the least recently used items when it reaches capacity.

    Attributes:
        capacity (int): Maximum number of items the cache can hold
        _cache (OrderedDict): Ordered dictionary to store cache items
        _lock (threading.RLock): Reentrant lock for thread safety
        _hits (int): Number of cache hits
        _misses (int): Number of cache misses
    """

    def __init__(self, capacity: int = 128, adaptive: bool = False,
                 min_capacity: int = 100, max_capacity: int = 10000):
        """
        Initialize a new LRU cache with the specified capacity.

        Args:
            capacity (int): Maximum number of items the cache can hold
            adaptive (bool): Whether to enable adaptive cache sizing
            min_capacity (int): Minimum capacity when using adaptive sizing
            max_capacity (int): Maximum capacity when using adaptive sizing
        """
        self.capacity = max(1, capacity)
        self.adaptive = adaptive
        self.min_capacity = max(1, min_capacity)
        self.max_capacity = max(self.min_capacity, max_capacity)
        self._cache: OrderedDict[K, V] = OrderedDict()
        self._lock = threading.RLock()
        self._hits = 0
        self._misses = 0

    def get(self, key: K) -> Optional[V]:
        """
        Get an item from the cache.

        If the key exists, it will be moved to the end of the OrderedDict
        to mark it as most recently used.

        Args:
            key: The key to look up

        Returns:
            The value associated with the key, or None if the key is not in the cache
        """
        with self._lock:
            if key in self._cache:
                # Move the key to the end to mark it as most recently used
                value = self._cache.pop(key)
                self._cache[key] = value
                self._hits += 1
                return value
            self._misses += 1
            return None

    def put(self, key: K, value: V) -> None:
        """
        Add or update an item in the cache.

        If the key already exists, it will be updated and moved to the end
        of the OrderedDict to mark it as most recently used. If the cache
        is at capacity, the least recently used item will be evicted.

        Args:
            key: The key to store
            value: The value to store
        """
        with self._lock:
            if key in self._cache:
                # Remove the existing key to update its position
                self._cache.pop(key)
            elif len(self._cache) >= self.capacity:
                # Remove the first item (least recently used)
                self._cache.popitem(last=False)

            # Add the new key-value pair
            self._cache[key] = value

    def remove(self, key: K) -> bool:
        """
        Remove an item from the cache.

        Args:
            key: The key to remove

        Returns:
            True if the key was in the cache and was removed, False otherwise
        """
        with self._lock:
            if key in self._cache:
                self._cache.pop(key)
                return True
            return False

    def clear(self) -> None:
        """Clear all items from the cache."""
        with self._lock:
            self._cache.clear()

    def contains(self, key: K) -> bool:
        """
        Check if a key is in the cache.

        Args:
            key: The key to check

        Returns:
            True if the key is in the cache, False otherwise
        """
        with self._lock:
            return key in self._cache

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            A dictionary containing cache statistics:
            - capacity: Maximum capacity of the cache
            - size: Current number of items in the cache
            - hits: Number of cache hits
            - misses: Number of cache misses
            - hit_ratio: Ratio of hits to total accesses
        """
        with self._lock:
            total = self._hits + self._misses
            hit_ratio = self._hits / total if total > 0 else 0

            return {
                "capacity": self.capacity,
                "size": len(self._cache),
                "hits": self._hits,
                "misses": self._misses,
                "hit_ratio": hit_ratio,
                "hit_rate": hit_ratio,  # Alias for hit_ratio for backward compatibility
                "memory_usage_mb": 0.0  # Placeholder for memory usage (not implemented)
            }

    def __len__(self) -> int:
        """Return the number of items in the cache."""
        with self._lock:
            return len(self._cache)

    def __contains__(self, key: K) -> bool:
        """Check if a key is in the cache."""
        with self._lock:
            return key in self._cache

    def __getitem__(self, key: K) -> V:
        """
        Get an item from the cache using dictionary syntax.

        Args:
            key: The key to look up

        Returns:
            The value associated with the key

        Raises:
            KeyError: If the key is not in the cache
        """
        with self._lock:
            if key in self._cache:
                # Move the key to the end to mark it as most recently used
                value = self._cache.pop(key)
                self._cache[key] = value
                self._hits += 1
                return value
            self._misses += 1
            raise KeyError(key)

    def __setitem__(self, key: K, value: V) -> None:
        """
        Add or update an item in the cache using dictionary syntax.

        Args:
            key: The key to store
            value: The value to store
        """
        self.put(key, value)

    def __delitem__(self, key: K) -> None:
        """
        Remove an item from the cache using dictionary syntax.

        Args:
            key: The key to remove

        Raises:
            KeyError: If the key is not in the cache
        """
        with self._lock:
            if key in self._cache:
                self._cache.pop(key)
            else:
                raise KeyError(key)

    def items(self) -> Tuple[Tuple[K, V], ...]:
        """
        Get all items in the cache.

        Returns:
            A tuple of (key, value) pairs
        """
        with self._lock:
            return tuple(self._cache.items())

    def keys(self) -> Tuple[K, ...]:
        """
        Get all keys in the cache.

        Returns:
            A tuple of keys
        """
        with self._lock:
            return tuple(self._cache.keys())

    def values(self) -> Tuple[V, ...]:
        """
        Get all values in the cache.

        Returns:
            A tuple of values
        """
        with self._lock:
            return tuple(self._cache.values())

    def adjust_capacity(self, new_capacity: int) -> None:
        """
        Adjust the capacity of the cache.

        If adaptive sizing is enabled, the capacity will be constrained
        between min_capacity and max_capacity.

        Args:
            new_capacity (int): The new capacity to set
        """
        with self._lock:
            if self.adaptive:
                # Constrain the new capacity between min and max
                new_capacity = max(self.min_capacity, min(self.max_capacity, new_capacity))

            # Only adjust if the new capacity is different
            if new_capacity != self.capacity:
                old_capacity = self.capacity
                self.capacity = new_capacity

                # If we're reducing capacity, we may need to evict items
                if new_capacity < old_capacity and len(self._cache) > new_capacity:
                    # Calculate how many items to remove
                    items_to_remove = len(self._cache) - new_capacity

                    # Remove the least recently used items
                    for _ in range(items_to_remove):
                        self._cache.popitem(last=False)
