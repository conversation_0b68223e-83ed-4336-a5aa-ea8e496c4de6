//@version=6
indicator('SPY Zones & Levels Export', overlay = true, max_boxes_count = 500, max_lines_count = 500)

// Generated on 2025-07-13 03:26
// Exported 3 zones and 5 levels from candlestick chart

// Variables to store boxes, lines and labels
var box_array = array.new<box>()
var line_array = array.new<line>()
var label_array = array.new<label>()

// Clear previous items to prevent trail
if barstate.islast
    if array.size(box_array) > 0
        for i = 0 to array.size(box_array) - 1 by 1
            box.delete(array.get(box_array, i))
        array.clear(box_array)
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1 by 1
            line.delete(array.get(line_array, i))
        array.clear(line_array)
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1 by 1
            label.delete(array.get(label_array, i))
        array.clear(label_array)

// Volatility Zone 1 (Volatility) from 400.0000 to 410.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 400.0000, bar_index + 100, 410.0000, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 405.0000, 
                          text = 'Volatility Zone 1', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// Volatility Zone 2 (Volatility) from 420.0000 to 430.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 420.0000, bar_index + 100, 430.0000, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 425.0000, 
                          text = 'Volatility Zone 2', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// AK Density Zone 1 (Density) from 435.0000 to 445.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 435.0000, bar_index + 100, 445.0000, 
                      border_color = color.blue, bgcolor = color.new(color.blue, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 440.0000, 
                          text = 'AK Density Zone 1', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.blue, 
                          size = size.small)
    array.push(label_array, zone_label)

// Volatility Level 1 (Volatility) at 450.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 450.0000, bar_index + 100, 450.0000, 
                         color = color.white, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 450.0000, 
                           text = 'Volatility Level 1: 450.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.white, 
                           size = size.small)
    array.push(label_array, level_label)

// Volatility Level 2 (Volatility) at 460.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 460.0000, bar_index + 100, 460.0000, 
                         color = color.yellow, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 460.0000, 
                           text = 'Volatility Level 2: 460.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.yellow, 
                           size = size.small)
    array.push(label_array, level_label)

// AK Daily Vol Level 1 (Ak_daily_vol) at 470.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 470.0000, bar_index + 100, 470.0000, 
                         color = color.cyan, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 470.0000, 
                           text = 'AK Daily Vol Level 1: 470.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.cyan, 
                           size = size.small)
    array.push(label_array, level_label)

// AK Daily Vol Level 2 (Ak_daily_vol) at 480.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 480.0000, bar_index + 100, 480.0000, 
                         color = color.fuchsia, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 480.0000, 
                           text = 'AK Daily Vol Level 2: 480.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.fuchsia, 
                           size = size.small)
    array.push(label_array, level_label)

// Test Price Level (Price_level) at 490.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 490.0000, bar_index + 100, 490.0000, 
                         color = color.orange, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 490.0000, 
                           text = 'Test Price Level: 490.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.orange, 
                           size = size.small)
    array.push(label_array, level_label)

// Summary: Generated 3 zones and 5 levels
// Items included in this script:
// ZONES:
// - Volatility Zone 1: 400.0000 to 410.0000
// - Volatility Zone 2: 420.0000 to 430.0000
// - AK Density Zone 1: 435.0000 to 445.0000
// LEVELS:
// - Volatility Level 1: 450.0000
// - Volatility Level 2: 460.0000
// - AK Daily Vol Level 1: 470.0000
// - AK Daily Vol Level 2: 480.0000
// - Test Price Level: 490.0000
