"""
Dialog Manager Module

This module provides centralized control over dialog boxes and popup messages
to enable background processing without user interruption.
"""

import logging
from PyQt6 import QtWidgets, QtCore
from typing import Optional, Union

# Global logger for suppressed messages
logger = logging.getLogger(__name__)

class DialogManager:
    """
    Centralized manager for controlling dialog box behavior.
    Allows suppression of dialogs during background operations.
    """
    
    _instance = None
    _suppress_dialogs = True  # Default to suppressed for background processing
    _suppress_info_dialogs = True
    _suppress_warning_dialogs = True
    _suppress_error_dialogs = False  # Keep critical errors visible by default
    _log_suppressed_messages = True
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DialogManager, cls).__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance of DialogManager."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    def set_suppress_dialogs(cls, suppress: bool):
        """Enable or disable dialog suppression globally."""
        cls._suppress_dialogs = suppress
        logger.info(f"Dialog suppression {'enabled' if suppress else 'disabled'}")
    
    @classmethod
    def set_suppress_info_dialogs(cls, suppress: bool):
        """Enable or disable suppression of information dialogs."""
        cls._suppress_info_dialogs = suppress
    
    @classmethod
    def set_suppress_warning_dialogs(cls, suppress: bool):
        """Enable or disable suppression of warning dialogs."""
        cls._suppress_warning_dialogs = suppress
    
    @classmethod
    def set_suppress_error_dialogs(cls, suppress: bool):
        """Enable or disable suppression of error dialogs."""
        cls._suppress_error_dialogs = suppress
    
    @classmethod
    def set_log_suppressed_messages(cls, log: bool):
        """Enable or disable logging of suppressed messages."""
        cls._log_suppressed_messages = log
    
    @classmethod
    def should_suppress_dialog(cls, dialog_type: str = "info") -> bool:
        """
        Check if dialogs should be suppressed based on type.
        
        Args:
            dialog_type: Type of dialog ("info", "warning", "error", "critical")
            
        Returns:
            True if dialog should be suppressed, False otherwise
        """
        if not cls._suppress_dialogs:
            return False
            
        if dialog_type.lower() in ["info", "information"]:
            return cls._suppress_info_dialogs
        elif dialog_type.lower() in ["warning", "warn"]:
            return cls._suppress_warning_dialogs
        elif dialog_type.lower() in ["error", "critical"]:
            return cls._suppress_error_dialogs
        
        return cls._suppress_dialogs
    
    @classmethod
    def show_message_box(cls, parent: Optional[QtWidgets.QWidget], 
                        title: str, message: str, 
                        dialog_type: str = "info",
                        buttons: QtWidgets.QMessageBox.StandardButton = QtWidgets.QMessageBox.StandardButton.Ok) -> QtWidgets.QMessageBox.StandardButton:
        """
        Show a message box with suppression support.
        
        Args:
            parent: Parent widget
            title: Dialog title
            message: Dialog message
            dialog_type: Type of dialog ("info", "warning", "error", "critical")
            buttons: Dialog buttons
            
        Returns:
            Button that was clicked (or default if suppressed)
        """
        # Log the message regardless of suppression
        if cls._log_suppressed_messages:
            log_level = {
                "info": logging.INFO,
                "information": logging.INFO,
                "warning": logging.WARNING,
                "warn": logging.WARNING,
                "error": logging.ERROR,
                "critical": logging.CRITICAL
            }.get(dialog_type.lower(), logging.INFO)
            
            logger.log(log_level, f"{title}: {message}")
        
        # Check if dialog should be suppressed
        if cls.should_suppress_dialog(dialog_type):
            # Return default button without showing dialog
            if buttons & QtWidgets.QMessageBox.StandardButton.Ok:
                return QtWidgets.QMessageBox.StandardButton.Ok
            elif buttons & QtWidgets.QMessageBox.StandardButton.Yes:
                return QtWidgets.QMessageBox.StandardButton.Yes
            else:
                return QtWidgets.QMessageBox.StandardButton.NoButton
        
        # Show the dialog normally
        msg_box = QtWidgets.QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(buttons)
        
        # Set appropriate icon based on dialog type
        icon_map = {
            "info": QtWidgets.QMessageBox.Icon.Information,
            "information": QtWidgets.QMessageBox.Icon.Information,
            "warning": QtWidgets.QMessageBox.Icon.Warning,
            "warn": QtWidgets.QMessageBox.Icon.Warning,
            "error": QtWidgets.QMessageBox.Icon.Critical,
            "critical": QtWidgets.QMessageBox.Icon.Critical
        }
        
        if dialog_type.lower() in icon_map:
            msg_box.setIcon(icon_map[dialog_type.lower()])
        
        return msg_box.exec()
    
    @classmethod
    def information(cls, parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
        """Show an information dialog with suppression support."""
        return cls.show_message_box(parent, title, message, "info")
    
    @classmethod
    def warning(cls, parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
        """Show a warning dialog with suppression support."""
        return cls.show_message_box(parent, title, message, "warning")
    
    @classmethod
    def critical(cls, parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
        """Show a critical error dialog with suppression support."""
        return cls.show_message_box(parent, title, message, "critical")
    
    @classmethod
    def question(cls, parent: Optional[QtWidgets.QWidget], title: str, message: str,
                buttons: QtWidgets.QMessageBox.StandardButton = QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
                default_button: QtWidgets.QMessageBox.StandardButton = QtWidgets.QMessageBox.StandardButton.Yes) -> QtWidgets.QMessageBox.StandardButton:
        """Show a question dialog with suppression support."""
        # For question dialogs, if suppressed, return the default button
        if cls.should_suppress_dialog("info"):
            if cls._log_suppressed_messages:
                logger.info(f"{title}: {message} (Auto-answered: {default_button.name})")
            return default_button
        
        msg_box = QtWidgets.QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(buttons)
        msg_box.setDefaultButton(default_button)
        msg_box.setIcon(QtWidgets.QMessageBox.Icon.Question)
        
        return msg_box.exec()


# Convenience functions that can be used as drop-in replacements for QMessageBox
def information(parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
    """Show an information dialog with suppression support."""
    return DialogManager.information(parent, title, message)

def warning(parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
    """Show a warning dialog with suppression support."""
    return DialogManager.warning(parent, title, message)

def critical(parent: Optional[QtWidgets.QWidget], title: str, message: str) -> QtWidgets.QMessageBox.StandardButton:
    """Show a critical error dialog with suppression support."""
    return DialogManager.critical(parent, title, message)

def question(parent: Optional[QtWidgets.QWidget], title: str, message: str,
            buttons: QtWidgets.QMessageBox.StandardButton = QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            default_button: QtWidgets.QMessageBox.StandardButton = QtWidgets.QMessageBox.StandardButton.Yes) -> QtWidgets.QMessageBox.StandardButton:
    """Show a question dialog with suppression support."""
    return DialogManager.question(parent, title, message, buttons, default_button)


# Context manager for temporary dialog suppression
class SuppressDialogs:
    """Context manager to temporarily suppress dialogs during operations."""
    
    def __init__(self, suppress_info: bool = True, suppress_warning: bool = True, suppress_error: bool = False):
        self.suppress_info = suppress_info
        self.suppress_warning = suppress_warning
        self.suppress_error = suppress_error
        self.original_info = None
        self.original_warning = None
        self.original_error = None
        self.original_global = None
    
    def __enter__(self):
        # Save original settings
        dm = DialogManager.get_instance()
        self.original_global = dm._suppress_dialogs
        self.original_info = dm._suppress_info_dialogs
        self.original_warning = dm._suppress_warning_dialogs
        self.original_error = dm._suppress_error_dialogs
        
        # Apply new settings
        dm.set_suppress_dialogs(True)
        dm.set_suppress_info_dialogs(self.suppress_info)
        dm.set_suppress_warning_dialogs(self.suppress_warning)
        dm.set_suppress_error_dialogs(self.suppress_error)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore original settings
        dm = DialogManager.get_instance()
        dm.set_suppress_dialogs(self.original_global)
        dm.set_suppress_info_dialogs(self.original_info)
        dm.set_suppress_warning_dialogs(self.original_warning)
        dm.set_suppress_error_dialogs(self.original_error)
