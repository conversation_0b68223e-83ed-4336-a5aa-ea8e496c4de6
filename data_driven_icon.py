"""
Data Driven Icon Generator

This module provides a function to generate a simple data-driven icon
that can be used in the loading screen.
"""

from PyQt6 import QtGui, Qt<PERSON><PERSON>

def create_data_driven_icon(size=64, primary_color=None, secondary_color=None):
    """
    Create a simple data-driven icon as a QPixmap.
    
    Args:
        size: Size of the icon in pixels
        primary_color: Primary color for the icon (QColor)
        secondary_color: Secondary color for the icon (QColor)
        
    Returns:
        QPixmap containing the icon
    """
    # Use default colors if not provided
    if primary_color is None:
        primary_color = QtGui.QColor("#007acc")  # Blue
    if secondary_color is None:
        secondary_color = QtGui.QColor("#4CAF50")  # Green
    
    # Create a pixmap
    pixmap = QtGui.QPixmap(size, size)
    pixmap.fill(QtCore.Qt.GlobalColor.transparent)
    
    # Create a painter
    painter = QtGui.QPainter(pixmap)
    painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)
    
    # Draw a data visualization icon
    
    # Draw a chart background (rounded rectangle)
    chart_rect = QtCore.QRectF(size * 0.1, size * 0.1, size * 0.8, size * 0.8)
    painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255, 40), 1))
    painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255, 20)))
    painter.drawRoundedRect(chart_rect, size * 0.1, size * 0.1)
    
    # Draw a bar chart
    bar_width = size * 0.1
    bar_spacing = size * 0.05
    bar_bottom = size * 0.7
    
    # Bar heights (percentage of chart height)
    bar_heights = [0.3, 0.5, 0.2, 0.6, 0.4]
    
    # Draw bars
    for i, height_pct in enumerate(bar_heights):
        bar_height = size * height_pct
        bar_x = size * 0.2 + i * (bar_width + bar_spacing)
        bar_y = bar_bottom - bar_height
        
        # Alternate colors
        if i % 2 == 0:
            painter.setBrush(QtGui.QBrush(primary_color))
        else:
            painter.setBrush(QtGui.QBrush(secondary_color))
        
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawRoundedRect(QtCore.QRectF(bar_x, bar_y, bar_width, bar_height), 
                               size * 0.02, size * 0.02)
    
    # Draw a trend line
    painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255, 200), 2))
    
    # Create points for the line
    points = []
    for i, height_pct in enumerate(bar_heights):
        point_x = size * 0.2 + i * (bar_width + bar_spacing) + bar_width / 2
        point_y = bar_bottom - (size * height_pct) - (size * 0.05)
        points.append(QtCore.QPointF(point_x, point_y))
    
    # Draw the line connecting the points
    for i in range(len(points) - 1):
        painter.drawLine(points[i], points[i + 1])
    
    # Draw points at each data point
    for point in points:
        painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255)))
        painter.drawEllipse(point, size * 0.03, size * 0.03)
    
    # End painting
    painter.end()
    
    return pixmap
