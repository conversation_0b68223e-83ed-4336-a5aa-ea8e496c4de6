"""
Login Dialog for Schwab API Authentication

This module provides a PyQt6 dialog for entering Schwab API credentials
and performing the OAuth authentication flow.
"""

import os
import json
import logging
import base64
from typing import Op<PERSON>, <PERSON><PERSON>
from PyQt6 import QtWidgets, QtCore, QtGui
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QLabel, QProgressBar,
    QCheckBox, QGroupBox, QMessageBox, QFrame
)

logger = logging.getLogger(__name__)

class LoginDialog(QDialog):
    """
    Dialog for Schwab API authentication.
    Handles credential input and OAuth flow.
    """

    # Signals
    authentication_successful = pyqtSignal(str, str, str)  # api_key, app_secret, callback_url
    authentication_failed = pyqtSignal(str)  # error_message
    skip_oauth_requested = pyqtSignal(str, str, str)  # api_key, app_secret, callback_url

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Schwab API Login")
        self.setModal(True)
        self.setFixedSize(500, 600)

        # Credentials
        self.api_key = ""
        self.app_secret = ""
        self.callback_url = "https://127.0.0.1"  # Default to match common Schwab setup

        # Settings file for saving credentials (encrypted)
        self.settings_file = "schwab_settings.json"

        self.init_ui()
        self.load_saved_credentials()

        # Apply theme
        self.apply_theme()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("Schwab API Authentication")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # Credentials group
        creds_group = QGroupBox("API Credentials")
        creds_layout = QFormLayout(creds_group)

        # API Key input
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your Schwab API Key")
        self.api_key_input.textChanged.connect(self.validate_inputs)
        creds_layout.addRow("API Key:", self.api_key_input)

        # App Secret input
        self.app_secret_input = QLineEdit()
        self.app_secret_input.setPlaceholderText("Enter your App Secret")
        self.app_secret_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.app_secret_input.textChanged.connect(self.validate_inputs)
        creds_layout.addRow("App Secret:", self.app_secret_input)

        # Callback URL input
        self.callback_url_input = QLineEdit()
        self.callback_url_input.setText(self.callback_url)
        self.callback_url_input.setPlaceholderText("https://127.0.0.1")
        creds_layout.addRow("Callback URL:", self.callback_url_input)

        # Show/Hide password checkbox
        self.show_password_cb = QCheckBox("Show App Secret")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        creds_layout.addRow("", self.show_password_cb)

        # Remember credentials checkbox
        self.remember_cb = QCheckBox("Remember credentials (API key and app secret saved locally)")
        self.remember_cb.setChecked(True)
        creds_layout.addRow("", self.remember_cb)

        # Clear saved credentials button
        clear_layout = QHBoxLayout()
        clear_layout.addStretch()
        self.clear_saved_btn = QPushButton("Clear Saved")
        self.clear_saved_btn.clicked.connect(self.clear_saved_credentials)
        self.clear_saved_btn.setStyleSheet("color: #cc6600; font-size: 11px;")
        self.clear_saved_btn.setToolTip("Clear saved API credentials from local storage")
        clear_layout.addWidget(self.clear_saved_btn)
        creds_layout.addRow("", clear_layout)

        layout.addWidget(creds_group)

        # Authentication status
        self.status_group = QGroupBox("Authentication Status")
        status_layout = QVBoxLayout(self.status_group)

        self.status_label = QLabel("Ready to authenticate")
        self.status_label.setStyleSheet("color: #666; padding: 5px;")
        status_layout.addWidget(self.status_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        layout.addWidget(self.status_group)

        # Buttons
        button_layout = QHBoxLayout()

        self.test_connection_btn = QPushButton("Test Connection")
        self.test_connection_btn.clicked.connect(self.test_connection)
        self.test_connection_btn.setEnabled(False)
        button_layout.addWidget(self.test_connection_btn)

        self.skip_oauth_btn = QPushButton("Skip OAuth")
        self.skip_oauth_btn.clicked.connect(self.skip_oauth)
        self.skip_oauth_btn.setEnabled(False)
        self.skip_oauth_btn.setToolTip("Login without OAuth flow (for when OAuth is not required)")
        button_layout.addWidget(self.skip_oauth_btn)

        self.authenticate_btn = QPushButton("Full OAuth")
        self.authenticate_btn.clicked.connect(self.authenticate)
        self.authenticate_btn.setEnabled(False)
        self.authenticate_btn.setStyleSheet("font-weight: bold;")
        self.authenticate_btn.setToolTip("Complete OAuth authentication flow")
        button_layout.addWidget(self.authenticate_btn)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        # Help button
        help_layout = QHBoxLayout()
        help_layout.addStretch()

        self.help_btn = QPushButton("Need Help?")
        self.help_btn.clicked.connect(self.show_help)
        self.help_btn.setStyleSheet("color: #0066cc; border: none; text-decoration: underline;")
        help_layout.addWidget(self.help_btn)

        layout.addLayout(help_layout)

    def apply_theme(self):
        """Apply consistent theming"""
        try:
            # Try to import theme colors from the main application
            import theme
            colors = theme.DEFAULT

            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {colors['background']};
                    color: {colors['text']};
                }}
                QGroupBox {{
                    font-weight: bold;
                    border: 2px solid {colors['borders']};
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 10px;
                }}
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }}
                QLineEdit {{
                    padding: 8px;
                    border: 1px solid {colors['borders']};
                    border-radius: 4px;
                    background-color: {colors['control_panel']};
                    color: {colors['text']};
                }}
                QPushButton {{
                    padding: 8px 16px;
                    border: 1px solid {colors['borders']};
                    border-radius: 4px;
                    background-color: {colors['control_panel']};
                    color: {colors['text']};
                }}
                QPushButton:hover {{
                    background-color: {colors['primary_accent']};
                }}
                QPushButton:pressed {{
                    background-color: {colors['pressed_accent']};
                }}
                QPushButton:disabled {{
                    background-color: {colors['borders']};
                    color: #888;
                }}
            """)
        except ImportError:
            # Fallback styling if theme module not available
            pass

    def validate_inputs(self):
        """Validate input fields and enable/disable buttons"""
        api_key = self.api_key_input.text().strip()
        app_secret = self.app_secret_input.text().strip()

        is_valid = len(api_key) > 0 and len(app_secret) > 0

        self.authenticate_btn.setEnabled(is_valid)
        self.skip_oauth_btn.setEnabled(is_valid)
        self.test_connection_btn.setEnabled(is_valid)

    def toggle_password_visibility(self, checked):
        """Toggle password visibility"""
        if checked:
            self.app_secret_input.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.app_secret_input.setEchoMode(QLineEdit.EchoMode.Password)

    def _encode_credential(self, credential: str) -> str:
        """Encode credential using base64 for basic obfuscation"""
        try:
            return base64.b64encode(credential.encode('utf-8')).decode('utf-8')
        except Exception:
            return credential

    def _decode_credential(self, encoded_credential: str) -> str:
        """Decode credential from base64"""
        try:
            return base64.b64decode(encoded_credential.encode('utf-8')).decode('utf-8')
        except Exception:
            return encoded_credential

    def load_saved_credentials(self):
        """Load saved credentials from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)

                # Load API key
                api_key = settings.get('api_key', '')
                if api_key:
                    self.api_key_input.setText(api_key)

                # Load app secret (encoded)
                encoded_app_secret = settings.get('app_secret_encoded', '')
                if encoded_app_secret:
                    try:
                        app_secret = self._decode_credential(encoded_app_secret)
                        self.app_secret_input.setText(app_secret)
                    except Exception as e:
                        logger.warning(f"Failed to decode app secret: {e}")

                # Load callback URL
                self.callback_url_input.setText(settings.get('callback_url', self.callback_url))

                logger.info("Loaded saved credentials")

                # Update status to show credentials were loaded
                if api_key and encoded_app_secret:
                    self.status_label.setText("Saved credentials loaded successfully")
                    self.status_label.setStyleSheet("color: green; padding: 5px;")
                elif api_key:
                    self.status_label.setText("Saved API key loaded (app secret not saved)")
                    self.status_label.setStyleSheet("color: orange; padding: 5px;")

        except Exception as e:
            logger.warning(f"Failed to load saved credentials: {e}")

    def save_credentials(self):
        """Save credentials to file if remember is checked"""
        if not self.remember_cb.isChecked():
            return

        try:
            api_key = self.api_key_input.text().strip()
            app_secret = self.app_secret_input.text().strip()
            callback_url = self.callback_url_input.text().strip()

            settings = {
                'api_key': api_key,
                'callback_url': callback_url,
            }

            # Save app secret with basic encoding for obfuscation
            if app_secret:
                settings['app_secret_encoded'] = self._encode_credential(app_secret)

            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=2)

            logger.info("Saved API credentials (including app secret)")
        except Exception as e:
            logger.warning(f"Failed to save credentials: {e}")

    def clear_saved_credentials(self):
        """Clear saved credentials from file"""
        try:
            if os.path.exists(self.settings_file):
                os.remove(self.settings_file)
                logger.info("Cleared saved credentials")
                self.status_label.setText("Saved credentials cleared")
                self.status_label.setStyleSheet("color: orange; padding: 5px;")
            else:
                self.status_label.setText("No saved credentials to clear")
                self.status_label.setStyleSheet("color: #666; padding: 5px;")
        except Exception as e:
            logger.warning(f"Failed to clear saved credentials: {e}")
            self.status_label.setText(f"Error clearing credentials: {str(e)}")
            self.status_label.setStyleSheet("color: red; padding: 5px;")

    def test_connection(self):
        """Test the API connection without full authentication"""
        self.status_label.setText("Testing connection...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # For now, just validate the inputs
        api_key = self.api_key_input.text().strip()
        app_secret = self.app_secret_input.text().strip()
        callback_url = self.callback_url_input.text().strip()

        if not api_key or not app_secret:
            self.status_label.setText("Please enter both API Key and App Secret")
            self.progress_bar.setVisible(False)
            return

        if not callback_url.startswith('https://'):
            self.status_label.setText("Callback URL must use HTTPS")
            self.progress_bar.setVisible(False)
            return

        self.status_label.setText("Credentials format appears valid. Click 'Authenticate' to proceed.")
        self.progress_bar.setVisible(False)

    def skip_oauth(self):
        """Skip OAuth and login with just credentials"""
        api_key = self.api_key_input.text().strip()
        app_secret = self.app_secret_input.text().strip()
        callback_url = self.callback_url_input.text().strip()

        if not api_key or not app_secret:
            QMessageBox.warning(self, "Error", "Please enter both API Key and App Secret")
            return

        self.status_label.setText("Logging in without OAuth...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # Disable buttons during login
        self.authenticate_btn.setEnabled(False)
        self.skip_oauth_btn.setEnabled(False)
        self.test_connection_btn.setEnabled(False)

        try:
            # Save credentials if requested
            self.save_credentials()

            # Emit signal for skip OAuth
            self.skip_oauth_requested.emit(api_key, app_secret, callback_url)

            self.status_label.setText("Login initiated without OAuth flow.")

        except Exception as e:
            self.status_label.setText(f"Login failed: {str(e)}")
            self.progress_bar.setVisible(False)
            self.authenticate_btn.setEnabled(True)
            self.skip_oauth_btn.setEnabled(True)
            self.test_connection_btn.setEnabled(True)
            self.authentication_failed.emit(str(e))

    def authenticate(self):
        """Start the authentication process"""
        api_key = self.api_key_input.text().strip()
        app_secret = self.app_secret_input.text().strip()
        callback_url = self.callback_url_input.text().strip()

        if not api_key or not app_secret:
            QMessageBox.warning(self, "Error", "Please enter both API Key and App Secret")
            return

        self.status_label.setText("Starting authentication...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # Disable buttons during authentication
        self.authenticate_btn.setEnabled(False)
        self.skip_oauth_btn.setEnabled(False)
        self.test_connection_btn.setEnabled(False)

        try:
            # Save credentials if requested
            self.save_credentials()

            # Emit signal with credentials
            self.authentication_successful.emit(api_key, app_secret, callback_url)

            self.status_label.setText("Authentication initiated. Please complete the OAuth flow in your browser.")

        except Exception as e:
            self.status_label.setText(f"Authentication failed: {str(e)}")
            self.progress_bar.setVisible(False)
            self.authenticate_btn.setEnabled(True)
            self.skip_oauth_btn.setEnabled(True)
            self.test_connection_btn.setEnabled(True)
            self.authentication_failed.emit(str(e))

    def authentication_completed(self, success: bool, message: str = ""):
        """Called when authentication process completes"""
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("Authentication successful!")
            self.status_label.setStyleSheet("color: green; padding: 5px;")

            # Close dialog after short delay
            QtCore.QTimer.singleShot(1500, self.accept)
        else:
            self.status_label.setText(f"Authentication failed: {message}")
            self.status_label.setStyleSheet("color: red; padding: 5px;")
            self.authenticate_btn.setEnabled(True)
            self.skip_oauth_btn.setEnabled(True)
            self.test_connection_btn.setEnabled(True)

    def show_help(self):
        """Show help dialog"""
        help_text = """
        <h3>Schwab API Login Help</h3>

        <p><b>Required Credentials:</b><br>
        • API Key (Consumer Key)<br>
        • App Secret (Consumer Secret)<br>
        • Callback URL (must match your app configuration)</p>

        <p><b>Login Options:</b><br>
        • <b>Skip OAuth:</b> Quick login without browser (recommended)<br>
        • <b>Full OAuth:</b> Complete OAuth flow with browser authentication</p>

        <p><b>Important Notes:</b><br>
        • Your app must be approved before you can use it<br>
        • Keep your credentials secure<br>
        • The callback URL must match exactly</p>

        <p><b>Troubleshooting:</b><br>
        • Ensure your app status is "Ready for Use"<br>
        • Check that callback URL matches exactly<br>
        • Make sure you're using the correct credentials</p>
        """

        msg = QMessageBox(self)
        msg.setWindowTitle("Schwab API Help")
        msg.setTextFormat(Qt.TextFormat.RichText)
        msg.setText(help_text)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()

    def get_credentials(self) -> Tuple[str, str, str]:
        """Get the entered credentials"""
        return (
            self.api_key_input.text().strip(),
            self.app_secret_input.text().strip(),
            self.callback_url_input.text().strip()
        )
