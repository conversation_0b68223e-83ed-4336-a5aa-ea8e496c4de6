"""
Pivot Validator Module

This module provides a separate model for validating pivot crossings.
It uses a different set of features and a different model than the main crossing classifier
to provide a second opinion on crossing signals.
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.calibration import CalibratedClassifierCV
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import logging

# Note: Main logging configuration is in main.py
# This is just a fallback in case this module is used standalone
logging.basicConfig(
    level=logging.ERROR,  # Minimal verbosity, only errors
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pivot_validator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("PivotValidator")
logger.setLevel(logging.ERROR)

class PivotValidator:
    """
    A separate model for validating pivot crossings.

    This class provides a second opinion on crossing signals by using a different
    set of features and a different model than the main crossing classifier.
    """

    def __init__(self):
        """Initialize the PivotValidator."""
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = []

    def extract_features(self, data, idx, vector_values=None):
        """
        Extract features specific to pivot quality.

        Args:
            data: DataFrame containing price data
            idx: Index of the crossing point
            vector_values: Series containing vector values (optional)

        Returns:
            dict: Dictionary of features
        """
        if idx < 10 or idx >= len(data) - 1:
            return None

        try:
            # Basic price features
            current_price = data['Close'].iloc[idx]
            prev_price = data['Close'].iloc[idx-1]

            # Calculate pivot strength features
            price_change_pct = (current_price / prev_price - 1) * 100

            # Volume features
            current_volume = data['Volume'].iloc[idx] if 'Volume' in data.columns else 0
            avg_volume_10 = data['Volume'].iloc[idx-10:idx].mean() if 'Volume' in data.columns else 0
            volume_ratio = current_volume / avg_volume_10 if avg_volume_10 > 0 else 1.0

            # Volatility features
            high_low_range = (data['High'].iloc[idx] - data['Low'].iloc[idx]) / data['Close'].iloc[idx] * 100
            avg_hl_range_10 = ((data['High'].iloc[idx-10:idx] - data['Low'].iloc[idx-10:idx]) / data['Close'].iloc[idx-10:idx]).mean() * 100
            volatility_ratio = high_low_range / avg_hl_range_10 if avg_hl_range_10 > 0 else 1.0

            # Trend features
            price_5d_ago = data['Close'].iloc[idx-5]
            price_10d_ago = data['Close'].iloc[idx-10]
            trend_5d = (current_price / price_5d_ago - 1) * 100
            trend_10d = (current_price / price_10d_ago - 1) * 100

            # Vector-related features
            if vector_values is not None:
                vector_price = vector_values.iloc[idx]
                vector_5d_ago = vector_values.iloc[idx-5]
                vector_10d_ago = vector_values.iloc[idx-10]

                price_to_vector_ratio = current_price / vector_price
                vector_trend_5d = (vector_price / vector_5d_ago - 1) * 100
                vector_trend_10d = (vector_price / vector_10d_ago - 1) * 100

                # Angle of approach
                approach_angle = abs((current_price / vector_price - prev_price / vector_values.iloc[idx-1]) * 100)
            else:
                price_to_vector_ratio = 1.0
                vector_trend_5d = 0.0
                vector_trend_10d = 0.0
                approach_angle = 0.0

            # Candlestick pattern features
            body_size = abs(data['Close'].iloc[idx] - data['Open'].iloc[idx]) / data['Open'].iloc[idx] * 100
            upper_shadow = (data['High'].iloc[idx] - max(data['Open'].iloc[idx], data['Close'].iloc[idx])) / data['Open'].iloc[idx] * 100
            lower_shadow = (min(data['Open'].iloc[idx], data['Close'].iloc[idx]) - data['Low'].iloc[idx]) / data['Open'].iloc[idx] * 100

            # Compile features
            features = {
                'price_change_pct': price_change_pct,
                'volume_ratio': volume_ratio,
                'high_low_range': high_low_range,
                'volatility_ratio': volatility_ratio,
                'trend_5d': trend_5d,
                'trend_10d': trend_10d,
                'price_to_vector_ratio': price_to_vector_ratio,
                'vector_trend_5d': vector_trend_5d,
                'vector_trend_10d': vector_trend_10d,
                'approach_angle': approach_angle,
                'body_size': body_size,
                'upper_shadow': upper_shadow,
                'lower_shadow': lower_shadow
            }

            return features

        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return None

    def create_training_dataset(self, data, vector_values, crossings, outcomes):
        """
        Create a training dataset from historical crossings.

        Args:
            data: DataFrame containing price data
            vector_values: Series containing vector values
            crossings: List of crossing indices
            outcomes: Dictionary mapping crossing indices to outcomes (1=valid, 0=invalid)

        Returns:
            tuple: (X, y) feature matrix and target vector
        """
        X = []
        y = []

        for idx in crossings:
            if idx not in outcomes:
                continue

            features = self.extract_features(data, idx, vector_values)
            if features is None:
                continue

            X.append(list(features.values()))
            y.append(outcomes[idx])

        if not X:
            return None, None

        self.feature_names = list(features.keys())
        return np.array(X), np.array(y)

    def train(self, data, vector_values, crossings, outcomes):
        """
        Train the pivot validator model.

        Args:
            data: DataFrame containing price data
            vector_values: Series containing vector values
            crossings: List of crossing indices
            outcomes: Dictionary mapping crossing indices to outcomes (1=valid, 0=invalid)

        Returns:
            bool: True if training was successful, False otherwise
        """
        try:
            X, y = self.create_training_dataset(data, vector_values, crossings, outcomes)
            if X is None or len(X) < 10 or len(set(y)) < 2:
                logger.warning("Insufficient data for training pivot validator")
                return False

            # Scale the features
            X_scaled = self.scaler.fit_transform(X)

            # Create and train the model
            base_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=5,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )

            # Use calibration to get reliable probabilities
            self.model = CalibratedClassifierCV(
                base_model,
                method='sigmoid',
                cv=5
            )

            self.model.fit(X_scaled, y)

            # Evaluate on training data
            y_pred = self.model.predict(X_scaled)
            accuracy = accuracy_score(y, y_pred)
            precision = precision_score(y, y_pred, zero_division=0)
            recall = recall_score(y, y_pred, zero_division=0)
            f1 = f1_score(y, y_pred, zero_division=0)

            logger.info(f"Pivot validator trained on {len(X)} samples")
            logger.info(f"Training metrics - Accuracy: {accuracy:.3f}, Precision: {precision:.3f}, Recall: {recall:.3f}, F1: {f1:.3f}")

            self.is_trained = True
            return True

        except Exception as e:
            logger.error(f"Error training pivot validator: {str(e)}")
            return False

    def predict(self, data, idx, vector_values=None):
        """
        Predict whether a pivot crossing is valid.

        Args:
            data: DataFrame containing price data
            idx: Index of the crossing point
            vector_values: Series containing vector values (optional)

        Returns:
            dict: Prediction result with probability and validity
        """
        if not self.is_trained or self.model is None:
            return None

        try:
            features = self.extract_features(data, idx, vector_values)
            if features is None:
                return None

            # Convert to array and scale
            X = np.array([list(features.values())])
            X_scaled = self.scaler.transform(X)

            # Get prediction and probability
            prediction = self.model.predict(X_scaled)[0]
            probability = self.model.predict_proba(X_scaled)[0][1]  # Probability of class 1 (valid)

            result = {
                'prediction': int(prediction),
                'probability': float(probability),
                'is_valid': bool(prediction == 1),
                'features': features
            }

            logger.debug(f"Pivot validation: {'Valid' if result['is_valid'] else 'Invalid'} with {probability:.3f} probability")
            return result

        except Exception as e:
            logger.error(f"Error predicting pivot validity: {str(e)}")
            return None

    def get_feature_importance(self):
        """
        Get feature importance from the model.

        Returns:
            dict: Feature importance scores
        """
        if not self.is_trained or self.model is None or not hasattr(self.model, 'base_estimator_'):
            return None

        try:
            # For CalibratedClassifierCV, we need to access the base estimator
            base_model = self.model.base_estimator_

            if hasattr(base_model, 'feature_importances_'):
                importances = base_model.feature_importances_
                feature_importance = dict(zip(self.feature_names, importances))
                return feature_importance
            else:
                return None

        except Exception as e:
            logger.error(f"Error getting feature importance: {str(e)}")
            return None
