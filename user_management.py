"""
User Management Module for Schwab API Integration

This module handles user authentication, session management, and credential storage
for the Schwab API integration.
"""

import os
import json
import logging
import hashlib
from typing import Optional, Dict, Any
from PyQt6 import QtCore
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer

from schwab_api import schwab_api

logger = logging.getLogger(__name__)

class AuthenticationWorker(QThread):
    """
    Worker thread for handling Schwab API authentication.
    Prevents UI blocking during OAuth flow.
    """

    authentication_completed = pyqtSignal(bool, str)  # success, message

    def __init__(self, api_key: str, app_secret: str, callback_url: str):
        super().__init__()
        self.api_key = api_key
        self.app_secret = app_secret
        self.callback_url = callback_url

    def run(self):
        """Run the authentication process"""
        try:
            logger.info("Authentication worker starting...")

            # Initialize the API
            success = schwab_api.initialize_api(self.api_key, self.app_secret, self.callback_url)

            if not success:
                # Need to authenticate - this will open a browser
                logger.info("No existing token found, starting OAuth flow...")
                success = schwab_api.authenticate()
            else:
                logger.info("Using existing token")

            if success:
                logger.info("Authentication worker completed successfully")
                self.authentication_completed.emit(True, "Authentication successful")
            else:
                logger.warning("Authentication worker failed")
                self.authentication_completed.emit(False, "Authentication failed")

        except Exception as e:
            logger.error(f"Authentication worker error: {e}")
            self.authentication_completed.emit(False, str(e))

class UserManager(QObject):
    """
    Manages user authentication and session state for Schwab API.
    """

    # Signals
    login_successful = pyqtSignal()
    login_failed = pyqtSignal(str)  # error_message
    logout_completed = pyqtSignal()
    session_expired = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.current_user = None
        self.session_file = "schwab_session.json"
        self.auth_worker = None
        self.session_timer = None

        # Initialize timer only when we have a Qt application running
        # Use a delayed initialization to avoid Qt threading issues
        try:
            QtCore.QTimer.singleShot(0, self._init_timer)
        except Exception as e:
            logger.debug(f"Could not schedule timer initialization: {e}")
            # Timer is optional, continue without it

        # Try to restore previous session
        self.restore_session()

    def _init_timer(self):
        """Initialize the session timer when Qt application is ready"""
        try:
            # Only create timer if we're in a Qt application context
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is not None and hasattr(app, 'thread') and QtCore.QThread.currentThread() == app.thread():
                self.session_timer = QTimer(self)
                self.session_timer.timeout.connect(self.check_session_validity)
                self.session_timer.start(300000)  # Check every 5 minutes
                logger.debug("Session monitoring timer initialized")
            else:
                logger.debug("Not in main Qt thread, skipping timer initialization")
        except Exception as e:
            logger.debug(f"Could not initialize session timer: {e}")
            # Timer is optional, so we can continue without it

    def skip_oauth_login(self, api_key: str, app_secret: str, callback_url: str = "https://127.0.0.1"):
        """
        Login without OAuth flow - just store credentials.

        Args:
            api_key: Schwab API key
            app_secret: Schwab app secret
            callback_url: OAuth callback URL
        """
        try:
            # Validate inputs
            if not api_key or not app_secret:
                raise ValueError("API key and app secret are required")

            # Initialize API with credentials
            success = schwab_api.initialize_api(api_key, app_secret, callback_url)
            if not success:
                # Try skip OAuth login
                success = schwab_api.skip_oauth_login()

            if success:
                # Store user information
                api_key_preview = f"{api_key[:8]}..." if len(api_key) > 8 else api_key

                self.current_user = {
                    'api_key': api_key,
                    'api_key_preview': api_key_preview,
                    'app_secret': app_secret,
                    'callback_url': callback_url,
                    'login_time': QtCore.QDateTime.currentDateTime().toString(),
                    'session_id': self._generate_session_id(api_key),
                    'oauth_skipped': True
                }

                # Save session and emit success
                self.save_session()
                self.login_successful.emit()
                logger.info(f"Skip OAuth login successful for API key: {api_key_preview}")
            else:
                self.login_failed.emit("Failed to login without OAuth")

        except Exception as e:
            logger.error(f"Skip OAuth login failed: {e}")
            self.login_failed.emit(str(e))

    def login(self, api_key: str, app_secret: str, callback_url: str = "https://127.0.0.1"):
        """
        Initiate login process with Schwab API.

        Args:
            api_key: Schwab API key
            app_secret: Schwab app secret
            callback_url: OAuth callback URL
        """
        try:
            # Create preview of API key for display (first 8 chars + ...)
            api_key_preview = api_key[:8] + "..." if len(api_key) > 8 else api_key

            # Store user info
            self.current_user = {
                'api_key': api_key,
                'api_key_preview': api_key_preview,
                'app_secret': app_secret,
                'callback_url': callback_url,
                'login_time': QtCore.QDateTime.currentDateTime().toString(),
                'session_id': self._generate_session_id(api_key)
            }

            # Start authentication in worker thread
            self.auth_worker = AuthenticationWorker(api_key, app_secret, callback_url)
            self.auth_worker.authentication_completed.connect(self.on_authentication_completed)
            self.auth_worker.start()

            logger.info(f"Starting login process for API key: {api_key_preview}")

        except Exception as e:
            logger.error(f"Login initiation failed: {e}")
            self.login_failed.emit(str(e))

    def on_authentication_completed(self, success: bool, message: str):
        """Handle authentication completion"""
        if success:
            # Save session
            self.save_session()
            self.login_successful.emit()
            logger.info("User login successful")
        else:
            self.current_user = None
            self.login_failed.emit(message)
            logger.error(f"User login failed: {message}")

        # Clean up worker
        if self.auth_worker:
            self.auth_worker.deleteLater()
            self.auth_worker = None

    def logout(self):
        """Logout from Schwab API"""
        try:
            # Disconnect from API
            schwab_api.disconnect()

            # Clear user session
            self.current_user = None

            # Remove session file
            if os.path.exists(self.session_file):
                os.remove(self.session_file)

            # Remove token file
            if os.path.exists(schwab_api.token_path):
                os.remove(schwab_api.token_path)

            self.logout_completed.emit()
            logger.info("User logout completed")

        except Exception as e:
            logger.error(f"Logout error: {e}")
            # Still emit logout completed even if there were errors
            self.logout_completed.emit()

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current user information"""
        return self.current_user.copy() if self.current_user else None

    def is_logged_in(self) -> bool:
        """Check if user is currently logged in"""
        return self.current_user is not None and schwab_api.is_connected()

    def save_session(self):
        """Save current session to file"""
        if not self.current_user:
            return

        try:
            # Don't save sensitive information like app_secret
            session_data = {
                'api_key_preview': self.current_user.get('api_key_preview'),
                'callback_url': self.current_user.get('callback_url'),
                'login_time': self.current_user.get('login_time'),
                'session_id': self.current_user.get('session_id'),
                'saved_time': QtCore.QDateTime.currentDateTime().toString()
            }

            with open(self.session_file, 'w') as f:
                json.dump(session_data, f, indent=2)

            logger.info("Session saved")

        except Exception as e:
            logger.warning(f"Failed to save session: {e}")

    def restore_session(self):
        """Try to restore previous session"""
        try:
            if not os.path.exists(self.session_file):
                return

            with open(self.session_file, 'r') as f:
                session_data = json.load(f)

            # Check if we have a valid token file
            if not os.path.exists(schwab_api.token_path):
                logger.info("No token file found, cannot restore session")
                return

            # Try to restore the session (this will require re-entering credentials)
            logger.info(f"Found previous session for: {session_data.get('api_key_preview', 'Unknown')}")

            # Note: We can't fully restore without the app_secret, so we just keep the session info
            # The user will need to log in again, but we can show them their previous API key

        except Exception as e:
            logger.warning(f"Failed to restore session: {e}")

    def check_session_validity(self):
        """Check if current session is still valid"""
        if not self.is_logged_in():
            return

        try:
            # Test the connection with the improved test method
            if schwab_api.is_connected():
                connection_ok = schwab_api.test_connection()
                if not connection_ok:
                    # Connection test failed, session might be expired
                    logger.warning("Session validation failed - connection test failed")
                    self.handle_session_expiry()
            else:
                logger.warning("Session validation failed - not connected")
                self.handle_session_expiry()

        except Exception as e:
            logger.warning(f"Session validation error: {e}")
            self.handle_session_expiry()

    def handle_session_expiry(self):
        """Handle session expiration"""
        logger.info("Session expired, logging out user")

        # Clear current session
        self.current_user = None

        # Disconnect API
        schwab_api.disconnect()

        # Emit session expired signal
        self.session_expired.emit()

        # Also emit logout completed for UI updates
        self.logout_completed.emit()

    def _generate_session_id(self, api_key: str) -> str:
        """Generate a unique session ID"""
        import time
        data = f"{api_key}_{time.time()}"
        return hashlib.md5(data.encode()).hexdigest()[:16]

    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        if not self.current_user:
            return {'logged_in': False}

        return {
            'logged_in': True,
            'api_key_preview': self.current_user.get('api_key_preview'),
            'login_time': self.current_user.get('login_time'),
            'session_id': self.current_user.get('session_id'),
            'api_connected': schwab_api.is_connected()
        }

    def refresh_authentication(self):
        """Refresh the current authentication if possible"""
        if not self.current_user:
            logger.warning("Cannot refresh authentication - no current user")
            return False

        try:
            # Try to reinitialize with current credentials
            api_key = self.current_user.get('api_key')
            app_secret = self.current_user.get('app_secret')
            callback_url = self.current_user.get('callback_url')

            if not api_key or not app_secret:
                logger.warning("Cannot refresh - missing credentials")
                return False

            success = schwab_api.initialize_api(api_key, app_secret, callback_url)
            if not success:
                success = schwab_api.authenticate()

            return success

        except Exception as e:
            logger.error(f"Authentication refresh failed: {e}")
            return False

# Global user manager instance - will be initialized when needed
user_manager = None

def get_user_manager():
    """Get the global user manager instance, creating it if necessary"""
    global user_manager
    if user_manager is None:
        user_manager = UserManager()
    return user_manager
