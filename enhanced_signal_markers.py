"""
Enhanced Signal Markers for Market Odds Charts

This module provides enhanced signal markers with glow effects and interactive popups
for displaying signal details when clicked.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import numpy as np

class SignalPopup(QtWidgets.QDialog):
    """
    Popup dialog for displaying signal details when a signal marker is clicked.
    """
    def __init__(self, signal_data, parent=None):
        """
        Initialize the signal popup.

        Args:
            signal_data: Dictionary containing signal information
            parent: Parent widget
        """
        super().__init__(parent)
        self.signal_data = signal_data
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set window properties
        self.setWindowTitle("Signal Details")
        self.setStyleSheet("""
            QDialog {
                background-color: #1e2a3a;
                color: #e0e0e0;
                border: 1px solid #007acc;
                border-radius: 8px;
            }
            QLabel {
                color: #e0e0e0;
                font-size: 12px;
            }
            QLabel#titleLabel {
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2d2d2d;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                                stop:0 #007acc, stop:1 #00aaff);
                border-radius: 3px;
            }
        """)

        # Main layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setSpacing(10)

        # Signal type and direction
        signal_type = self.signal_data.get('type', 'Unknown')
        direction = self.signal_data.get('direction', 'Unknown')
        strength = self.signal_data.get('strength', 'Unknown')

        # Title label
        title_text = f"{signal_type} ({direction.upper()})"
        title_label = QtWidgets.QLabel(title_text)
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Add horizontal line
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        line.setStyleSheet("background-color: #555555;")
        layout.addWidget(line)

        # Create grid for metrics
        metrics_grid = QtWidgets.QGridLayout()
        metrics_grid.setColumnStretch(1, 1)
        metrics_grid.setSpacing(8)

        # Add confidence
        confidence = self.signal_data.get('confidence', 0)
        metrics_grid.addWidget(QtWidgets.QLabel("Confidence:"), 0, 0)
        confidence_bar = QtWidgets.QProgressBar()
        confidence_bar.setRange(0, 100)
        confidence_bar.setValue(int(confidence))
        confidence_bar.setFormat(f"{confidence:.1f}%")
        confidence_bar.setTextVisible(True)
        metrics_grid.addWidget(confidence_bar, 0, 1)

        # Add strength
        metrics_grid.addWidget(QtWidgets.QLabel("Strength:"), 1, 0)
        metrics_grid.addWidget(QtWidgets.QLabel(strength.upper()), 1, 1)

        # Add magnitude if available
        if 'magnitude' in self.signal_data:
            magnitude = self.signal_data.get('magnitude', 0)
            metrics_grid.addWidget(QtWidgets.QLabel("Magnitude:"), 2, 0)
            metrics_grid.addWidget(QtWidgets.QLabel(f"{magnitude:.2f}%"), 2, 1)

        # Add timestamp if available
        if 'timestamp' in self.signal_data:
            timestamp = self.signal_data.get('timestamp', '')
            metrics_grid.addWidget(QtWidgets.QLabel("Time:"), 3, 0)
            metrics_grid.addWidget(QtWidgets.QLabel(str(timestamp)), 3, 1)

        # Add symbol if available
        if 'symbol' in self.signal_data:
            symbol = self.signal_data.get('symbol', '')
            metrics_grid.addWidget(QtWidgets.QLabel("Symbol:"), 4, 0)
            metrics_grid.addWidget(QtWidgets.QLabel(symbol), 4, 1)

        # Add timeframe if available
        if 'timeframe' in self.signal_data:
            timeframe = self.signal_data.get('timeframe', '')
            metrics_grid.addWidget(QtWidgets.QLabel("Timeframe:"), 5, 0)
            metrics_grid.addWidget(QtWidgets.QLabel(timeframe), 5, 1)

        # Add components if available
        if 'components' in self.signal_data:
            components = self.signal_data.get('components', {})
            row = 6
            for key, value in components.items():
                metrics_grid.addWidget(QtWidgets.QLabel(f"{key.capitalize()}:"), row, 0)
                if isinstance(value, float):
                    metrics_grid.addWidget(QtWidgets.QLabel(f"{value:.2f}"), row, 1)
                else:
                    metrics_grid.addWidget(QtWidgets.QLabel(str(value)), row, 1)
                row += 1

        layout.addLayout(metrics_grid)

        # Add message if available
        if 'message' in self.signal_data and self.signal_data['message']:
            message = self.signal_data.get('message', '')
            message_label = QtWidgets.QLabel(message)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("padding: 8px; background-color: #2a3a4a; border-radius: 4px;")
            layout.addWidget(message_label)

        # Add close button
        close_button = QtWidgets.QPushButton("Close")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0098ff;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        close_button.clicked.connect(self.accept)

        button_layout = QtWidgets.QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)


class EnhancedScatterPlotItem(pg.ScatterPlotItem):
    """
    Enhanced scatter plot item with static glow effect and click handling.
    """
    def __init__(self, **kwargs):
        """
        Initialize the enhanced scatter plot item.

        Args:
            **kwargs: Arguments to pass to ScatterPlotItem
        """
        super().__init__(**kwargs)

        # Store signal data for each point
        self.signal_data_map = {}

        # Enable hover events
        self.setAcceptHoverEvents(True)

        # Connect clicked signal
        self.sigClicked.connect(self.on_click)

    def add_signal_point(self, pos, signal_data, **kwargs):
        """
        Add a signal point with associated data.

        Args:
            pos: Position tuple (x, y)
            signal_data: Dictionary containing signal information
            **kwargs: Additional arguments for the point
        """
        # Add the point
        spots = [{'pos': pos, **kwargs}]
        self.addPoints(spots)

        # Store the signal data
        point_index = len(self.data) - 1
        self.signal_data_map[point_index] = signal_data



    def on_click(self, plot, points):
        """
        Handle click events on signal points.

        Args:
            plot: The plot item
            points: List of clicked points
        """
        if not points:
            return

        # Get the first clicked point
        point = points[0]
        point_index = self.data.tolist().index(point.data())

        # Check if we have signal data for this point
        if point_index in self.signal_data_map:
            signal_data = self.signal_data_map[point_index]

            # Create and show the popup
            popup = SignalPopup(signal_data, parent=plot.getViewWidget())
            popup.exec()


def create_signal_markers(plot_widget, signal_data_list, x_values, y_values):
    """
    Create enhanced signal markers on a plot widget.

    Args:
        plot_widget: The pyqtgraph PlotWidget to add markers to
        signal_data_list: List of signal data dictionaries
        x_values: List of x-coordinates for the signals
        y_values: List of y-coordinates for the signals

    Returns:
        Dictionary of signal markers by type
    """
    # Define colors and symbols for different signal types
    signal_styles = {
        'BullishReversal': {
            'color': '#4CAF50',  # Green
            'symbol': 'o',
            'size': 15,
            'glow_color': '#4CAF50',
            'glow_size': 28,     # More moderate glow
            'glow_alpha': 0.4    # Less opaque
        },
        'BearishReversal': {
            'color': '#F44336',  # Red
            'symbol': 'o',
            'size': 15,
            'glow_color': '#F44336',
            'glow_size': 28,
            'glow_alpha': 0.4
        },
        'BullishPullback': {
            'color': '#00BCD4',  # Cyan
            'symbol': 't',  # Triangle up
            'size': 15,
            'glow_color': '#00BCD4',
            'glow_size': 28,
            'glow_alpha': 0.4
        },
        'BearishPullback': {
            'color': '#FF5722',  # Deep Orange
            'symbol': 't1',  # Triangle down
            'size': 15,
            'glow_color': '#FF5722',
            'glow_size': 28,
            'glow_alpha': 0.4
        }
    }

    # Create a marker for each signal type
    signal_markers = {}

    # Group signals by type
    signal_groups = {}
    for i, signal in enumerate(signal_data_list):
        signal_type = signal.get('type', 'Unknown')
        if signal_type not in signal_groups:
            signal_groups[signal_type] = []

        # Add index to the group
        signal_groups[signal_type].append(i)

    # Create a scatter plot for each signal type
    for signal_type, indices in signal_groups.items():
        # Get style for this signal type
        style = signal_styles.get(signal_type, {
            'color': '#9E9E9E',  # Grey default
            'symbol': 'o',
            'size': 10,
            'glow_color': '#9E9E9E',
            'glow_size': 20,
            'glow_alpha': 0.4
        })

        # Create glow effect first (larger, semi-transparent markers)
        # Add two layers of glow for a more subtle effect
        # Outer glow (larger, more transparent)
        outer_glow = pg.ScatterPlotItem(
            pen=None,  # No outline for glow
            brush=pg.mkBrush(color=style['glow_color'] + '30'),  # 20% opacity
            symbol=style['symbol'],
            size=style['glow_size'] * 1.3,  # 30% larger than inner glow
            name=None  # No name for glow layer
        )

        # Inner glow (smaller, less transparent)
        glow_scatter = pg.ScatterPlotItem(
            pen=None,  # No outline for glow
            brush=pg.mkBrush(color=style['glow_color'] + '60'),  # 40% opacity
            symbol=style['symbol'],
            size=style['glow_size'],
            name=None  # No name for glow layer
        )

        # Add glow points
        glow_points = []
        for idx in indices:
            glow_points.append({
                'pos': (x_values[idx], y_values[idx])
            })

        if glow_points:
            # Add outer glow layer
            outer_glow.addPoints(glow_points)
            plot_widget.addItem(outer_glow)

            # Add inner glow layer
            glow_scatter.addPoints(glow_points)
            plot_widget.addItem(glow_scatter)

        # Create enhanced scatter plot for main markers
        scatter = EnhancedScatterPlotItem(
            pen=pg.mkPen(color=style['color'], width=2),
            brush=pg.mkBrush(color=style['color']),
            symbol=style['symbol'],
            size=style['size'],
            name=signal_type
        )

        # Add each signal point with its data
        for idx in indices:
            scatter.add_signal_point(
                pos=(x_values[idx], y_values[idx]),
                signal_data=signal_data_list[idx]
            )

        # Add to plot
        plot_widget.addItem(scatter)

        # Store in dictionary
        signal_markers[signal_type] = scatter

    return signal_markers
