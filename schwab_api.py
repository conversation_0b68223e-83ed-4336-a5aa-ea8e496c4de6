"""
Schwab API Integration Module

This module provides a wrapper around the schwab-py library for integrating
Charles Schwab API data into the trading application.
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from PyQt6 import QtCore
import schwab
from schwab import auth, client
import asyncio
import threading
import time

logger = logging.getLogger(__name__)

# WARNING: Historical data loading from Schwab API has been DISABLED
logger.warning("SCHWAB API HISTORICAL DATA LOADING IS DISABLED - All historical data requests will be blocked")

class SchwabAPIError(Exception):
    """Custom exception for Schwab API errors"""
    pass

class SchwabAPI(QtCore.QObject):
    """
    Schwab API wrapper class that provides data fetching capabilities
    and integrates with the existing application architecture.
    """

    # Signals for data updates
    data_ready = QtCore.pyqtSignal(str, object)  # symbol, data
    error_occurred = QtCore.pyqtSignal(str)  # error message
    connection_status_changed = QtCore.pyqtSignal(bool)  # connected status

    def __init__(self, token_path: str = "schwab_token.json"):
        super().__init__()
        self.client = None
        self.token_path = token_path
        self.is_authenticated = False
        self.api_key = None
        self.app_secret = None
        self.callback_url = "https://127.0.0.1"  # Default to match common Schwab setup

        # Cache for recent data to avoid excessive API calls
        self.data_cache = {}
        self.cache_timeout = 60  # seconds

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # minimum seconds between requests

    def initialize_api(self, api_key: str, app_secret: str, callback_url: str = None) -> bool:
        """
        Initialize the Schwab API with credentials.

        Args:
            api_key: Schwab API key
            app_secret: Schwab app secret
            callback_url: OAuth callback URL (optional)

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            self.api_key = api_key
            self.app_secret = app_secret
            if callback_url:
                self.callback_url = callback_url

            # Try to create client from existing token first
            if os.path.exists(self.token_path):
                try:
                    self.client = auth.client_from_token_file(
                        token_path=self.token_path,
                        api_key=self.api_key,
                        app_secret=self.app_secret
                    )
                    self.is_authenticated = True
                    self.connection_status_changed.emit(True)
                    logger.info("Successfully authenticated with existing token")
                    return True
                except Exception as e:
                    logger.warning(f"Failed to use existing token: {e}")
                    # Remove invalid token file
                    try:
                        os.remove(self.token_path)
                    except:
                        pass

            # If no valid token exists, we'll need to authenticate
            logger.info("No valid token found. Authentication required.")
            return False

        except Exception as e:
            logger.error(f"Failed to initialize Schwab API: {e}")
            self.error_occurred.emit(f"Failed to initialize Schwab API: {str(e)}")
            return False

    def skip_oauth_login(self) -> bool:
        """
        Login without OAuth flow - just store credentials for later use.
        This is useful when OAuth is not required for certain operations.

        Returns:
            bool: True if credentials are stored successfully, False otherwise
        """
        try:
            if not self.api_key or not self.app_secret:
                raise SchwabAPIError("API key and app secret must be set before login")

            logger.info("Logging in without OAuth flow...")
            logger.info(f"API Key: {self.api_key[:8]}...")
            logger.info(f"Callback URL: {self.callback_url}")

            # For skip OAuth, we just mark as authenticated without creating a client
            # The client will be created when actually needed for API calls
            self.is_authenticated = True
            self.connection_status_changed.emit(True)
            logger.info("Successfully logged in without OAuth (credentials stored)")
            return True

        except Exception as e:
            logger.error(f"Skip OAuth login failed: {e}")
            self.error_occurred.emit(f"Login failed: {str(e)}")
            self.is_authenticated = False
            self.connection_status_changed.emit(False)
            return False

    def authenticate(self) -> bool:
        """
        Perform OAuth authentication flow.

        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            if not self.api_key or not self.app_secret:
                raise SchwabAPIError("API key and app secret must be set before authentication")

            logger.info("Starting OAuth authentication flow...")
            logger.info(f"API Key: {self.api_key[:8]}...")
            logger.info(f"Callback URL: {self.callback_url}")
            logger.info("A web browser will open for authentication. Please complete the login process.")

            # Remove any existing token file to force fresh authentication
            if os.path.exists(self.token_path):
                try:
                    os.remove(self.token_path)
                    logger.info("Removed existing token file for fresh authentication")
                except Exception as e:
                    logger.warning(f"Could not remove existing token file: {e}")

            # Try automatic flow first, fall back to manual if needed
            try:
                logger.info("Attempting automatic OAuth flow...")
                self.client = auth.client_from_login_flow(
                    api_key=self.api_key,
                    app_secret=self.app_secret,
                    callback_url=self.callback_url,
                    token_path=self.token_path,
                    interactive=True,
                    callback_timeout=60.0  # Shorter timeout for automatic flow
                )
            except Exception as auto_error:
                logger.warning(f"Automatic flow failed: {auto_error}")
                logger.info("Falling back to manual OAuth flow...")

                # Use manual flow as fallback
                self.client = auth.client_from_manual_flow(
                    api_key=self.api_key,
                    app_secret=self.app_secret,
                    callback_url=self.callback_url,
                    token_path=self.token_path
                )

            self.is_authenticated = True
            self.connection_status_changed.emit(True)
            logger.info("Successfully authenticated with Schwab API")
            return True

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Authentication failed: {e}")

            # Provide specific error messages for common issues
            if "invalid_client" in error_msg.lower():
                detailed_error = (
                    "Authentication failed: invalid_client\n\n"
                    "This usually means:\n"
                    "1. Your Schwab app is not approved yet (must be 'Ready for Use' status)\n"
                    "2. Your API key or app secret is incorrect\n"
                    "3. Your callback URL doesn't match exactly (check case and trailing slashes)\n\n"
                    "Please verify:\n"
                    "• App status is 'Ready for Use' in Schwab Developer Portal\n"
                    "• API key and app secret are correct\n"
                    f"• Callback URL is exactly: {self.callback_url}\n\n"
                    "Common callback URLs:\n"
                    "• https://127.0.0.1 (most common)\n"
                    "• https://127.0.0.1:8182\n"
                    "• https://localhost\n\n"
                    "Visit: https://developer.schwab.com/ to check your app status"
                )
            elif "unauthorized" in error_msg.lower():
                detailed_error = (
                    "Authentication failed: Unauthorized\n\n"
                    "Your Schwab application may not be approved yet.\n"
                    "Check your app status in the Schwab Developer Portal.\n"
                    "It must show 'Ready for Use' to work with the API.\n\n"
                    "If your app is approved, check:\n"
                    "• API key and app secret are correct\n"
                    "• Callback URL matches exactly\n\n"
                    "Visit: https://developer.schwab.com/ to check your app"
                )
            elif "connection" in error_msg.lower() or "network" in error_msg.lower():
                detailed_error = (
                    "Authentication failed: Connection Error\n\n"
                    "Check your internet connection and try again.\n"
                    "If the problem persists, Schwab's servers may be down."
                )
            else:
                detailed_error = f"Authentication failed: {error_msg}\n\nCheck the console for more details."

            self.error_occurred.emit(detailed_error)
            self.is_authenticated = False
            self.connection_status_changed.emit(False)
            return False

    def _rate_limit_check(self):
        """Ensure we don't exceed rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()

    def _check_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Check if we have recent cached data"""
        if cache_key in self.data_cache:
            data, timestamp = self.data_cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return data
            else:
                # Remove expired cache entry
                del self.data_cache[cache_key]
        return None

    def _cache_data(self, cache_key: str, data: pd.DataFrame):
        """Cache data with timestamp"""
        self.data_cache[cache_key] = (data.copy(), time.time())

    def get_price_history(self, symbol: str, timeframe: str = "1m", days: int = 1) -> Optional[pd.DataFrame]:
        """
        Get historical price data for a symbol.

        DISABLED: This method has been disabled to prevent any historical data loading from Schwab API.

        Args:
            symbol: Stock symbol
            timeframe: Data timeframe (1m, 5m, 15m, 30m, 1h, 1d)
            days: Number of days of data

        Returns:
            None - Historical data loading is disabled
        """
        logger.warning(f"Historical data loading from Schwab API is DISABLED. Request for {symbol} ({timeframe}, {days} days) was blocked.")
        self.error_occurred.emit("Historical data loading from Schwab API is disabled")
        return None

    def get_live_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get live/real-time quote for a symbol.
        This is specifically for live data, not historical.

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary with live quote data or None if error
        """
        return self.get_quote(symbol)

    def get_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get current quote for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary with quote data or None if error
        """
        if not self.is_authenticated or not self.client:
            self.error_occurred.emit("Not authenticated with Schwab API")
            return None

        try:
            self._rate_limit_check()

            response = self.client.get_quotes([symbol.upper()])

            if response.status_code != 200:
                raise SchwabAPIError(f"Quote request failed with status {response.status_code}")

            data = response.json()

            if symbol.upper() in data:
                return data[symbol.upper()]
            else:
                logger.warning(f"No quote data returned for {symbol}")
                return None

        except Exception as e:
            logger.error(f"Failed to get quote for {symbol}: {e}")
            self.error_occurred.emit(f"Failed to get quote: {str(e)}")
            return None

    def get_option_chain(self, symbol: str, strike_count: int = 10) -> Optional[Dict[str, Any]]:
        """
        Get option chain for a symbol.

        Args:
            symbol: Stock symbol
            strike_count: Number of strikes to include

        Returns:
            Dictionary with option chain data or None if error
        """
        if not self.is_authenticated or not self.client:
            self.error_occurred.emit("Not authenticated with Schwab API")
            return None

        try:
            self._rate_limit_check()

            response = self.client.get_option_chain(
                symbol=symbol.upper(),
                strike_count=strike_count
            )

            if response.status_code != 200:
                raise SchwabAPIError(f"Option chain request failed with status {response.status_code}")

            return response.json()

        except Exception as e:
            logger.error(f"Failed to get option chain for {symbol}: {e}")
            self.error_occurred.emit(f"Failed to get option chain: {str(e)}")
            return None

    def is_connected(self) -> bool:
        """Check if API is connected and authenticated"""
        if not self.is_authenticated or self.client is None:
            return False

        # Try a simple test to verify the connection actually works
        try:
            # Test with a simple quote request (doesn't require special permissions)
            test_response = self.client.get_quotes(["SPY"])
            return test_response.status_code == 200
        except Exception as e:
            logger.warning(f"Connection test failed: {e}")
            return False

    def disconnect(self):
        """Disconnect from the API"""
        self.client = None
        self.is_authenticated = False
        self.connection_status_changed.emit(False)
        logger.info("Disconnected from Schwab API")

    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """
        Get account information.

        Returns:
            Dictionary with account data or None if error
        """
        if not self.is_authenticated or not self.client:
            self.error_occurred.emit("Not authenticated with Schwab API")
            return None

        try:
            self._rate_limit_check()

            response = self.client.get_account_numbers()

            if response.status_code == 401:
                logger.warning("Account info request returned 401 - insufficient permissions or token expired")
                self.error_occurred.emit("Account access not authorized - this may be normal for market data only access")
                return None
            elif response.status_code != 200:
                raise SchwabAPIError(f"Account info request failed with status {response.status_code}")

            return response.json()

        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            # Don't emit error for 401s as they might be expected
            if "401" not in str(e):
                self.error_occurred.emit(f"Failed to get account info: {str(e)}")
            return None

    def test_connection(self) -> bool:
        """
        Test the API connection using a simple market data request.
        This is better for testing than account info which requires special permissions.

        Returns:
            bool: True if connection is working, False otherwise
        """
        if not self.is_authenticated or not self.client:
            return False

        try:
            self._rate_limit_check()

            # Use a simple quote request to test the connection
            # This should work with basic market data permissions
            response = self.client.get_quotes(["SPY"])

            if response.status_code == 200:
                logger.info("Connection test successful using market data request")
                return True
            elif response.status_code == 401:
                logger.warning("Connection test failed - authentication issue")
                self.error_occurred.emit("Authentication failed - check your credentials and permissions")
                return False
            else:
                logger.warning(f"Connection test failed with status {response.status_code}")
                self.error_occurred.emit(f"Connection test failed with status {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            self.error_occurred.emit(f"Connection test failed: {str(e)}")
            return False

# Global instance for easy access
schwab_api = SchwabAPI()

def initialize_api(api_key: str, app_secret: str, callback_url: str = None) -> bool:
    """
    Initialize the global Schwab API instance.

    Args:
        api_key: Schwab API key
        app_secret: Schwab app secret
        callback_url: OAuth callback URL (optional)

    Returns:
        bool: True if initialization successful, False otherwise
    """
    return schwab_api.initialize_api(api_key, app_secret, callback_url)
