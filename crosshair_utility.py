"""
Crosshair Utility Module

This module provides utility functions for adding consistent crosshairs to plot widgets
across the application.
"""

from PyQt6 import QtCore
import pyqtgraph as pg

def add_crosshair(plot_widget, hide_cursor=True, include_dot=True, get_extrema_func=None):
    """
    Add a white dashed crosshair to a plot widget.

    Args:
        plot_widget: The pyqtgraph PlotWidget to add the crosshair to
        hide_cursor: Whether to hide the mouse cursor when hovering over the plot
        include_dot: Whether to include a dot at the intersection of the crosshair lines
        get_extrema_func: Function that returns the current extrema value for percentage calculation

    Returns:
        A dictionary containing the crosshair components:
        - crosshair_v: Vertical crosshair line
        - crosshair_h: Horizontal crosshair line
        - crosshair_text: Text item for displaying coordinates
        - crosshair_dot: Scatter plot item for the dot at the intersection (if include_dot=True)
        - proxy: Signal proxy for mouse movement
    """
    # Create white dashed pen for crosshair with width 2 as per requirements
    white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)

    # Create crosshair lines
    crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
    crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)

    # Add crosshair lines to plot
    plot_widget.addItem(crosshair_v, ignoreBounds=True)
    plot_widget.addItem(crosshair_h, ignoreBounds=True)

    # Create a scatter plot item for the dot at the intersection if requested
    crosshair_dot = None
    if include_dot:
        # White dot with black border
        crosshair_dot = pg.ScatterPlotItem(
            size=8,  # Size of the dot
            pen=pg.mkPen('black', width=1.5),  # Black border
            brush=pg.mkBrush('white'),  # White fill
            symbol='o'  # Circle symbol
        )
        plot_widget.addItem(crosshair_dot, ignoreBounds=True)

    # Add text item for displaying coordinates
    crosshair_text = pg.TextItem(
        text="",
        color='#FFFFFF',  # White text
        fill=pg.mkBrush('#1e2a3a' + 'CC'),  # Semi-transparent background
        anchor=(0, 0),
        border=pg.mkPen(color='#FFFFFF55', width=1)  # Light border
    )
    plot_widget.addItem(crosshair_text, ignoreBounds=True)

    # Create update function for this specific plot
    def update_crosshair(event):
        pos = event[0]
        if plot_widget.sceneBoundingRect().contains(pos):
            # Convert mouse position to plot coordinates
            mouse_point = plot_widget.getPlotItem().vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Make crosshairs visible
            crosshair_v.setVisible(True)
            crosshair_h.setVisible(True)

            # Now make text visible to show percentage from extrema
            crosshair_text.setVisible(True)

            # Update crosshair positions
            crosshair_v.setPos(x)
            crosshair_h.setPos(y)

            # Update dot position if it exists
            if crosshair_dot:
                crosshair_dot.setData([x], [y])

            # Calculate percentage from extrema and converted price if function is provided
            extrema_text = ""
            if get_extrema_func:
                extrema_value = get_extrema_func()
                if extrema_value is not None and extrema_value != 0:
                    # Calculate converted price from percentage
                    converted_price = extrema_value * (1 + y / 100)
                    # Format the text with percentage and price
                    extrema_text = f"\n{y:.2f}% (${converted_price:.2f})"

            # Update text with coordinates and extrema information
            crosshair_text.setText(f"X: {x:.2f}\nY: {y:.2f}{extrema_text}")
            crosshair_text.setPos(x, y)

            # IMPORTANT: Hide the mouse cursor when hovering over the chart
            if hide_cursor:
                # Only set cursor to blank at the widget level
                # This is safer than using application-level cursor hiding
                plot_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
        else:
            # Hide crosshairs when mouse is outside the plot
            crosshair_v.setVisible(False)
            crosshair_h.setVisible(False)
            crosshair_text.setVisible(False)
            if crosshair_dot:
                crosshair_dot.setData([], [])  # Hide dot by setting empty data

            # Restore the default cursor when outside the chart
            plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

    # Set up signal proxy for mouse movement
    proxy = pg.SignalProxy(
        plot_widget.scene().sigMouseMoved,
        rateLimit=60,
        slot=update_crosshair
    )

    result = {
        'crosshair_v': crosshair_v,
        'crosshair_h': crosshair_h,
        'crosshair_text': crosshair_text,
        'proxy': proxy
    }

    if crosshair_dot:
        result['crosshair_dot'] = crosshair_dot

    return result

def add_price_only_crosshair(plot_widget, hide_cursor=True, parent=None):
    """
    Add a crosshair to a plot widget that only shows the price (y-axis) value.

    This function creates both horizontal and vertical crosshair lines
    but only displays the price (y-axis) value in the text label.

    Args:
        plot_widget: The pyqtgraph PlotWidget to add the crosshair to
        hide_cursor: Whether to hide the mouse cursor when hovering over the plot
        parent: Parent widget that may have a update_crosshair_price method

    Returns:
        A dictionary containing the crosshair components
    """
    # Create white dashed pen for crosshair with width 2 as per requirements
    white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)

    # Create both horizontal and vertical crosshair lines
    crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
    crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)

    # Create a scatter plot item for the dot at the intersection
    # White dot with black border
    crosshair_dot = pg.ScatterPlotItem(
        size=8,  # Size of the dot
        pen=pg.mkPen('black', width=1.5),  # Black border
        brush=pg.mkBrush('white'),  # White fill
        symbol='o'  # Circle symbol
    )

    # Add crosshair lines and dot to plot
    plot_widget.addItem(crosshair_h, ignoreBounds=True)
    plot_widget.addItem(crosshair_v, ignoreBounds=True)
    plot_widget.addItem(crosshair_dot, ignoreBounds=True)

    # Add text item for displaying price only
    crosshair_text = pg.TextItem(
        text="",
        color='#FFFFFF',  # White text
        fill=pg.mkBrush('#1e2a3a' + 'CC'),  # Semi-transparent background
        anchor=(0, 0.5),  # Left-center aligned
        border=pg.mkPen(color='#FFFFFF55', width=1)  # Light border
    )
    plot_widget.addItem(crosshair_text, ignoreBounds=True)

    # Create update function for this specific plot
    def update_crosshair(event):
        pos = event[0]
        if plot_widget.sceneBoundingRect().contains(pos):
            # Convert mouse position to plot coordinates
            mouse_point = plot_widget.getPlotItem().vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Make crosshair visible but keep text invisible
            crosshair_h.setVisible(True)
            crosshair_v.setVisible(True)
            crosshair_text.setVisible(False)  # Keep tooltip text invisible

            # Update crosshair positions
            crosshair_h.setPos(y)
            crosshair_v.setPos(x)

            # Update dot position at the intersection
            crosshair_dot.setData([x], [y])

            # Store the last known y-position for use when the mouse leaves the plot
            plot_widget.last_y_pos = y

            # We'll use the parent's update_crosshair_price method instead of showing our own text
            # This prevents duplicate price labels
            crosshair_text.setVisible(False)

            # Update parent's crosshair price label if available
            if parent and hasattr(parent, 'update_crosshair_price'):
                parent.update_crosshair_price(y, pos=(x, y))

            # IMPORTANT: Hide the mouse cursor when hovering over the chart
            if hide_cursor:
                # Only set cursor to blank at the widget level
                plot_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
        else:
            # Hide crosshair when mouse is outside the plot
            crosshair_h.setVisible(False)
            crosshair_v.setVisible(False)

            # We're not using the crosshair_text anymore, so keep it hidden
            crosshair_text.setVisible(False)

            crosshair_dot.setData([], [])  # Hide dot by setting empty data

            # Update parent's crosshair price label to show no price
            if parent and hasattr(parent, 'update_crosshair_price'):
                parent.update_crosshair_price(None, pos=None)

            # Restore the default cursor when outside the chart
            plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

    # Set up signal proxy for mouse movement
    proxy = pg.SignalProxy(
        plot_widget.scene().sigMouseMoved,
        rateLimit=60,
        slot=update_crosshair
    )

    return {
        'crosshair_v': crosshair_v,
        'crosshair_h': crosshair_h,
        'crosshair_text': crosshair_text,
        'crosshair_dot': crosshair_dot,
        'proxy': proxy
    }

def add_price_crosshair(plot_widget, get_price_func=None, hide_cursor=True):
    """
    Add a white dashed crosshair to a plot widget with price display.

    Args:
        plot_widget: The pyqtgraph PlotWidget to add the crosshair to
        get_price_func: Function that takes y-coordinate and returns price
        hide_cursor: Whether to hide the mouse cursor when hovering over the plot

    Returns:
        A dictionary containing the crosshair components
    """
    # Create white dashed pen for crosshair with width 2 as per requirements
    white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)

    # Create crosshair lines
    crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
    crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)

    # Create a scatter plot item for the dot at the intersection
    # White dot with black border
    crosshair_dot = pg.ScatterPlotItem(
        size=8,  # Size of the dot
        pen=pg.mkPen('black', width=1.5),  # Black border
        brush=pg.mkBrush('white'),  # White fill
        symbol='o'  # Circle symbol
    )

    # Add crosshair lines and dot to plot
    plot_widget.addItem(crosshair_v, ignoreBounds=True)
    plot_widget.addItem(crosshair_h, ignoreBounds=True)
    plot_widget.addItem(crosshair_dot, ignoreBounds=True)

    # Add text item for displaying coordinates
    crosshair_text = pg.TextItem(
        text="",
        color='#FFFFFF',  # White text
        fill=pg.mkBrush('#1e2a3a' + 'CC'),  # Semi-transparent background
        anchor=(0, 0),
        border=pg.mkPen(color='#FFFFFF55', width=1)  # Light border
    )
    plot_widget.addItem(crosshair_text, ignoreBounds=True)

    # Create update function for this specific plot
    def update_crosshair(event):
        pos = event[0]
        if plot_widget.sceneBoundingRect().contains(pos):
            # Convert mouse position to plot coordinates
            mouse_point = plot_widget.getPlotItem().vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Make crosshairs visible but keep text invisible
            crosshair_v.setVisible(True)
            crosshair_h.setVisible(True)
            crosshair_text.setVisible(False)  # Keep tooltip text invisible

            # Update crosshair positions
            crosshair_v.setPos(x)
            crosshair_h.setPos(y)

            # Update dot position at the intersection
            crosshair_dot.setData([x], [y])

            # Get price if function is provided
            price_text = ""
            if get_price_func:
                price = get_price_func(y)
                price_text = f"\nPrice: ${price:.2f}"

            # Update text with coordinates
            crosshair_text.setText(f"X: {x:.2f}\nY: {y:.2f}{price_text}")
            crosshair_text.setPos(x, y)

            # IMPORTANT: Hide the mouse cursor when hovering over the chart
            if hide_cursor:
                # Only set cursor to blank at the widget level
                # This is safer than using application-level cursor hiding
                plot_widget.setCursor(QtCore.Qt.CursorShape.BlankCursor)
        else:
            # Hide crosshairs when mouse is outside the plot
            crosshair_v.setVisible(False)
            crosshair_h.setVisible(False)
            crosshair_text.setVisible(False)
            crosshair_dot.setData([], [])  # Hide dot by setting empty data

            # Restore the default cursor when outside the chart
            plot_widget.setCursor(QtCore.Qt.CursorShape.ArrowCursor)

    # Set up signal proxy for mouse movement
    proxy = pg.SignalProxy(
        plot_widget.scene().sigMouseMoved,
        rateLimit=60,
        slot=update_crosshair
    )

    return {
        'crosshair_v': crosshair_v,
        'crosshair_h': crosshair_h,
        'crosshair_text': crosshair_text,
        'crosshair_dot': crosshair_dot,
        'proxy': proxy
    }
