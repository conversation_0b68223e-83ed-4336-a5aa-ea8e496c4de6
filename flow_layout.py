"""
FlowLayout implementation for PyQt6

This module provides a FlowLayout class that arranges widgets in a flow layout,
similar to how text flows in a paragraph. Widgets are arranged from left to right
and wrap to the next line when there is not enough horizontal space.
"""

from PyQt6.QtCore import QPoint, QRect, QSize, Qt
from PyQt6.QtWidgets import QLayout, QLayoutItem, QSizePolicy


class FlowLayout(QLayout):
    """
    Flow layout arranges widgets in a flow, similar to how text flows in a paragraph.
    Widgets are arranged from left to right and wrap to the next line when there is
    not enough horizontal space.
    """

    def __init__(self, parent=None, margin=0, spacing=-1):
        """
        Initialize the FlowLayout.

        Args:
            parent: Parent widget
            margin: Margin around the layout
            spacing: Spacing between widgets
        """
        super().__init__(parent)
        self.setContentsMargins(margin, margin, margin, margin)
        self.setSpacing(spacing)
        self.items = []

    def __del__(self):
        """Delete all items in the layout."""
        item = self.takeAt(0)
        while item:
            item = self.takeAt(0)

    def addItem(self, item):
        """Add an item to the layout."""
        self.items.append(item)

    def count(self):
        """Return the number of items in the layout."""
        return len(self.items)

    def itemAt(self, index):
        """Return the item at the given index."""
        if 0 <= index < len(self.items):
            return self.items[index]
        return None

    def takeAt(self, index):
        """Remove and return the item at the given index."""
        if 0 <= index < len(self.items):
            return self.items.pop(index)
        return None

    def expandingDirections(self):
        """Return the expanding directions of the layout."""
        return Qt.Orientation(0)  # No expanding directions

    def hasHeightForWidth(self):
        """Return True if the layout's height depends on its width."""
        return True

    def heightForWidth(self, width):
        """Calculate the height needed for the given width."""
        return self._do_layout(QRect(0, 0, width, 0), True)

    def setGeometry(self, rect):
        """Set the geometry of the layout."""
        super().setGeometry(rect)
        self._do_layout(rect, False)

    def sizeHint(self):
        """Return the preferred size of the layout."""
        return self.minimumSize()

    def minimumSize(self):
        """Return the minimum size of the layout."""
        size = QSize()
        for item in self.items:
            size = size.expandedTo(item.minimumSize())
        margin = self.contentsMargins()
        size += QSize(margin.left() + margin.right(), margin.top() + margin.bottom())
        return size

    def _do_layout(self, rect, test_only=False):
        """
        Layout the items within the given rectangle.

        Args:
            rect: Rectangle to layout items in
            test_only: If True, only calculate the height, don't actually move widgets

        Returns:
            The height used by the layout
        """
        x = rect.x()
        y = rect.y()
        line_height = 0
        spacing = self.spacing()
        margin = self.contentsMargins()
        
        # Adjust for margins
        effective_rect = rect.adjusted(margin.left(), margin.top(), -margin.right(), -margin.bottom())
        x = effective_rect.x()
        y = effective_rect.y()
        right = effective_rect.right() + 1

        for item in self.items:
            widget = item.widget()
            if widget and not widget.isVisible():
                continue

            item_width = item.sizeHint().width()
            item_height = item.sizeHint().height()

            # If we would exceed the right edge, move to the next line
            if x + item_width > right and line_height > 0:
                x = effective_rect.x()
                y += line_height + spacing
                line_height = 0

            if not test_only:
                item.setGeometry(QRect(QPoint(x, y), item.sizeHint()))

            # Update position and line height
            x += item_width + spacing
            line_height = max(line_height, item_height)

        # Return the total height
        return y + line_height - rect.y() + margin.bottom()
