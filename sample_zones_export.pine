//@version=6
indicator('SPY Zones Export', overlay = true, max_boxes_count = 500)

// Generated on 2025-07-13 03:07
// Exported 3 zones from candlestick chart

// Variables to store boxes and labels
var box_array = array.new<box>()
var label_array = array.new<label>()

// Clear previous boxes and labels to prevent trail
if barstate.islast
    if array.size(box_array) > 0
        for i = 0 to array.size(box_array) - 1 by 1
            box.delete(array.get(box_array, i))
        array.clear(box_array)
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1 by 1
            label.delete(array.get(label_array, i))
        array.clear(label_array)

// Volatility Zone 1 (Volatility) from 400.0000 to 410.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 400.0000, bar_index + 100, 410.0000, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 405.0000, 
                          text = 'Volatility Zone 1', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// Volatility Zone 2 (Volatility) from 420.0000 to 430.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 420.0000, bar_index + 100, 430.0000, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 425.0000, 
                          text = 'Volatility Zone 2', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// AK Density Zone 1 (Density) from 435.0000 to 445.0000
if barstate.islast
    zone_box = box.new(bar_index - 100, 435.0000, bar_index + 100, 445.0000, 
                      border_color = color.blue, bgcolor = color.new(color.blue, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 440.0000, 
                          text = 'AK Density Zone 1', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.blue, 
                          size = size.small)
    array.push(label_array, zone_label)

// Summary: Generated 3 zones
// Zones included in this script:
// - Volatility Zone 1: 400.0000 to 410.0000
// - Volatility Zone 2: 420.0000 to 430.0000
// - AK Density Zone 1: 435.0000 to 445.0000
