"""
FWL Odds Tab for Market Odds and Options Analyzer

This module provides a UI tab for visualizing FWL (Forward-Looking) Odds.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging
from pyqtgraph.graphicsItems.ArrowItem import Arrow<PERSON><PERSON>
from crosshair_utility import add_price_only_crosshair
from scipy.interpolate import CubicSpline
import math

logger = logging.getLogger(__name__)

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
        'highlight': '#FFC107',            # Material Design Amber
        'selection': '#2979FF',            # Selection highlight color
        'button_radius': '4px',            # Button corner radius
        'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
    }

class FWLOddsTab(QtWidgets.QWidget):
    """
    Tab for visualizing Forward-Looking Odds.
    """
    def __init__(self, parent=None, data_tab=None):
        """
        Initialize the FWL odds tab.

        Args:
            parent: Parent widget
            data_tab: Reference to the Data tab
        """
        super().__init__(parent)
        self.parent = parent
        self.data_tab = data_tab
        self.chart_colors = {
            'background': THEME_COLORS['control_panel'],  # Use theme control panel color
            'bullish': THEME_COLORS['bullish'],           # Material Design Green
            'bearish': THEME_COLORS['bearish'],           # Material Design Red
            'text': THEME_COLORS['text'],                 # Light gray text
            'line': '#FFFFFF',                            # White lines
            'bars': '#3A539B',                            # Blue for bars
            'average': '#FFC107',                         # Amber for average line
            'high': '#FFFFFF',                            # White for high prices (was Cyan)
            'low': '#FFFFFF',                             # White for low prices (was Magenta)
            'long': '#00FF00',                            # Green for long theoretical prices
            'short': '#FF00FF'                            # Magenta for short theoretical prices
        }

        # Initialize theoretical prices as empty list for data subtab
        self.theoretical_prices = []

        # AK's daily vol zones state and data
        self.ak_daily_vol_zones_active = False
        self.ak_daily_vol_zones_data = {}
        self.ak_daily_vol_zones_cache = {}

        # Store last calculated intersections for AK's Odds Zones
        self.last_intersections = []

        # AK's odds zones state and data
        self.ak_odds_zones_active = False
        self.ak_odds_zones_data = {}
        self.ak_odds_zones_cache = {}

        # Initialize UI
        self.init_ui()

        # Connect to data tab's calculation mode change signal if available
        if self.data_tab is not None:
            # Check if the data tab has the necessary signals
            if hasattr(self.data_tab, 'percentage_based_btn'):
                self.data_tab.percentage_based_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.current_price_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.pivot_price_btn.toggled.connect(self.on_calculation_mode_changed)
                if hasattr(self.data_tab, 'cycle_pivot_btn'):
                    self.data_tab.cycle_pivot_btn.toggled.connect(self.on_calculation_mode_changed)
                logger.info("Connected to data tab calculation mode change signals")

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Status label
        self.status_label = QtWidgets.QLabel("FWL Odds - Ready")
        self.status_label.setStyleSheet(f"color: white;")
        main_layout.addWidget(self.status_label)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(self.chart_colors['background'])
        # Empty axis labels to remove confusion
        self.plot_widget.setLabel('left', '', color=self.chart_colors['text'])
        self.plot_widget.setLabel('bottom', '', color=self.chart_colors['text'])
        main_layout.addWidget(self.plot_widget)

        # Initialize crosshair (will be properly set up in generate_fwl_odds)
        self.crosshair = None

        # Store the initial view state for reset functionality
        self.initial_view_state = None

        # Enable mouse interaction for zooming
        self.plot_widget.setMouseEnabled(x=True, y=True)
        self.plot_widget.setMenuEnabled(False)  # Still disable context menu

        # Enable mouse wheel zooming but disable rectangular region selection
        view_box = self.plot_widget.getPlotItem().getViewBox()
        view_box.enableAutoRange(enable=False)  # Disable auto-range to maintain manual zoom control
        view_box.setMouseMode(pg.ViewBox.PanMode)  # Use pan mode instead of rect mode to disable left-click zoom

        # Add control buttons (zoom controls)
        controls_layout = QtWidgets.QHBoxLayout()

        # Zoom in button
        self.zoom_in_btn = QtWidgets.QPushButton("Zoom In")
        self.zoom_in_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_btn)

        # Zoom out button
        self.zoom_out_btn = QtWidgets.QPushButton("Zoom Out")
        self.zoom_out_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_btn)

        # Reset zoom button
        self.reset_zoom_btn = QtWidgets.QPushButton("Reset Zoom")
        self.reset_zoom_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)
        controls_layout.addWidget(self.reset_zoom_btn)

        # Add stretch to push buttons to the left
        controls_layout.addStretch()

        # Add the controls layout to the main layout
        main_layout.addLayout(controls_layout)

        # Set background color
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

    def get_data_from_data_tab(self):
        """
        Get data from the Data tab.

        Returns:
            DataFrame: The data from the Data tab, or None if not available
        """
        try:
            # Check if data_tab is available
            if self.data_tab is None:
                logger.warning("Data tab reference is None")
                self.status_label.setText("Error: Data tab not found")
                return None

            # Try to get the data tab reference if it's not directly available
            if hasattr(self, 'parent') and self.parent is not None:
                if hasattr(self.parent, 'get_data_tab'):
                    self.data_tab = self.parent.get_data_tab()
                    logger.info("Retrieved data tab from parent")

            # Check if the data tab has a table model with data
            if not hasattr(self.data_tab, 'table_model') or self.data_tab.table_model is None:
                logger.warning("No table model in Data tab")
                self.status_label.setText("Error: No table model in Data tab")
                return None

            # Get the data from the table model
            data = self.data_tab.table_model._data
            if data is None or data.empty:
                logger.warning("No data available in Data tab table model")
                self.status_label.setText("Error: No data available in Data tab")
                return None

            # Check if we're in current price mode and should use theoretical values
            is_current_price_mode = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_current_price_mode = self.data_tab.calculation_mode == "current_price"
                logger.info(f"Current calculation mode: {self.data_tab.calculation_mode}")

            # Determine which columns to check for
            high_column = 'Projected High' if is_current_price_mode and 'Projected High' in data.columns else 'High'
            low_column = 'Projected Low' if is_current_price_mode and 'Projected Low' in data.columns else 'Low'

            logger.info(f"Using {high_column}/{low_column} values for FWL odds")

            # Check if the data has the required columns
            required_columns = [high_column, low_column, 'Category']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.warning(f"Missing required columns in data: {missing_columns}")
                self.status_label.setText(f"Error: Missing columns in data: {', '.join(missing_columns)}")
                return None

            logger.info(f"Successfully retrieved data from data tab: {len(data)} rows")
            return data

        except Exception as e:
            logger.error(f"Error getting data from data tab: {str(e)}", exc_info=True)
            self.status_label.setText(f"Error: {str(e)}")
            return None

    def get_calculation_mode(self):
        """
        Get the calculation mode from the data tab.

        Returns:
            str: The calculation mode ('percentage_based', 'current_price', or 'pivot_price'), or None if not available
        """
        try:
            if self.data_tab is None:
                return None

            if hasattr(self.data_tab, 'calculation_mode'):
                mode = self.data_tab.calculation_mode
                logger.info(f"Retrieved calculation mode from data tab: {mode}")
                return mode

            return None

        except Exception as e:
            logger.error(f"Error getting calculation mode: {str(e)}", exc_info=True)
            return None

    def get_reference_price(self):
        """
        Get the appropriate reference price based on the calculation mode.

        Returns:
            tuple: (price, label, color) where:
                - price is the reference price value
                - label is the description of the price
                - color is the color to use for the infinity line
        """
        try:
            # Get the calculation mode
            calculation_mode = self.get_calculation_mode()

            # If percentage based, use 0 as the reference
            if calculation_mode == "percentage_based":
                logger.info("Using 0 as reference price for percentage based mode")
                return (0, "Percentage Base (0%)", "white")

            # Try to get the market odds tab from the parent
            market_odds_tab = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab
                logger.info("Retrieved market odds tab from data tab")

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        logger.info("Retrieved market odds tab from parent hierarchy")
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # If still not found, try to get it from the data tab's get_market_odds_tab method
            if market_odds_tab is None and self.data_tab is not None and hasattr(self.data_tab, 'get_market_odds_tab'):
                market_odds_tab = self.data_tab.get_market_odds_tab()
                logger.info("Retrieved market odds tab from data tab's get_market_odds_tab method")

            # If we found the market odds tab, get the appropriate price
            if market_odds_tab is not None:
                # For pivot price mode, use the pivot price
                if calculation_mode == "pivot_price":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Extrema Price: {pivot_price:.2f}", "green")

                # For cycle pivot mode, use the latest cycle's pivot price
                elif calculation_mode == "cycle_pivot":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using latest cycle pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Cycle Extrema: {pivot_price:.2f}", "green")

                # For current price mode or default, use the current price
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Check if we're viewing historical data and should use historical cutoff
                    if hasattr(market_odds_tab, 'historical_cutoff_index'):
                        historical_cutoff_index = market_odds_tab.historical_cutoff_index
                        current_price = market_odds_tab.data['Close'].iloc[historical_cutoff_index]
                        logger.info(f"Using historical reference price (index {historical_cutoff_index}): {current_price}")
                        return (current_price, f"Historical Price: {current_price:.2f}", "orange")
                    else:
                        current_price = market_odds_tab.data['Close'].iloc[-1]
                        logger.info(f"Using current price as reference: {current_price}")
                        return (current_price, f"Current Price: {current_price:.2f}", "yellow")

            # If we couldn't get a price, return None
            logger.warning("Could not get reference price")
            return None

        except Exception as e:
            logger.error(f"Error getting reference price: {str(e)}", exc_info=True)
            return None

    def extract_category_number(self, category):
        """
        Extract the numeric part from a category string (e.g., '3H' -> '3').

        Args:
            category: The category string

        Returns:
            str: The numeric part of the category, or None if not available
        """
        if category is None:
            return None

        # Extract numeric part using regex
        import re
        match = re.match(r'(\d+)[HL]', category)
        if match:
            return match.group(1)
        return None

    def get_latest_category(self, data):
        """
        Get the category of the latest row in the data.

        Args:
            data: DataFrame with the data

        Returns:
            str: The category of the latest row, or None if not available
        """
        if data is None or data.empty or 'Category' not in data.columns:
            return None

        # Get the last row's category
        latest_category = data['Category'].iloc[-1]
        if pd.isna(latest_category) or latest_category == "":
            # If the last row doesn't have a category, try to find the last non-empty category
            for i in range(len(data)-2, -1, -1):  # Start from second-to-last row and go backwards
                category = data['Category'].iloc[i]
                if not pd.isna(category) and category != "":
                    latest_category = category
                    logger.info(f"Using category from row {i} instead of last row: {latest_category}")
                    break
            else:
                return None  # No valid category found in any row

        logger.info(f"Latest category: {latest_category}")
        return latest_category

    def get_rows_with_same_weekday(self, data):
        """
        Get all rows with the same weekday as the latest row.

        Args:
            data: DataFrame with the data

        Returns:
            DataFrame: Subset of data with matching weekday, or empty DataFrame if no match
        """
        if data is None or data.empty:
            logger.warning("Invalid data for weekday matching")
            return pd.DataFrame()

        try:
            # Log all columns for debugging
            logger.info(f"Available columns in data: {data.columns.tolist()}")

            # Create a copy of the data to avoid modifying the original
            data_copy = data.copy()

            # Try to extract date information from the data
            # Method 1: Look for date-related columns
            date_column = None
            for col in data.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    logger.info(f"Found potential date column: {col}")
                    break

            # Method 2: Check if the index is a DatetimeIndex
            if date_column is None and isinstance(data.index, pd.DatetimeIndex):
                logger.info("Using DatetimeIndex for weekday matching")
                data_copy['_temp_date'] = data.index
                date_column = '_temp_date'

            # Method 3: Try to parse the first column as a date
            if date_column is None:
                logger.info(f"Trying to parse first column as date: {data.columns[0]}")
                try:
                    data_copy['_temp_date'] = pd.to_datetime(data.iloc[:, 0])
                    date_column = '_temp_date'
                    logger.info("Successfully parsed first column as date")
                except:
                    logger.warning("Could not parse first column as date")

            # Method 4: If all else fails, create a date column from the row index
            if date_column is None:
                logger.info("Creating date column from row index")
                # Get the current date
                import datetime
                today = datetime.datetime.now()

                # Create dates going back from today based on row index
                # This assumes the data is in chronological order with the most recent at the end
                dates = []
                for i in range(len(data)):
                    # Go back i days from today
                    date = today - datetime.timedelta(days=i)
                    dates.append(date)

                # Reverse the list so the oldest date is first
                dates.reverse()

                # Add the dates as a new column
                data_copy['_temp_date'] = dates
                date_column = '_temp_date'
                logger.info("Created date column from row index")

            # Convert date column to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(data_copy[date_column]):
                try:
                    data_copy[date_column] = pd.to_datetime(data_copy[date_column])
                    logger.info(f"Converted {date_column} to datetime")
                except Exception as e:
                    logger.error(f"Error converting date column to datetime: {str(e)}")
                    return pd.DataFrame()

            # Get the weekday of the latest row
            latest_date = data_copy[date_column].iloc[-1]
            latest_weekday = latest_date.weekday()  # 0=Monday, 1=Tuesday, ..., 6=Sunday
            weekday_name = latest_date.strftime('%A')  # Full weekday name

            logger.info(f"Latest date: {latest_date}, weekday: {weekday_name} ({latest_weekday})")

            # Filter rows with the same weekday
            matching_rows = data_copy[data_copy[date_column].dt.weekday == latest_weekday]

            if matching_rows.empty:
                logger.warning(f"No rows found with weekday '{weekday_name}'")
            else:
                logger.info(f"Found {len(matching_rows)} rows with weekday '{weekday_name}'")

            # Check if we need to limit the number of occurrences
            occurrence_count = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_count = self.parent.get_occurrence_count()

            if occurrence_count > 0 and len(matching_rows) > occurrence_count:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
                # Take only the specified number of occurrences
                matching_rows = matching_rows.head(occurrence_count)
                logger.info(f"Limited to {occurrence_count} most recent occurrences")

            # Drop the temporary date column if we added it
            if date_column == '_temp_date':
                matching_rows = matching_rows.drop('_temp_date', axis=1)

            return matching_rows

        except Exception as e:
            logger.error(f"Error in get_rows_with_same_weekday: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def get_rows_with_same_category(self, data, category):
        """
        Get all rows with the exact same category.

        Args:
            data: DataFrame with the data
            category: The category to match

        Returns:
            DataFrame: Subset of data with matching category
        """
        if data is None or data.empty or 'Category' not in data.columns or category is None:
            logger.warning("Invalid data or category for matching")
            return pd.DataFrame()

        # Log all available categories for debugging
        all_categories = data['Category'].unique()
        logger.info(f"All available categories in data: {all_categories}")

        # Match only the exact category (e.g., if category is '3H', only match '3H', not '3L')
        logger.info(f"Looking for rows with exact category '{category}'")

        # Filter rows with the exact category
        matching_rows = data[data['Category'] == category]

        if matching_rows.empty:
            logger.warning(f"No rows found with exact category '{category}'")
        else:
            logger.info(f"Found {len(matching_rows)} rows with exact category '{category}'")

        # Check if we need to limit the number of occurrences
        occurrence_count = 0
        if hasattr(self.parent, 'get_occurrence_count'):
            occurrence_count = self.parent.get_occurrence_count()

        if occurrence_count > 0 and len(matching_rows) > occurrence_count:
            # Try to find a date column for sorting
            date_column = None
            for col in matching_rows.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    break

            if date_column is not None:
                # Convert to datetime if needed
                if not pd.api.types.is_datetime64_any_dtype(matching_rows[date_column]):
                    try:
                        matching_rows[date_column] = pd.to_datetime(matching_rows[date_column])
                    except:
                        date_column = None

            if date_column is not None:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
            else:
                # If no date column, sort by index in descending order (assuming higher index = more recent)
                matching_rows = matching_rows.sort_index(ascending=False)

            # Take only the specified number of occurrences
            matching_rows = matching_rows.head(occurrence_count)
            logger.info(f"Limited to {occurrence_count} most recent occurrences")

        return matching_rows

    def find_line_intersections(self, x1, y1, x2, y2):
        """
        Find intersection points between two lines defined by sets of points.

        Args:
            x1, y1: Arrays of x,y coordinates for the first line (green line)
            x2, y2: Arrays of x,y coordinates for the second line (red line)

        Returns:
            List of tuples: [(x, y), ...] intersection points
        """
        intersections = []

        try:
            # Convert to numpy arrays if they aren't already
            x1, y1 = np.array(x1), np.array(y1)
            x2, y2 = np.array(x2), np.array(y2)

            # For each line segment in the first line
            for i in range(len(x1) - 1):
                # Get the current line segment from the first line
                x1_start, y1_start = x1[i], y1[i]
                x1_end, y1_end = x1[i + 1], y1[i + 1]

                # For each line segment in the second line
                for j in range(len(x2) - 1):
                    # Get the current line segment from the second line
                    x2_start, y2_start = x2[j], y2[j]
                    x2_end, y2_end = x2[j + 1], y2[j + 1]

                    # Find intersection between these two line segments
                    intersection = self.line_segment_intersection(
                        x1_start, y1_start, x1_end, y1_end,
                        x2_start, y2_start, x2_end, y2_end
                    )

                    if intersection is not None:
                        intersections.append(intersection)

        except Exception as e:
            logger.error(f"Error in find_line_intersections: {str(e)}", exc_info=True)

        return intersections

    def line_segment_intersection(self, x1, y1, x2, y2, x3, y3, x4, y4):
        """
        Find intersection point between two line segments.

        Args:
            x1, y1, x2, y2: First line segment from (x1,y1) to (x2,y2)
            x3, y3, x4, y4: Second line segment from (x3,y3) to (x4,y4)

        Returns:
            Tuple (x, y) if intersection exists within both segments, None otherwise
        """
        try:
            # Calculate the direction vectors
            denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

            # If denominator is 0, lines are parallel
            if abs(denom) < 1e-10:
                return None

            # Calculate intersection parameters
            t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
            u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

            # Check if intersection is within both line segments
            if 0 <= t <= 1 and 0 <= u <= 1:
                # Calculate intersection point
                intersection_x = x1 + t * (x2 - x1)
                intersection_y = y1 + t * (y2 - y1)
                return (intersection_x, intersection_y)

            return None

        except Exception as e:
            logger.error(f"Error in line_segment_intersection: {str(e)}", exc_info=True)
            return None

    def create_smooth_curve(self, x_coords, y_coords, num_points=100):
        """
        Create a smooth curve using cubic spline interpolation combined with sinusoidal waves.

        Args:
            x_coords: List of x coordinates
            y_coords: List of y coordinates
            num_points: Number of points to generate for the smooth curve

        Returns:
            Tuple of (smooth_x, smooth_y) arrays
        """
        try:
            if len(x_coords) < 2 or len(y_coords) < 2:
                return np.array(x_coords), np.array(y_coords)

            # Convert to numpy arrays and sort by x-coordinate
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)

            # Sort by x-coordinate to ensure proper interpolation
            sorted_indices = np.argsort(x_coords)
            x_sorted = x_coords[sorted_indices]
            y_sorted = y_coords[sorted_indices]

            # If we have only 2 points, create intermediate points for better curve
            if len(x_sorted) == 2:
                # Create 3 intermediate points between the two points
                x_mid = np.linspace(x_sorted[0], x_sorted[1], 5)
                # Add slight sinusoidal variation to y coordinates for smoothness
                y_range = abs(y_sorted[1] - y_sorted[0])
                y_mid = np.linspace(y_sorted[0], y_sorted[1], 5)

                # Add sinusoidal wave for smoothness (small amplitude)
                wave_amplitude = y_range * 0.05  # 5% of the y range
                for i in range(1, 4):  # Only modify intermediate points
                    wave_offset = wave_amplitude * math.sin(math.pi * i / 4)
                    y_mid[i] += wave_offset

                x_sorted = x_mid
                y_sorted = y_mid

            # Create cubic spline interpolation
            if len(x_sorted) >= 2:
                # Ensure we have enough points for cubic spline
                if len(x_sorted) < 4:
                    # For fewer than 4 points, use linear interpolation with sinusoidal smoothing
                    x_smooth = np.linspace(x_sorted[0], x_sorted[-1], num_points)
                    y_smooth = np.interp(x_smooth, x_sorted, y_sorted)

                    # Add sinusoidal wave for smoothness
                    y_range = np.max(y_sorted) - np.min(y_sorted)
                    wave_amplitude = y_range * 0.03  # 3% of the y range

                    for i in range(len(x_smooth)):
                        # Create wave that starts and ends at 0 to preserve endpoints
                        wave_phase = math.pi * i / (len(x_smooth) - 1)
                        wave_offset = wave_amplitude * math.sin(wave_phase) * math.sin(wave_phase * 2)
                        y_smooth[i] += wave_offset
                else:
                    # Use cubic spline for 4 or more points
                    cs = CubicSpline(x_sorted, y_sorted, bc_type='natural')
                    x_smooth = np.linspace(x_sorted[0], x_sorted[-1], num_points)
                    y_smooth = cs(x_smooth)

                    # Add subtle sinusoidal wave for extra smoothness
                    y_range = np.max(y_sorted) - np.min(y_sorted)
                    wave_amplitude = y_range * 0.02  # 2% of the y range

                    for i in range(len(x_smooth)):
                        # Create wave that starts and ends at 0 to preserve endpoints
                        wave_phase = math.pi * i / (len(x_smooth) - 1)
                        wave_offset = wave_amplitude * math.sin(wave_phase * 3) * math.sin(wave_phase)
                        y_smooth[i] += wave_offset

                return x_smooth, y_smooth
            else:
                return x_coords, y_coords

        except Exception as e:
            logger.error(f"Error creating smooth curve: {str(e)}", exc_info=True)
            # Return original coordinates if smoothing fails
            return np.array(x_coords), np.array(y_coords)

    def generate_fwl_odds(self):
        """Generate FWL odds graph showing high/low prices for rows with the same category."""
        try:
            # Clear the plot and reset crosshair
            self.plot_widget.clear()
            self.crosshair = None

            # Clear last intersections for fresh calculation
            self.last_intersections = []

            # Clear parent's statistics box if available
            if hasattr(self.parent, 'clear_statistics_box'):
                self.parent.clear_statistics_box()

            # Get data from the Data tab
            data = self.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available from data tab")
                return

            # Log data info for debugging
            logger.info(f"Data from data tab: {len(data)} rows, columns: {data.columns.tolist()}")

            # Get the matching mode from the parent (Volatility_Statistics_tab)
            matching_mode = 'hl'  # Default to H/L matching
            if hasattr(self.parent, 'get_matching_mode'):
                matching_mode = self.parent.get_matching_mode()
                logger.info(f"Using matching mode: {matching_mode}")

            # Get matching rows based on the selected mode
            if matching_mode == 'weekday':
                # Use weekday matching
                matching_rows = self.get_rows_with_same_weekday(data)
                if matching_rows.empty:
                    logger.warning("No rows found with the same weekday")
                    self.status_label.setText("FWL Odds - No rows found with the same weekday")
                    return

                # Get the weekday name for display
                # Find the date column or use the first column as a fallback
                date_column = None
                for col in data.columns:
                    if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                        date_column = col
                        break

                if date_column is None:
                    # Use the index if it's a DatetimeIndex
                    if isinstance(data.index, pd.DatetimeIndex):
                        latest_date = data.index[-1]
                    else:
                        # Use the first column as a last resort
                        try:
                            latest_date = pd.to_datetime(data.iloc[-1, 0])
                        except:
                            # If all else fails, use current date
                            import datetime
                            latest_date = datetime.datetime.now()
                else:
                    # Use the identified date column
                    try:
                        latest_date = pd.to_datetime(data[date_column].iloc[-1])
                    except:
                        # If conversion fails, use current date
                        import datetime
                        latest_date = datetime.datetime.now()

                weekday_name = latest_date.strftime('%A')
                latest_category = weekday_name  # Use weekday name as the category for display

                logger.info(f"Using weekday matching for {weekday_name}")
            else:
                # Use H/L category matching (default)
                # Get the latest category
                latest_category = self.get_latest_category(data)
                if latest_category is None:
                    logger.warning("No category found in latest row")
                    self.status_label.setText("FWL Odds - No category found in latest row")
                    return

                logger.info(f"Latest category: {latest_category}")

                # Get rows with the same category
                matching_rows = self.get_rows_with_same_category(data, latest_category)
                if matching_rows.empty:
                    logger.warning(f"No rows found with category '{latest_category}'")
                    self.status_label.setText(f"FWL Odds - No rows found with category '{latest_category}'")
                    return

            # Exclude the latest row to avoid using the latest high/low prices
            if len(matching_rows) > 1:
                # Find the index of the latest row in the matching_rows DataFrame
                latest_row_index = matching_rows.index.max()
                # Drop the latest row
                matching_rows = matching_rows.drop(latest_row_index)
                logger.info(f"Excluded latest row (index {latest_row_index}) from display")

            logger.info(f"Found {len(matching_rows)} rows with category '{latest_category}' (after excluding latest)")

            # Extract high and low prices
            try:
                # Check if we're in current price mode and should use theoretical values
                is_current_price_mode = False
                if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                    is_current_price_mode = self.data_tab.calculation_mode == "current_price"
                    logger.info(f"Current calculation mode: {self.data_tab.calculation_mode}")

                # Determine which columns to use for high and low values
                high_column = 'Projected High' if is_current_price_mode and 'Projected High' in matching_rows.columns else 'High'
                low_column = 'Projected Low' if is_current_price_mode and 'Projected Low' in matching_rows.columns else 'Low'

                logger.info(f"Using {high_column}/{low_column} values for FWL odds")

                high_prices = matching_rows[high_column].values
                low_prices = matching_rows[low_column].values

                # Convert to numeric if needed
                # Handle NumPy arrays correctly
                if isinstance(high_prices, np.ndarray):
                    # Convert to pandas Series temporarily for easier handling
                    high_prices = pd.Series(high_prices)
                    low_prices = pd.Series(low_prices)

                # Convert to numeric, handle NaN values, and convert back to numpy array
                high_prices = pd.to_numeric(high_prices, errors='coerce').fillna(0).to_numpy()
                low_prices = pd.to_numeric(low_prices, errors='coerce').fillna(0).to_numpy()

                logger.info(f"High prices: {high_prices}")
                logger.info(f"Low prices: {low_prices}")

                # Check if we have valid data
                if len(high_prices) == 0 or len(low_prices) == 0:
                    logger.warning("No valid high/low prices found")
                    self.status_label.setText(f"FWL Odds - No valid high/low prices found for category '{latest_category}'")
                    return

                # Fixed x positions for high and low columns
                high_x_pos = 3
                low_x_pos = 4

                # Calculate vertical spacing for stacked labels
                # Use a fixed spacing based on the range of prices
                price_range = max(np.max(high_prices) - np.min(high_prices),
                                 np.max(low_prices) - np.min(low_prices))
                if price_range == 0:  # Avoid division by zero
                    price_range = 1

                # Note: Vertical spacing calculation removed as it's no longer needed

                # Sort prices for better visualization
                sorted_high_indices = np.argsort(high_prices)
                sorted_low_indices = np.argsort(low_prices)

                # Get the categories for each row to display in the labels
                categories = matching_rows['Category'].values

                # Find the highest and lowest high prices
                highest_high_idx = sorted_high_indices[-1]  # Last index after sorting (highest value)
                lowest_high_idx = sorted_high_indices[0]    # First index after sorting (lowest value)
                highest_high = high_prices[highest_high_idx]
                lowest_high = high_prices[lowest_high_idx]

                # Find the highest and lowest low prices
                highest_low_idx = sorted_low_indices[-1]    # Last index after sorting (highest value)
                lowest_low_idx = sorted_low_indices[0]      # First index after sorting (lowest value)
                highest_low = low_prices[highest_low_idx]
                lowest_low = low_prices[lowest_low_idx]

                logger.info(f"Highest high: {highest_high}, Lowest high: {lowest_high}")
                logger.info(f"Highest low: {highest_low}, Lowest low: {lowest_low}")

                # Count the number of high and low prices
                num_high_prices = len(high_prices)
                num_low_prices = len(low_prices)

                logger.info(f"Number of high prices: {num_high_prices}")
                logger.info(f"Number of low prices: {num_low_prices}")

                # Add text labels for high prices (stacked at x=3)
                for i, idx in enumerate(sorted_high_indices):
                    high = high_prices[idx]
                    # Position labels with even spacing
                    y_pos = high

                    # Get the category for this price to display in the label
                    row_idx = sorted_high_indices[i]
                    if row_idx < len(categories):
                        category = categories[row_idx]
                        # Add category to the label
                        label_text = f"{high:.2f} ({category})"
                    else:
                        label_text = f"{high:.2f}"

                    # Calculate percentage for this high price (1% for highest, 50% for lowest)
                    # For high prices, we want the highest to be 1% and the lowest to be 50%
                    # The index i goes from 0 to num_high_prices-1
                    # We need to reverse it so that the highest price (last in sorted_high_indices) gets 1%
                    reversed_i = num_high_prices - 1 - i

                    # Calculate percentage based on position in the sorted list
                    if num_high_prices > 1:
                        # Evenly distribute percentages from 1% to 50%
                        percentage = 1 + (reversed_i * (50 - 1) / (num_high_prices - 1))
                    else:
                        # If there's only one price, use 1%
                        percentage = 1

                    # Format to 2 decimal places
                    percentage_str = f"{percentage:.2f}%"

                    # Add high price label without a background (at x=3)
                    high_label = pg.TextItem(
                        text=label_text,
                        color=self.chart_colors['high'],
                        anchor=(0, 0.5)  # Anchor to the left side of the text for right alignment
                    )
                    high_label.setPos(high_x_pos, y_pos)
                    self.plot_widget.addItem(high_label)

                    # Add percentage label separately (at x=3.5)
                    high_percentage_label = pg.TextItem(
                        text=percentage_str,
                        color=self.chart_colors['high'],
                        anchor=(0, 0.5)  # Anchor to the left side of the text for right alignment
                    )
                    high_percentage_label.setPos(3.5, y_pos)
                    self.plot_widget.addItem(high_percentage_label)

                # Add text labels for low prices (stacked at x=4)
                for i, idx in enumerate(sorted_low_indices):
                    low = low_prices[idx]
                    # Position labels with even spacing
                    y_pos = low

                    # Get the category for this price to display in the label
                    row_idx = sorted_low_indices[i]
                    if row_idx < len(categories):
                        category = categories[row_idx]
                        # Add category to the label
                        label_text = f"{low:.2f} ({category})"
                    else:
                        label_text = f"{low:.2f}"

                    # Calculate percentage for this low price (1% for lowest, 50% for highest)
                    # For low prices, we want the lowest to be 1% and the highest to be 50%
                    # The index i goes from 0 to num_low_prices-1
                    # The lowest price is first in sorted_low_indices, so we can use i directly

                    # Calculate percentage based on position in the sorted list
                    if num_low_prices > 1:
                        # Evenly distribute percentages from 1% to 50%
                        percentage = 1 + (i * (50 - 1) / (num_low_prices - 1))
                    else:
                        # If there's only one price, use 1%
                        percentage = 1

                    # Format to 2 decimal places
                    percentage_str = f"{percentage:.2f}%"

                    # Add low price label without a background (at x=4)
                    low_label = pg.TextItem(
                        text=label_text,
                        color=self.chart_colors['low'],
                        anchor=(0, 0.5)  # Anchor to the left side of the text for right alignment
                    )
                    low_label.setPos(low_x_pos, y_pos)
                    self.plot_widget.addItem(low_label)

                    # Add percentage label separately (at x=4.5)
                    low_percentage_label = pg.TextItem(
                        text=percentage_str,
                        color=self.chart_colors['low'],
                        anchor=(0, 0.5)  # Anchor to the left side of the text for right alignment
                    )
                    low_percentage_label.setPos(4.5, y_pos)
                    self.plot_widget.addItem(low_percentage_label)



            except Exception as e:
                logger.error(f"Error processing high/low prices: {str(e)}", exc_info=True)
                self.status_label.setText(f"Error processing high/low prices: {str(e)}")
                return

            # Get the reference price based on calculation mode
            reference_price_info = self.get_reference_price()

            # Add infinity line for reference price if available
            if reference_price_info is not None:
                reference_price, reference_label, _ = reference_price_info  # Ignore the color since we're using white

                # Create an infinity line for the reference price (white color)
                reference_price_line = pg.InfiniteLine(
                    pos=reference_price,
                    angle=0,  # Horizontal line
                    pen=pg.mkPen(color='white', width=2, style=QtCore.Qt.PenStyle.DashLine),
                    label=reference_label,
                    labelOpts={
                        'position': 0.1,  # Position the label at 10% from the left
                        'color': 'white',  # White text for label
                        'fill': (0, 0, 0, 50),
                        'movable': True
                    }
                )
                self.plot_widget.addItem(reference_price_line)
                logger.info(f"Added infinity line for {reference_label}")
            else:
                logger.warning("Could not add infinity line: reference price not available")

            # Set axis range as specified: x from 0 to 5.0 to include percentage labels at x=4.5
            self.plot_widget.setXRange(0, 5.0)  # Show x-axis from 0 to 5.0

            # Use the previously calculated highest high and lowest low values
            # These were calculated in lines 395-405
            min_price = lowest_low  # Use the lowest low price as the minimum
            max_price = highest_high  # Use the highest high price as the maximum

            # Get the calculation mode to adjust scaling appropriately
            calculation_mode = self.get_calculation_mode()
            logger.info(f"Scaling graph for calculation mode: {calculation_mode}")

            # Include reference price in the range if available
            if reference_price_info is not None:
                reference_price = reference_price_info[0]  # Extract the price value
                min_price = min(min_price, reference_price)
                max_price = max(max_price, reference_price)
                logger.info(f"Including reference price {reference_price} in y-axis range")

            # Ensure we have a reasonable price range for all calculation modes
            price_range = max_price - min_price
            if price_range < 0.001:  # If range is too small, expand it
                # Use a default range based on the calculation mode
                if calculation_mode == "percentage_based":
                    # For percentage based, use a reasonable percentage range
                    center = (max_price + min_price) / 2
                    min_price = center - 5  # -5%
                    max_price = center + 5  # +5%
                else:
                    # For price-based modes, use a percentage of the reference price
                    if reference_price_info is not None:
                        ref_price = reference_price_info[0]
                        min_price = ref_price * 0.95  # 5% below reference
                        max_price = ref_price * 1.05  # 5% above reference
                    else:
                        # Fallback if no reference price
                        center = (max_price + min_price) / 2
                        min_price = center * 0.95
                        max_price = center * 1.05

                logger.info(f"Expanded y-axis range to ensure visibility: {min_price} to {max_price}")

            # Store the original min/max for the rectangle
            rect_min = min_price
            rect_max = max_price

            # Calculate padding as 0.5% of min and max values
            padding_percentage = 0.005  # 0.5%
            padding_high = max_price * padding_percentage
            padding_low = min_price * padding_percentage

            # Extend the range by 0.5% in each direction
            min_price = min_price - padding_low
            max_price = max_price + padding_high

            logger.info(f"Extended y-axis range by 0.5% above and below: {min_price} to {max_price}")

            # Set y-axis range with fixed 0.5% padding
            y_min = min_price
            y_max = max_price
            self.plot_widget.setYRange(y_min, y_max)
            logger.info(f"Set y-axis range to {y_min} - {y_max} (0.5% padding)")

            # Store the initial view state for reset functionality
            self.initial_view_state = (0, 5.0, y_min, y_max)

            # Keep mouse interaction enabled for zooming
            self.plot_widget.setMouseEnabled(x=True, y=True)
            self.plot_widget.setMenuEnabled(False)  # Still disable context menu

            # Connect viewbox range changed signal to update crosshair when zooming
            self.plot_widget.getPlotItem().vb.sigRangeChanged.connect(self.on_view_range_changed)

            # Add price-only crosshair to the plot with mouse tracking enabled
            self.crosshair = add_price_only_crosshair(self.plot_widget, hide_cursor=True, parent=self.parent)

            # Add a white rectangle with transparent inside from x=1 to x=2.5
            # spanning from lowest low to highest high

            # Create a rectangle item with white border and transparent fill
            rect_width = 1.5  # From x=1 to x=2.5

            # Use the rect_min and rect_max values for the rectangle
            # This ensures the rectangle has the correct height without the +1/-1 padding
            rect_height = rect_max - rect_min

            # Rectangle should span from lowest low to highest high exactly
            rect_item = QtWidgets.QGraphicsRectItem(1, rect_min, rect_width, rect_height)

            # Set white border (pen) and transparent fill (brush)
            rect_item.setPen(pg.mkPen(color='#FFFFFF', width=1))  # White border
            rect_item.setBrush(pg.mkBrush(color=(0, 0, 0, 0)))    # Transparent fill

            # Add the rectangle to the plot
            self.plot_widget.addItem(rect_item)
            logger.info(f"Added rectangle from x=1 to x=2.5, y={rect_min} to y={rect_max}, height={rect_height}")

            # Add percentage labels inside the rectangle
            # For the vertical scale (price axis)

            # Get the reference price
            reference_price_info = self.get_reference_price()
            reference_price = 0  # Default for percentage based mode

            if reference_price_info is not None:
                reference_price = reference_price_info[0]

            logger.info(f"Reference price for percentage labels: {reference_price}")

            # Add percentage labels from current price to highest high
            # First, determine the range from current price to highest high
            price_to_highest = rect_max - reference_price

            if price_to_highest > 0:  # Only add if there's a positive range
                # Create 4 evenly spaced price levels between reference price and highest high
                for i in range(1, 5):
                    # Calculate position at this level
                    y_pos = reference_price + (price_to_highest * i / 5)

                    # Calculate how much % the price needs to move to reach this level
                    price_change_pct = ((y_pos - reference_price) / reference_price) * 100

                    # Create and add the label with price
                    pct_label = pg.TextItem(
                        text=f"{price_change_pct:.2f}% ({y_pos:.2f})",
                        color='white',
                        anchor=(0, 0.5)  # Left-aligned horizontally, centered vertically
                    )
                    pct_label.setPos(1.05, y_pos)  # Position near the left edge of the rectangle
                    self.plot_widget.addItem(pct_label)
                    logger.info(f"Added label at position {y_pos:.2f}, price change: {price_change_pct:.2f}%")

            # Add percentage labels from current price to lowest low
            # First, determine the range from current price to lowest low
            price_to_lowest = reference_price - rect_min

            if price_to_lowest > 0:  # Only add if there's a positive range
                # Create 4 evenly spaced price levels between reference price and lowest low
                for i in range(1, 5):
                    # Calculate position at this level
                    y_pos = reference_price - (price_to_lowest * i / 5)

                    # Calculate how much % the price needs to move to reach this level
                    price_change_pct = ((y_pos - reference_price) / reference_price) * 100

                    # Create and add the label with price
                    pct_label = pg.TextItem(
                        text=f"{price_change_pct:.2f}% ({y_pos:.2f})",
                        color='white',
                        anchor=(0, 0.5)  # Left-aligned horizontally, centered vertically
                    )
                    pct_label.setPos(1.05, y_pos)  # Position near the left edge of the rectangle
                    self.plot_widget.addItem(pct_label)
                    logger.info(f"Added label at position {y_pos:.2f}, price change: {price_change_pct:.2f}%")

            # Add a label for the reference price
            ref_label = pg.TextItem(
                text=f"0% ({reference_price:.2f})",
                color='white',
                anchor=(0, 0.5)  # Left-aligned horizontally, centered vertically
            )
            ref_label.setPos(1.05, reference_price)  # Position near the left edge of the rectangle
            self.plot_widget.addItem(ref_label)
            logger.info(f"Added reference price label at {reference_price:.2f}")

            # No horizontal percentage labels as per user request

            logger.info(f"Added white rectangle from x=1 to x=2.5, y={rect_min} to y={rect_max}")
            logger.info(f"Added price change percentage labels relative to reference price {reference_price:.2f}")

            # Create green and red lines for highs and lows, plus horizontal white dotted lines
            # The x-axis represents percentage: x=1 is 1%, x=2.25 is 50%, rectangle extends to x=2.5

            # Map high prices to x-coordinates based on their percentages
            high_x_coords = []
            high_y_coords = []

            # Map low prices to x-coordinates based on their percentages
            low_x_coords = []
            low_y_coords = []

            # For high prices: highest high (1%) at x=1, lowest high (50%) at x=2.5
            for i, idx in enumerate(sorted_high_indices):
                high = high_prices[idx]

                # Calculate percentage for this high price (1% for highest, 50% for lowest)
                if num_high_prices > 1:
                    # Evenly distribute percentages from 1% to 50%
                    reversed_i = num_high_prices - 1 - i
                    percentage = 1 + (reversed_i * (50 - 1) / (num_high_prices - 1))
                else:
                    # If there's only one price, use 1%
                    percentage = 1

                # Map percentage to x-coordinate: 1% -> x=1, 50% -> x=2.25
                x_coord = 1 + (percentage - 1) * (1.25 / 49)  # Map 1-50% to 1-2.25

                high_x_coords.append(x_coord)
                high_y_coords.append(high)

                # Create a horizontal white dotted line for this high price
                # The line spans from x=1 to x=2.5
                high_dotted_line = pg.PlotDataItem(
                    x=[1, 2.5],  # From left to right of rectangle
                    y=[high, high],  # Same y-coordinate (horizontal line)
                    pen=pg.mkPen(color='white', width=1, style=QtCore.Qt.PenStyle.DotLine)  # White dotted line
                )
                self.plot_widget.addItem(high_dotted_line)

            # For low prices: lowest low (1%) at x=1, highest low (50%) at x=2.5
            for i, idx in enumerate(sorted_low_indices):
                low = low_prices[idx]

                # Calculate percentage for this low price (1% for lowest, 50% for highest)
                if num_low_prices > 1:
                    # Evenly distribute percentages from 1% to 50%
                    percentage = 1 + (i * (50 - 1) / (num_low_prices - 1))
                else:
                    # If there's only one price, use 1%
                    percentage = 1

                # Map percentage to x-coordinate: 1% -> x=1, 50% -> x=2.25
                x_coord = 1 + (percentage - 1) * (1.25 / 49)  # Map 1-50% to 1-2.25

                low_x_coords.append(x_coord)
                low_y_coords.append(low)

                # Create a horizontal white dotted line for this low price
                # The line spans from x=1 to x=2.5
                low_dotted_line = pg.PlotDataItem(
                    x=[1, 2.5],  # From left to right of rectangle
                    y=[low, low],  # Same y-coordinate (horizontal line)
                    pen=pg.mkPen(color='white', width=1, style=QtCore.Qt.PenStyle.DotLine)  # White dotted line
                )
                self.plot_widget.addItem(low_dotted_line)

            # Create smooth curvy green line for high prices using sinusoidal waves and cubic splines
            smooth_high_x, smooth_high_y = None, None
            if len(high_x_coords) > 0:
                try:
                    # Create smooth curve using cubic spline interpolation and sinusoidal waves
                    smooth_high_x, smooth_high_y = self.create_smooth_curve(high_x_coords, high_y_coords, num_points=150)

                    # Create a smooth curvy line connecting the points
                    high_line = pg.PlotDataItem(
                        x=smooth_high_x,
                        y=smooth_high_y,
                        pen=pg.mkPen(color='green', width=2),
                        symbolPen=None,  # No pen for symbols (dots)
                        symbolBrush=None,  # No brush for symbols (dots)
                        symbol=None  # No symbols (dots)
                    )
                    self.plot_widget.addItem(high_line)
                    logger.info("Added smooth curvy green line for high prices using sinusoidal waves and cubic splines")
                except Exception as e:
                    # Fallback to simple line if smooth curve creation fails
                    logger.warning(f"Creating smooth curve failed for high prices: {str(e)}")
                    # Sort coordinates by x-value to ensure proper line drawing
                    sorted_indices = np.argsort(high_x_coords)
                    sorted_high_x = np.array(high_x_coords)[sorted_indices]
                    sorted_high_y = np.array(high_y_coords)[sorted_indices]

                    # Store fallback coordinates for intersection detection
                    smooth_high_x, smooth_high_y = sorted_high_x, sorted_high_y

                    high_line = pg.PlotDataItem(
                        x=sorted_high_x,
                        y=sorted_high_y,
                        pen=pg.mkPen(color='green', width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(high_line)
            else:
                logger.warning("No high price points to draw line")
                # No points to draw a line

            # Create smooth curvy red line for low prices using sinusoidal waves and cubic splines
            smooth_low_x, smooth_low_y = None, None
            if len(low_x_coords) > 0:
                try:
                    # Create smooth curve using cubic spline interpolation and sinusoidal waves
                    smooth_low_x, smooth_low_y = self.create_smooth_curve(low_x_coords, low_y_coords, num_points=150)

                    # Create a smooth curvy line connecting the points
                    low_line = pg.PlotDataItem(
                        x=smooth_low_x,
                        y=smooth_low_y,
                        pen=pg.mkPen(color='red', width=2),
                        symbolPen=None,  # No pen for symbols (dots)
                        symbolBrush=None,  # No brush for symbols (dots)
                        symbol=None  # No symbols (dots)
                    )
                    self.plot_widget.addItem(low_line)
                    logger.info("Added smooth curvy red line for low prices using sinusoidal waves and cubic splines")
                except Exception as e:
                    # Fallback to simple line if smooth curve creation fails
                    logger.warning(f"Creating smooth curve failed for low prices: {str(e)}")
                    # Sort coordinates by x-value to ensure proper line drawing
                    sorted_indices = np.argsort(low_x_coords)
                    sorted_low_x = np.array(low_x_coords)[sorted_indices]
                    sorted_low_y = np.array(low_y_coords)[sorted_indices]

                    # Store fallback coordinates for intersection detection
                    smooth_low_x, smooth_low_y = sorted_low_x, sorted_low_y

                    low_line = pg.PlotDataItem(
                        x=sorted_low_x,
                        y=sorted_low_y,
                        pen=pg.mkPen(color='red', width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(low_line)
            else:
                logger.warning("No low price points to draw line")
                # No points to draw a line

            # Find and mark intersections between green and red lines
            if len(high_x_coords) > 0 and len(low_x_coords) > 0:
                try:
                    # Use the smooth curve coordinates for intersection detection
                    if smooth_high_x is not None and smooth_high_y is not None and smooth_low_x is not None and smooth_low_y is not None:
                        # Find intersections between the smooth green and red curves
                        intersections = self.find_line_intersections(
                            smooth_high_x, smooth_high_y, smooth_low_x, smooth_low_y
                        )
                    else:
                        # Fallback to original coordinates if smooth curves weren't created
                        sorted_high_indices = np.argsort(high_x_coords)
                        sorted_high_x = np.array(high_x_coords)[sorted_high_indices]
                        sorted_high_y = np.array(high_y_coords)[sorted_high_indices]

                        sorted_low_indices = np.argsort(low_x_coords)
                        sorted_low_x = np.array(low_x_coords)[sorted_low_indices]
                        sorted_low_y = np.array(low_y_coords)[sorted_low_indices]

                        # Find intersections between the green and red lines
                        intersections = self.find_line_intersections(
                            sorted_high_x, sorted_high_y, sorted_low_x, sorted_low_y
                        )

                    # Store intersections for external access (for AK's Odds Zones)
                    self.last_intersections = intersections.copy()
                    logger.info(f"Stored {len(intersections)} intersections for AK's Odds Zones: {[f'({x:.2f}, {y:.2f})' for x, y in intersections]}")

                    # Mark each intersection with a yellow horizontal line
                    for i, (_, intersection_y) in enumerate(intersections):
                        # Create a yellow horizontal infinity line at the intersection
                        intersection_line = pg.InfiniteLine(
                            pos=intersection_y,
                            angle=0,  # Horizontal line
                            pen=pg.mkPen(color='yellow', width=2, style=QtCore.Qt.PenStyle.DashLine),
                            label=f"theoretical intersect ({intersection_y:.2f})",
                            labelOpts={
                                'position': 0.1,  # Position the label at 10% from the left
                                'color': 'white',  # White text for label
                                'fill': (0, 0, 0, 50),
                                'movable': True
                            }
                        )
                        self.plot_widget.addItem(intersection_line)

                        logger.info(f"Added yellow intersection line at y={intersection_y:.2f}")

                    if intersections:
                        logger.info(f"Found and marked {len(intersections)} intersections between green and red lines")
                    else:
                        logger.info("No intersections found between green and red lines")

                except Exception as e:
                    logger.error(f"Error finding line intersections: {str(e)}", exc_info=True)

            logger.info(f"Added smooth curvy green and red lines for high/low prices using sinusoidal waves and cubic splines, plus white horizontal dotted lines")

            # Add white solid line at the 50% level (x=2.25) extending to x=2.45
            # For high prices
            if len(high_x_coords) > 0:
                # Find the x-coordinate closest to 2.25 (50% level)
                high_50pct_idx = np.argmin(np.abs(np.array(high_x_coords) - 2.25))
                high_50pct_y = high_y_coords[high_50pct_idx]

                # Create a white solid line from x=2.25 to x=2.45
                high_end_line = pg.PlotDataItem(
                    x=[2.25, 2.45],  # From 50% level to x=2.45
                    y=[high_50pct_y, high_50pct_y],  # Same y-coordinate (horizontal line)
                    pen=pg.mkPen(color='white', width=2)  # White solid line
                )
                self.plot_widget.addItem(high_end_line)

            # For low prices
            if len(low_x_coords) > 0:
                # Find the x-coordinate closest to 2.25 (50% level)
                low_50pct_idx = np.argmin(np.abs(np.array(low_x_coords) - 2.25))
                low_50pct_y = low_y_coords[low_50pct_idx]

                # Create a white solid line from x=2.25 to x=2.45
                low_end_line = pg.PlotDataItem(
                    x=[2.25, 2.45],  # From 50% level to x=2.45
                    y=[low_50pct_y, low_50pct_y],  # Same y-coordinate (horizontal line)
                    pen=pg.mkPen(color='white', width=2)  # White solid line
                )
                self.plot_widget.addItem(low_end_line)

            logger.info(f"Added white solid lines at 50% level extending to x=2.45")

            # Add vertical line with arrows at x=2.35 connecting the green and red lines
            if len(high_x_coords) > 0 and len(low_x_coords) > 0:
                # Get the y-coordinates at the 50% level (x=2.25)
                high_50pct_y = high_y_coords[high_50pct_idx]
                low_50pct_y = low_y_coords[low_50pct_idx]

                # Create a vertical line with arrowheads at both ends
                # We need to use a QGraphicsPathItem for this since PyQtGraph doesn't have built-in double-headed arrows

                # Create a QPainterPath for the line with arrowheads
                path = QtGui.QPainterPath()

                # Start at the top (green line)
                path.moveTo(2.35, high_50pct_y)

                # Draw line to the bottom (red line)
                path.lineTo(2.35, low_50pct_y)

                # Add a stacked "Average Price Range" label in the middle of the vertical line
                # Calculate the middle point of the line
                middle_y = (high_50pct_y + low_50pct_y) / 2

                # Create a text item with stacked text (one word per line)
                normal_price_range_label = pg.TextItem(
                    text="Average\nPrice\nRange",
                    color='white',
                    anchor=(0.5, 0.5)  # Centered horizontally and vertically
                )
                normal_price_range_label.setPos(2.35, middle_y)  # Position exactly at the middle of the vertical line
                self.plot_widget.addItem(normal_price_range_label)

                # Create a path for the vertical line with arrowheads
                path = QtGui.QPainterPath()

                # Start at the top (green line)
                path.moveTo(2.35, high_50pct_y)

                # Draw line to the bottom (red line)
                path.lineTo(2.35, low_50pct_y)

                # Calculate the length of the vertical line
                vertical_line_length = abs(high_50pct_y - low_50pct_y)

                # Define arrowhead size - height is 5% of line length, width is based on stddev
                arrow_height = vertical_line_length * 0.05  # 5% of line length
                arrow_width = 0.025  # Default width, will be adjusted based on stddev

                # Calculate standard deviation of the width (for a vertical line, this is just the x-coordinate)
                # Since it's a vertical line, we use a small percentage of the line length as the stddev
                # For arrows pointing at 50% levels, use 0.25 stdv
                stddev_estimate = vertical_line_length * 0.005  # Estimate 1 stddev as 0.5% of line length
                arrow_width = stddev_estimate * 0.25  # 0.25 stdv

                # Add arrowhead at the top (pointing up)
                # For the top arrow, the angle is 90 degrees (pointing up)
                path.moveTo(2.35, high_50pct_y)  # Start at the tip
                path.lineTo(2.35 - arrow_width/2, high_50pct_y + arrow_height)  # Draw to left corner
                path.lineTo(2.35 + arrow_width/2, high_50pct_y + arrow_height)  # Draw to right corner
                path.lineTo(2.35, high_50pct_y)  # Close the triangle

                # Add arrowhead at the bottom (pointing down)
                # For the bottom arrow, the angle is 270 degrees (pointing down)
                path.moveTo(2.35, low_50pct_y)  # Start at the tip
                path.lineTo(2.35 - arrow_width/2, low_50pct_y - arrow_height)  # Draw to left corner
                path.lineTo(2.35 + arrow_width/2, low_50pct_y - arrow_height)  # Draw to right corner
                path.lineTo(2.35, low_50pct_y)  # Close the triangle

                # Create a QGraphicsPathItem with the path
                arrow_line = QtWidgets.QGraphicsPathItem(path)

                # Set the pen and brush
                arrow_line.setPen(pg.mkPen(color='white', width=2))
                arrow_line.setBrush(pg.mkBrush('white'))

                # Add the item to the plot
                self.plot_widget.addItem(arrow_line)

                # Now add two more lines with arrows:
                # 1. From green line at x=2.35 to 1% lows
                # 2. From red line at x=2.35 to 1% highs

                # Find the 1% low point (lowest low)
                low_1pct_y = low_prices[lowest_low_idx]  # The lowest low is at 1%
                low_1pct_x = 1 + (1 - 1) * (1.25 / 49)  # x-coordinate for 1% (should be x=1)

                # Find the 1% high point (highest high)
                high_1pct_y = high_prices[highest_high_idx]  # The highest high is at 1%
                high_1pct_x = 1 + (1 - 1) * (1.25 / 49)  # x-coordinate for 1% (should be x=1)

                # Create a line from green line to 1% lows
                green_to_low_line = pg.PlotDataItem(
                    x=[2.35, low_1pct_x],
                    y=[high_50pct_y, low_1pct_y],
                    pen=pg.mkPen(color='white', width=2),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(green_to_low_line)

                # Calculate angle for the arrow
                dx = low_1pct_x - 2.35
                dy = low_1pct_y - high_50pct_y
                angle = np.degrees(np.arctan2(dy, dx))

                # Create a simple arrow at the 1% low end
                green_to_low_arrow = ArrowItem(
                    angle=angle,  # Point in the direction of the line
                    tipAngle=30,  # Standard arrow head
                    baseAngle=20,  # Standard base
                    headLen=15,  # Standard length
                    tailLen=0,  # No tail
                    tailWidth=0,  # No tail
                    pen=None,  # No border
                    brush='white'  # White color
                )

                # Position the arrow at the 1% low point
                green_to_low_arrow.setPos(low_1pct_x, low_1pct_y)
                self.plot_widget.addItem(green_to_low_arrow)

                # Create a line from red line to 1% highs
                red_to_high_line = pg.PlotDataItem(
                    x=[2.35, high_1pct_x],
                    y=[low_50pct_y, high_1pct_y],
                    pen=pg.mkPen(color='white', width=2),
                    symbolPen=None,
                    symbolBrush=None,
                    symbol=None
                )
                self.plot_widget.addItem(red_to_high_line)

                # Calculate angle for the arrow
                dx = high_1pct_x - 2.35
                dy = high_1pct_y - low_50pct_y
                angle = np.degrees(np.arctan2(dy, dx))

                # Create a simple arrow at the 1% high end
                red_to_high_arrow = ArrowItem(
                    angle=angle,  # Point in the direction of the line
                    tipAngle=30,  # Standard arrow head
                    baseAngle=20,  # Standard base
                    headLen=15,  # Standard length
                    tailLen=0,  # No tail
                    tailWidth=0,  # No tail
                    pen=None,  # No border
                    brush='white'  # White color
                )

                # Position the arrow at the 1% high point
                red_to_high_arrow.setPos(high_1pct_x, high_1pct_y)
                self.plot_widget.addItem(red_to_high_arrow)

                # Add green line from highest high to current price at x=3.75
                # Get the reference price (current price)
                reference_price_info = self.get_reference_price()
                if reference_price_info is not None:
                    current_price, _, _ = reference_price_info

                    # Arrow size is now defined in the ArrowItem parameters

                    # Get the calculation mode to determine arrow direction
                    calculation_mode = self.get_calculation_mode()

                    # Create a line from highest high to current price
                    # We'll add the arrow separately using ArrowItem
                    high_to_current_line = pg.PlotDataItem(
                        x=[3.75, 3.75],
                        y=[highest_high, current_price],
                        pen=pg.mkPen(color=self.chart_colors['bearish'], width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(high_to_current_line)

                    # Determine arrow direction based on calculation mode
                    if calculation_mode == "percentage_based":
                        # For percentage based, arrow should point from highest high to 0%
                        # If highest high is positive, arrow points down; if negative, arrow points up
                        if highest_high > 0:
                            arrow_angle = 270  # Arrow points down toward 0
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward 0
                            arrow_direction = "up"
                    else:
                        # For price-based modes, arrow should point from highest high to current/pivot price
                        # If highest high is above current price, arrow points down; otherwise, points up
                        if highest_high > current_price:
                            arrow_angle = 270  # Arrow points down toward current/pivot price
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward current/pivot price
                            arrow_direction = "up"

                    logger.info(f"Highest high to current price arrow direction: {arrow_direction}")

                    # Create an arrow at the current price end using ArrowItem
                    high_to_current_arrow = ArrowItem(
                        angle=arrow_angle,  # Point in the direction determined above
                        tipAngle=30,  # Narrow arrow head
                        baseAngle=20,  # Narrow base
                        headLen=15,  # Length of the arrow head
                        tailLen=0,  # No tail
                        tailWidth=0,  # No tail
                        pen=None,  # No border
                        brush=self.chart_colors['bearish']  # Red color
                    )
                    # Position the arrow at the end of the line (at current price)
                    high_to_current_arrow.setPos(3.75, current_price)
                    self.plot_widget.addItem(high_to_current_arrow)

                    # Create a line from lowest high to current price
                    # We'll add the arrow separately using ArrowItem
                    low_high_to_current_line = pg.PlotDataItem(
                        x=[3.75, 3.75],
                        y=[lowest_high, current_price],
                        pen=pg.mkPen(color=self.chart_colors['bullish'], width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(low_high_to_current_line)

                    # Determine arrow direction based on calculation mode
                    if calculation_mode == "percentage_based":
                        # For percentage based, arrow should point from lowest high to 0%
                        # If lowest high is positive, arrow points down; if negative, arrow points up
                        if lowest_high > 0:
                            arrow_angle = 270  # Arrow points down toward 0
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward 0
                            arrow_direction = "up"
                    else:
                        # For price-based modes, arrow should point from lowest high to current/pivot price
                        # If lowest high is above current price, arrow points down; otherwise, points up
                        if lowest_high > current_price:
                            arrow_angle = 270  # Arrow points down toward current/pivot price
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward current/pivot price
                            arrow_direction = "up"

                    logger.info(f"Lowest high to current price arrow direction: {arrow_direction}")

                    # Create an arrow at the current price end using ArrowItem
                    low_high_to_current_arrow = ArrowItem(
                        angle=arrow_angle,  # Point in the direction determined above
                        tipAngle=30,  # Narrow arrow head
                        baseAngle=20,  # Narrow base
                        headLen=15,  # Length of the arrow head
                        tailLen=0,  # No tail
                        tailWidth=0,  # No tail
                        pen=None,  # No border
                        brush=self.chart_colors['bullish']  # Green color
                    )
                    # Position the arrow at the end of the line (at current price)
                    low_high_to_current_arrow.setPos(3.75, current_price)
                    self.plot_widget.addItem(low_high_to_current_arrow)

                    logger.info(f"Added red line from highest high to current price at x=3.75 with arrow")
                    logger.info(f"Added green line from lowest high to current price at x=3.75 with arrow")

                    # Now add similar lines for low prices at x=4.75

                    # Create a line from highest low to current price
                    # We'll add the arrow separately using ArrowItem
                    high_low_to_current_line = pg.PlotDataItem(
                        x=[4.75, 4.75],
                        y=[highest_low, current_price],
                        pen=pg.mkPen(color=self.chart_colors['bullish'], width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(high_low_to_current_line)

                    # Determine arrow direction based on calculation mode
                    if calculation_mode == "percentage_based":
                        # For percentage based, arrow should point from highest low to 0%
                        # If highest low is positive, arrow points down; if negative, arrow points up
                        if highest_low > 0:
                            arrow_angle = 270  # Arrow points down toward 0
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward 0
                            arrow_direction = "up"
                    else:
                        # For price-based modes, arrow should point from highest low to current/pivot price
                        # If highest low is above current price, arrow points down; otherwise, points up
                        if highest_low > current_price:
                            arrow_angle = 270  # Arrow points down toward current/pivot price
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward current/pivot price
                            arrow_direction = "up"

                    logger.info(f"Highest low to current price arrow direction: {arrow_direction}")

                    # Create an arrow at the current price end using ArrowItem
                    high_low_to_current_arrow = ArrowItem(
                        angle=arrow_angle,  # Point in the direction determined above
                        tipAngle=30,  # Narrow arrow head
                        baseAngle=20,  # Narrow base
                        headLen=15,  # Length of the arrow head
                        tailLen=0,  # No tail
                        tailWidth=0,  # No tail
                        pen=None,  # No border
                        brush=self.chart_colors['bullish']  # Green color
                    )
                    # Position the arrow at the end of the line (at current price)
                    high_low_to_current_arrow.setPos(4.75, current_price)
                    self.plot_widget.addItem(high_low_to_current_arrow)

                    # Create a line from lowest low to current price
                    # We'll add the arrow separately using ArrowItem
                    lowest_low_to_current_line = pg.PlotDataItem(
                        x=[4.75, 4.75],
                        y=[lowest_low, current_price],
                        pen=pg.mkPen(color=self.chart_colors['bearish'], width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(lowest_low_to_current_line)

                    # Determine arrow direction based on calculation mode
                    if calculation_mode == "percentage_based":
                        # For percentage based, arrow should point from lowest low to 0%
                        # If lowest low is positive, arrow points down; if negative, arrow points up
                        if lowest_low > 0:
                            arrow_angle = 270  # Arrow points down toward 0
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward 0
                            arrow_direction = "up"
                    else:
                        # For price-based modes, arrow should point from lowest low to current/pivot price
                        # If lowest low is above current price, arrow points down; otherwise, points up
                        if lowest_low > current_price:
                            arrow_angle = 270  # Arrow points down toward current/pivot price
                            arrow_direction = "down"
                        else:
                            arrow_angle = 90  # Arrow points up toward current/pivot price
                            arrow_direction = "up"

                    logger.info(f"Lowest low to current price arrow direction: {arrow_direction}")

                    # Create an arrow at the current price end using ArrowItem
                    lowest_low_to_current_arrow = ArrowItem(
                        angle=arrow_angle,  # Point in the direction determined above
                        tipAngle=30,  # Narrow arrow head
                        baseAngle=20,  # Narrow base
                        headLen=15,  # Length of the arrow head
                        tailLen=0,  # No tail
                        tailWidth=0,  # No tail
                        pen=None,  # No border
                        brush=self.chart_colors['bearish']  # Red color
                    )
                    # Position the arrow at the end of the line (at current price)
                    lowest_low_to_current_arrow.setPos(4.75, current_price)
                    self.plot_widget.addItem(lowest_low_to_current_arrow)

                    logger.info(f"Added green line from highest low to current price at x=4.75 with arrow")
                    logger.info(f"Added red line from lowest low to current price at x=4.75 with arrow")

                logger.info(f"Added vertical line with arrows at x=2.35 connecting green and red lines")
                logger.info(f"Added line from green line to 1% lows with arrow")
                logger.info(f"Added line from red line to 1% highs with arrow")

            # Add "Price" and "Odds" labels above the columns
            # Calculate the y-positions for the labels based on percentage above highest high
            highest_value = rect_max  # This is the highest high
            label_y_pos = highest_value * 1.0025  # 0.25% above highest high
            category_y_pos = highest_value * 1.004  # 0.4% above highest high

            # Log the positions for verification
            logger.info(f"Highest high value: {highest_value}")
            logger.info(f"'Price'/'Odds' labels positioned at y={label_y_pos} (0.25% above highest high)")
            logger.info(f"'Bullish/Bearish Moves' labels positioned at y={category_y_pos} (0.4% above highest high)")

            # Add "Price" label starting at x=3 above high prices column
            high_price_header = pg.TextItem(
                text="Price",
                color='white',
                anchor=(0, 0)  # Left-aligned horizontally, align to top
            )
            high_price_header.setPos(high_x_pos, label_y_pos)
            self.plot_widget.addItem(high_price_header)

            # Add "Price" label starting at x=4 above low prices column
            low_price_header = pg.TextItem(
                text="Price",
                color='white',
                anchor=(0, 0)  # Left-aligned horizontally, align to top
            )
            low_price_header.setPos(low_x_pos, label_y_pos)
            self.plot_widget.addItem(low_price_header)

            # Add "Odds" label starting at x=3.5 above high percentages column
            high_odds_header = pg.TextItem(
                text="Odds",
                color='white',
                anchor=(0, 0)  # Left-aligned horizontally, align to top
            )
            high_odds_header.setPos(3.5, label_y_pos)
            self.plot_widget.addItem(high_odds_header)

            # Add "Odds" label starting at x=4.5 above low percentages column
            low_odds_header = pg.TextItem(
                text="Odds",
                color='white',
                anchor=(0, 0)  # Left-aligned horizontally, align to top
            )
            low_odds_header.setPos(4.5, label_y_pos)
            self.plot_widget.addItem(low_odds_header)

            # Add "Bullish Movement" label centered above high price and odds columns
            bullish_header = pg.TextItem(
                text="Bullish Movement",
                color='white',
                anchor=(0.5, 0)  # Center horizontally, align to top
            )
            # Position centered between high price (x=3) and high odds (x=3.5)
            bullish_header.setPos(3.25, category_y_pos)
            self.plot_widget.addItem(bullish_header)

            # Add "Bearish Movement" label centered above low price and odds columns
            bearish_header = pg.TextItem(
                text="Bearish Movement",
                color='white',
                anchor=(0.5, 0)  # Center horizontally, align to top
            )
            # Position centered between low price (x=4) and low odds (x=4.5)
            bearish_header.setPos(4.25, category_y_pos)
            self.plot_widget.addItem(bearish_header)

            # Legend removed as per user request

            # Add price-only crosshair to the plot
            self.crosshair = add_price_only_crosshair(self.plot_widget, parent=self.parent)

            # Update status label with the requested format
            occurrences = len(matching_rows)
            total_rows = len(data) if data is not None else 0

            # Check if we're using theoretical values
            is_using_theoretical = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                       'Projected High' in matching_rows.columns and
                                       'Projected Low' in matching_rows.columns)

            # Get the matching mode for display
            matching_mode_display = "Weekday Matching" if (hasattr(self.parent, 'get_matching_mode') and self.parent.get_matching_mode() == 'weekday') else "H/L Matching"

            # Add indicators to status message
            theoretical_indicator = " (Using Projected High/Low)" if is_using_theoretical else ""
            status_message = f"Occurrences: {occurrences} Attribute: {latest_category} days to load: {total_rows} - {matching_mode_display}{theoretical_indicator}"
            self.status_label.setText(status_message)

            # Update the parent's statistics box if available
            if hasattr(self.parent, 'update_statistics_box'):
                # Calculate price levels for the statistics box using the same approach as volatility_graph.py
                # This ensures consistency across all tabs
                price_levels = {
                    'highest_high': highest_high if 'highest_high' in locals() else None,
                    'lowest_low': lowest_low if 'lowest_low' in locals() else None,
                    'average_high': np.mean(high_prices) if 'high_prices' in locals() and len(high_prices) > 0 else None,
                    'average_low': np.mean(low_prices) if 'low_prices' in locals() and len(low_prices) > 0 else None,
                    'lowest_high': lowest_high if 'lowest_high' in locals() else None,
                    'highest_low': highest_low if 'highest_low' in locals() else None,
                    # Include the raw high and low values for count calculations
                    'high_values': high_prices if 'high_prices' in locals() else [],
                    'low_values': low_prices if 'low_prices' in locals() else []
                }

                # Calculate true average (mean) of all highs
                if 'high_prices' in locals() and len(high_prices) > 0:
                    # Convert to pandas Series for easier handling
                    high_series = pd.Series(high_prices)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN values
                    high_series = high_series.dropna()

                    if len(high_series) > 0:
                        # Calculate the true average (mean) of all highs
                        true_avg_high = high_series.mean()
                        logger.info(f"Calculated true average of all highs: {true_avg_high}")

                        # Store for use in the white line labels
                        price_levels['true_avg_high'] = true_avg_high
                    else:
                        price_levels['true_avg_high'] = None
                else:
                    price_levels['true_avg_high'] = None

                # Calculate true average (mean) of all lows
                if 'low_prices' in locals() and len(low_prices) > 0:
                    # Convert to pandas Series for easier handling
                    low_series = pd.Series(low_prices)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN values
                    low_series = low_series.dropna()

                    if len(low_series) > 0:
                        # Calculate the true average (mean) of all lows
                        true_avg_low = low_series.mean()
                        logger.info(f"Calculated true average of all lows: {true_avg_low}")

                        # Store for use in the white line labels
                        price_levels['true_avg_low'] = true_avg_low
                    else:
                        price_levels['true_avg_low'] = None
                else:
                    price_levels['true_avg_low'] = None

                # Calculate median of all highs (long median) - State-of-the-art implementation
                if 'high_prices' in locals() and len(high_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_prices)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values for robust calculation
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Use numpy's median function for mathematically correct median calculation
                        # This handles both odd and even count cases properly:
                        # - Odd count: returns middle value
                        # - Even count: returns average of two middle values
                        median_value = np.median(high_series.values)

                        # Store the median value
                        price_levels['avg_high_lowest_high'] = median_value

                        # Enhanced logging with statistical validation
                        sorted_values = np.sort(high_series.values)
                        n = len(sorted_values)
                        logger.info(f"HIGH MEDIAN: {median_value:.6f} (n={n}, range={sorted_values[0]:.6f}-{sorted_values[-1]:.6f})")
                    else:
                        logger.warning("No valid high values after data cleaning for median calculation")
                        price_levels['avg_high_lowest_high'] = None
                else:
                    price_levels['avg_high_lowest_high'] = None

                # Calculate median of all lows (short median) - State-of-the-art implementation
                if 'low_prices' in locals() and len(low_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_prices)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values for robust calculation
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Use numpy's median function for mathematically correct median calculation
                        # This handles both odd and even count cases properly:
                        # - Odd count: returns middle value
                        # - Even count: returns average of two middle values
                        median_value = np.median(low_series.values)

                        # Store the median value
                        price_levels['avg_low_highest_low'] = median_value

                        # Enhanced logging with statistical validation
                        sorted_values = np.sort(low_series.values)
                        n = len(sorted_values)
                        logger.info(f"LOW MEDIAN: {median_value:.6f} (n={n}, range={sorted_values[0]:.6f}-{sorted_values[-1]:.6f})")
                    else:
                        logger.warning("No valid low values after data cleaning for median calculation")
                        price_levels['avg_low_highest_low'] = None
                else:
                    price_levels['avg_low_highest_low'] = None

                # Calculate apex (median between highest high and lowest low)
                if price_levels['highest_high'] is not None and price_levels['lowest_low'] is not None:
                    price_levels['apex'] = (price_levels['highest_high'] + price_levels['lowest_low']) / 2
                    logger.info(f"Calculated apex: {price_levels['apex']}")
                else:
                    price_levels['apex'] = None

                # Calculate maxavg for highs (average of top 50% highs/highest highs) - State-of-the-art
                if 'high_prices' in locals() and len(high_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_prices)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in descending order for top percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=False).values
                        # Take the top 50% (use ceiling to ensure at least 1 value for small datasets)
                        top_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        top_half_highs = sorted_high_values[:top_half_count]
                        # Calculate robust average
                        price_levels['maxavg_high'] = np.mean(top_half_highs)
                        logger.info(f"Calculated maxavg_high from {top_half_count}/{len(high_prices)} values: {price_levels['maxavg_high']:.6f}")
                    else:
                        price_levels['maxavg_high'] = None
                        logger.warning("No valid high values after data cleaning for maxavg_high")
                else:
                    price_levels['maxavg_high'] = None

                # Calculate maxavg for lows (average of bottom 50% lowest lows) - State-of-the-art
                if 'low_prices' in locals() and len(low_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_prices)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in ascending order for bottom percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest lows) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        bottom_half_lows = sorted_low_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['maxavg_low'] = np.mean(bottom_half_lows)
                        logger.info(f"Calculated maxavg_low from {bottom_half_count}/{len(low_prices)} values: {price_levels['maxavg_low']:.6f}")
                    else:
                        price_levels['maxavg_low'] = None
                        logger.warning("No valid low values after data cleaning for maxavg_low")
                else:
                    price_levels['maxavg_low'] = None

                # Calculate minavg for highs (average of bottom 50% highs/lowest highs) - State-of-the-art
                if 'high_prices' in locals() and len(high_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_prices)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in ascending order for bottom percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest highs) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        bottom_half_highs = sorted_high_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['minavg_high'] = np.mean(bottom_half_highs)
                        logger.info(f"Calculated minavg_high from {bottom_half_count}/{len(high_prices)} values: {price_levels['minavg_high']:.6f}")
                    else:
                        price_levels['minavg_high'] = None
                        logger.warning("No valid high values after data cleaning for minavg_high")
                else:
                    price_levels['minavg_high'] = None

                # Calculate minavg for lows (average of top 50% of lows/highest lows) - State-of-the-art
                if 'low_prices' in locals() and len(low_prices) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_prices)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in descending order for top percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=False).values
                        # Take the top 50% (highest lows) - use ceiling to ensure at least 1 value
                        top_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        top_half_lows = sorted_low_values[:top_half_count]
                        # Calculate robust average
                        price_levels['minavg_low'] = np.mean(top_half_lows)
                        logger.info(f"Calculated minavg_low from {top_half_count}/{len(low_prices)} values: {price_levels['minavg_low']:.6f}")
                    else:
                        price_levels['minavg_low'] = None
                        logger.warning("No valid low values after data cleaning for minavg_low")
                else:
                    price_levels['minavg_low'] = None

                # Add wall_long and wall_short values based on maxavg values
                if price_levels.get('maxavg_high') is not None:
                    price_levels['wall_long'] = price_levels['maxavg_high']
                    logger.info(f"Set wall_long to maxavg_high: {price_levels['wall_long']}")
                else:
                    price_levels['wall_long'] = None

                if price_levels.get('maxavg_low') is not None:
                    price_levels['wall_short'] = price_levels['maxavg_low']
                    logger.info(f"Set wall_short to maxavg_low: {price_levels['wall_short']}")
                else:
                    price_levels['wall_short'] = None

                # Extract theoretical price information if available
                theoretical_prices = []
                if is_using_theoretical and 'Projected High' in matching_rows.columns and 'Projected Low' in matching_rows.columns:
                    try:
                        # Get unique indices from the matching rows
                        indices = matching_rows.index.tolist()

                        # Add theoretical prices for each index
                        for idx in indices:
                            row = matching_rows.loc[idx]
                            if pd.notna(row.get('Projected High')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'high',
                                    'price': float(row['Projected High'])
                                })
                            if pd.notna(row.get('Projected Low')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'low',
                                    'price': float(row['Projected Low'])
                                })
                    except Exception as e:
                        logger.warning(f"Error extracting theoretical prices: {str(e)}")

                # Log the price levels for debugging
                logger.info(f"Calculated price levels for statistics box: {price_levels.keys()}")

                # Get the occurrence count limit if set
                occurrence_limit = 0
                if hasattr(self.parent, 'get_occurrence_count'):
                    occurrence_limit = self.parent.get_occurrence_count()

                # Update the status label with occurrence count information
                total_rows = len(data) if data is not None else 0

                # Create status message based on whether occurrence limit is set
                if occurrence_limit > 0:
                    # If occurrence limit is set, don't show days to load
                    status_message = f"FWL Odds - Occurrences: {occurrences} (Limited to {occurrence_limit}) Attribute: {latest_category}"
                else:
                    # If no occurrence limit, show days to load
                    status_message = f"FWL Odds - Occurrences: {occurrences} Attribute: {latest_category} days to load: {total_rows}"

                self.status_label.setText(status_message)

                # Store theoretical prices as an attribute for the data subtab
                self.theoretical_prices = theoretical_prices
                # Also store as projected_prices for compatibility with data subtab
                self.projected_prices = theoretical_prices

                # Update the parent's statistics box with the same parameters as used in volatility_graph.py
                # Get ETH zones data from multiple sources
                eth_zones_data = {}

                # First try to get from density graph tab
                if hasattr(self.parent, 'density_graph_tab') and hasattr(self.parent.density_graph_tab, 'eth_zones_data'):
                    eth_zones_data = getattr(self.parent.density_graph_tab, 'eth_zones_data', {})

                # If not found, try to get from parent's stored data
                if not eth_zones_data and hasattr(self.parent, 'eth_zones_data'):
                    eth_zones_data = getattr(self.parent, 'eth_zones_data', {})

                self.parent.update_statistics_box(price_levels, latest_category, occurrences, theoretical_prices, eth_zones_data=eth_zones_data)

                # Add historical day range line if viewing historical data
                if hasattr(self.parent, 'is_viewing_historical_data') and self.parent.is_viewing_historical_data():
                    historical_high, historical_low = self.parent.get_historical_day_range()
                    if historical_high is not None and historical_low is not None:
                        try:
                            # Create white vertical line at x=2.35 from historical low to historical high
                            historical_range_line = pg.PlotDataItem(
                                x=[2.35, 2.35],
                                y=[historical_low, historical_high],
                                pen=pg.mkPen(color='white', width=3),
                                symbolPen=None,
                                symbolBrush=None,
                                symbol=None
                            )
                            self.plot_widget.addItem(historical_range_line)

                            # Add labels for the next day's high and low
                            next_day_high_label = pg.TextItem(
                                text=f"Next Day High: {historical_high:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_high_label.setPos(2.4, historical_high)
                            self.plot_widget.addItem(next_day_high_label)

                            next_day_low_label = pg.TextItem(
                                text=f"Next Day Low: {historical_low:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_low_label.setPos(2.4, historical_low)
                            self.plot_widget.addItem(next_day_low_label)

                            logger.info(f"Added next day range line at x=2.35 from {historical_low:.2f} to {historical_high:.2f}")
                        except Exception as e:
                            logger.warning(f"Failed to create historical day range line: {str(e)}")

                # Plot toggled statistics if available
                if hasattr(self.parent, 'get_toggled_stats_data'):
                    toggled_stats = self.parent.get_toggled_stats_data()
                    if toggled_stats:
                        self.plot_toggled_statistics(toggled_stats, current_price)

        except Exception as e:
            logger.error(f"Error generating FWL odds: {str(e)}", exc_info=True)
            self.status_label.setText(f"Error generating FWL odds: {str(e)}")
            self.plot_widget.clear()

    def get_data_tab(self):
        """Get the Data tab reference."""
        return self.data_tab

    def plot_toggled_statistics(self, toggled_stats, reference_price):
        """Plot toggled statistics on the graph

        Args:
            toggled_stats (list): List of dictionaries with statistics data
            reference_price (float): Current reference price
        """
        try:
            if not toggled_stats:
                return

            logger.info(f"Plotting {len(toggled_stats)} toggled statistics")

            # Define colors for different types of stats
            bear_color = self.chart_colors['bearish']
            bull_color = self.chart_colors['bullish']
            long_color = self.chart_colors.get('long', '#00FF00')  # Green for long theoretical
            short_color = self.chart_colors.get('short', '#FF00FF')  # Magenta for short theoretical

            # Plot each toggled statistic
            for stat in toggled_stats:
                if 'price' not in stat or 'type' not in stat:
                    logger.warning(f"Skipping stat without price or type: {stat}")
                    continue

                price = stat['price']
                stat_type = stat['type']

                # Determine color and label based on type
                if stat_type == 'bear':
                    color = bear_color
                    label_prefix = "Bear"
                    arrow_angle = 270  # Down arrow
                elif stat_type == 'bull':
                    color = bull_color
                    label_prefix = "Bull"
                    arrow_angle = 90  # Up arrow
                elif stat_type == 'long':
                    color = long_color
                    label_prefix = "Long"
                    arrow_angle = 90  # Up arrow
                elif stat_type == 'short':
                    color = short_color
                    label_prefix = "Short"
                    arrow_angle = 270  # Down arrow
                else:
                    logger.warning(f"Unknown stat type: {stat_type}")
                    continue

                # Create a horizontal line at the price level with win rate if available
                winrate_str = stat.get('winrate_str', '')
                label_text = f"{label_prefix}: {price:.2f} ({winrate_str})" if winrate_str else f"{label_prefix}: {price:.2f}"
                line = pg.InfiniteLine(
                    pos=price,
                    angle=0,  # Horizontal line
                    pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine),  # Dashed line
                    label=label_text,
                    labelOpts={
                        'position': 0.95,  # Position near the right end
                        'color': color,
                        'movable': False,
                        'fill': (0, 0, 0, 0)  # Transparent background
                    }
                )
                self.plot_widget.addItem(line)

                # If reference price is available, add a vertical line from reference price to the stat price
                if reference_price is not None:
                    # Create a vertical line
                    vertical_line = pg.PlotDataItem(
                        x=[5.25, 5.25],  # Vertical line at x=5.25 (to the right of the main display)
                        y=[reference_price, price],
                        pen=pg.mkPen(color=color, width=2),
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None
                    )
                    self.plot_widget.addItem(vertical_line)

                    # Add arrow head at the end of the vertical line
                    arrow = ArrowItem(
                        angle=arrow_angle,
                        tipAngle=30,  # Narrow arrow head
                        baseAngle=20,  # Narrow base
                        headLen=15,  # Length of the arrow head
                        tailLen=0,  # No tail
                        tailWidth=0,  # No tail
                        pen=None,  # No border
                        brush=color  # Same color as the line
                    )
                    # Position the arrow at the end of the line
                    arrow.setPos(5.25, price)
                    self.plot_widget.addItem(arrow)

            logger.info("Successfully plotted toggled statistics")
        except Exception as e:
            logger.error(f"Error plotting toggled statistics: {str(e)}", exc_info=True)

    def zoom_in(self):
        """Zoom in on the plot by a factor of 0.8 (reducing the visible range by 20%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (80% of current range)
        x_range = (x_max - x_min) * 0.8
        y_range = (y_max - y_min) * 0.8

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"FWL Odds - Zoomed in")

    def zoom_out(self):
        """Zoom out on the plot by a factor of 1.25 (increasing the visible range by 25%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (125% of current range)
        x_range = (x_max - x_min) * 1.25
        y_range = (y_max - y_min) * 1.25

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"FWL Odds - Zoomed out")

    def reset_zoom(self):
        """Reset the zoom to the initial view state."""
        if self.initial_view_state:
            x_min, x_max, y_min, y_max = self.initial_view_state
            self.plot_widget.setXRange(x_min, x_max)
            self.plot_widget.setYRange(y_min, y_max)

            # Update status
            self.status_label.setText(f"FWL Odds - Zoom reset")
        else:
            # If initial view state is not set, use default values
            self.plot_widget.setXRange(0, 5.0)
            self.plot_widget.setYRange(-10, 10)

            # Update status
            self.status_label.setText(f"FWL Odds - Zoom reset (default view)")

    def on_view_range_changed(self, *_):
        """
        Handle view range changes when zooming.

        Args:
            *_: Arguments passed by the signal (not used directly)
        """
        # This method is called when the view range changes due to zooming
        # We can use it to update any elements that need to be repositioned

        # If we have a crosshair, make sure it's still visible and properly positioned
        if hasattr(self, 'crosshair') and self.crosshair is not None:
            # The crosshair will automatically update on mouse movement
            # But we can force an update if needed by triggering a mouse move event
            pass  # The crosshair will update automatically on next mouse movement

    def on_calculation_mode_changed(self, checked):
        """
        Handle calculation mode change in the data tab.

        Args:
            checked: Whether the button is checked
        """
        if checked:  # Only respond to the button that was checked (not the ones unchecked)
            logger.info("Calculation mode changed in data tab, refreshing FWL odds")
            # Regenerate the FWL odds display with the new calculation mode
            self.generate_fwl_odds()
