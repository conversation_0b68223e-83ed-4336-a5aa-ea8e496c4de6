//@version=6
indicator('Eth\'s Option Zones', overlay = true, max_labels_count = 500)

// Generated on 2025-06-23 18:45
// IV Peak lines from BID prices, IV Walls/Overflow/Max Fear from ASK prices
// Lines extend infinitely and include text labels

// Variables to store labels
var label_array = array.new<label>()

// Clear previous labels to prevent trail
if barstate.islast
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1 by 1
            label.delete(array.get(label_array, i))
        array.clear(label_array)

// IV Peak (Call - Bid) at 596.94
hline(596.94, title = 'IV Peak Call (Bid)', color = color.gray, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 596.94, text = 'IV Peak Call (Bid): 596.94', style = label.style_none, color = color.new(color.white, 100), textcolor = color.gray, size = size.small)
    array.push(label_array, lbl)

// IV Peak (Put - Bid) at 592.55
hline(592.55, title = 'IV Peak Put (Bid)', color = color.gray, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 592.55, text = 'IV Peak Put (Bid): 592.55', style = label.style_none, color = color.new(color.white, 100), textcolor = color.gray, size = size.small)
    array.push(label_array, lbl)

// IV Inner Wall Call (Ask) at 597.66
hline(597.66, title = 'IV Inner Wall Call (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 597.66, text = 'IV Inner Wall Call (Ask): 597.66', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// IV Wall Call (Ask) at 598.05
hline(598.05, title = 'IV Wall Call (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 598.05, text = 'IV Wall Call (Ask): 598.05', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// IV Overflow Call (Ask) at 598.52
hline(598.52, title = 'IV Overflow Call (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 598.52, text = 'IV Overflow Call (Ask): 598.52', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// Max Fear Call (Ask) at 599.76
hline(599.76, title = 'Max Fear Call (Ask)', color = color.red, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 599.76, text = 'Max Fear Call (Ask): 599.76', style = label.style_none, color = color.new(color.white, 100), textcolor = color.red, size = size.small)
    array.push(label_array, lbl)

// IV Inner Wall Put (Ask) at 590.25
hline(590.25, title = 'IV Inner Wall Put (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 590.25, text = 'IV Inner Wall Put (Ask): 590.25', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// IV Wall Put (Ask) at 589.52
hline(589.52, title = 'IV Wall Put (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 589.52, text = 'IV Wall Put (Ask): 589.52', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// IV Overflow Put (Ask) at 588.73
hline(588.73, title = 'IV Overflow Put (Ask)', color = color.blue, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 588.73, text = 'IV Overflow Put (Ask): 588.73', style = label.style_none, color = color.new(color.white, 100), textcolor = color.blue, size = size.small)
    array.push(label_array, lbl)

// Max Fear Put (Ask) at 587.07
hline(587.07, title = 'Max Fear Put (Ask)', color = color.red, linestyle = hline.style_solid)
if barstate.islast
    lbl = label.new(bar_index, 587.07, text = 'Max Fear Put (Ask): 587.07', style = label.style_none, color = color.new(color.white, 100), textcolor = color.red, size = size.small)
    array.push(label_array, lbl)

// Summary: Generated 10 IV levels
// Levels included in this script:
// - Iv Inner Wall Call Ask: 597.66
// - Iv Inner Wall Put Ask: 590.25
// - Iv Overflow Call Ask: 598.52
// - Iv Overflow Put Ask: 588.73
// - Iv Peak Call Bid: 596.94
// - Iv Peak Put Bid: 592.55
// - Iv Wall Call Ask: 598.05
// - Iv Wall Put Ask: 589.52
// - Max Fear Call Ask: 599.76
// - Max Fear Put Ask: 587.07
