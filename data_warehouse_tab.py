"""
Data Warehouse Tab for Main Application

This module provides a comprehensive data warehouse tab that displays all CSV files
from the system's data folder in both candlestick chart format and CSV table format.

Features:
- File discovery and selection from data folder
- PyQtGraph candlestick charts with black background
- CSV table display with sorting and filtering
- Data export functionality (chart images and CSV files)
- Real-time data refresh capabilities
"""

import os
import glob
import pandas as pd
import numpy as np
from datetime import datetime
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
from candlestick_chart import CandlestickItem
from data_tab import OHLCTableModel
from TVscraper import TradingViewScraper, DataFetchWorker

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
        'neutral': '#9E9E9E',              # Material Design Grey
        'highlight': '#FFC107',            # Material Design Amber
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
    }


class DataWarehouseTab(QtWidgets.QWidget):
    """
    Main data warehouse tab widget that provides comprehensive data viewing capabilities.
    
    This tab allows users to:
    - Browse and select data files from the data folder
    - View data in candlestick chart format
    - View data in CSV table format
    - Export charts and data
    """
    
    def __init__(self, parent=None):
        """
        Initialize the data warehouse tab.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Data storage
        self.data_files = {}  # Dictionary mapping file names to file paths
        self.current_data = None  # Currently loaded DataFrame
        self.current_file = None  # Currently selected file name
        
        # Chart items
        self.candlestick_item = None

        # Chart colors (using theme colors)
        self.chart_colors = {
            'background': THEME_COLORS['background'],
            'text': THEME_COLORS['text'],
            'grid': THEME_COLORS['borders'],
            'bullish': THEME_COLORS['bullish'],
            'bearish': THEME_COLORS['bearish'],
            'axis': THEME_COLORS['neutral']
        }

        # Data update worker
        self.update_worker = None
        
        # Initialize UI
        self.init_ui()
        
        # Load available data files
        self.refresh_data_files()
    
    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # Data update panel
        update_panel = self.create_update_panel()
        main_layout.addWidget(update_panel)

        # Create main content area with tab widget
        content_area = self.create_content_area()
        main_layout.addWidget(content_area)

        # Status bar
        self.status_label = QtWidgets.QLabel("Ready - Select a data file to begin")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']}; padding: 5px;")
        main_layout.addWidget(self.status_label)
    
    def create_control_panel(self):
        """Create the control panel with file selection and action buttons."""
        panel = QtWidgets.QGroupBox("Data File Selection")
        panel.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        layout = QtWidgets.QHBoxLayout(panel)
        layout.setContentsMargins(15, 20, 15, 15)
        layout.setSpacing(15)

        # File selection
        file_label = QtWidgets.QLabel("Data File:")
        file_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold;")
        layout.addWidget(file_label)

        self.file_combo = QtWidgets.QComboBox()
        self.file_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                padding: 5px;
                min-width: 250px;
                font-family: 'Consolas', 'Courier New', monospace;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {THEME_COLORS['text']};
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
            }}
        """)
        self.file_combo.currentTextChanged.connect(self.on_file_selected)
        layout.addWidget(self.file_combo)

        layout.addStretch()

        # Action buttons
        self.refresh_btn = QtWidgets.QPushButton("Refresh Files")
        self.refresh_btn.setStyleSheet(self.get_button_style())
        self.refresh_btn.clicked.connect(self.refresh_data_files)
        layout.addWidget(self.refresh_btn)

        return panel

    def create_update_panel(self):
        """Create the data update panel with TradingView scraper integration."""
        panel = QtWidgets.QGroupBox("Data Warehouse Update (TradingView Scraper)")
        panel.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {THEME_COLORS['borders']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        layout = QtWidgets.QHBoxLayout(panel)
        layout.setContentsMargins(15, 20, 15, 15)
        layout.setSpacing(15)

        # Description label
        update_label = QtWidgets.QLabel("Update all CSV files in the data warehouse with latest TradingView data:")
        update_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: normal;")
        layout.addWidget(update_label)

        layout.addStretch()

        # Update all button
        self.update_all_btn = QtWidgets.QPushButton("Update All Data")
        self.update_all_btn.setStyleSheet(self.get_update_button_style())
        self.update_all_btn.clicked.connect(self.update_all_data)
        self.update_all_btn.setEnabled(False)
        layout.addWidget(self.update_all_btn)

        # Progress bar (initially hidden)
        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 3px;
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                text-align: center;
                font-family: 'Consolas', 'Courier New', monospace;
                min-width: 200px;
            }}
            QProgressBar::chunk {{
                background-color: {THEME_COLORS['primary_accent']};
                border-radius: 2px;
            }}
        """)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        return panel

    def get_button_style(self):
        """Get consistent button styling."""
        return f"""
            QPushButton {{
                background-color: white;
                color: black;
                border: 1px solid black;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-family: 'Consolas', 'Courier New', monospace;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: #808080;
            }}
            QPushButton:pressed {{
                background-color: #d0d0d0;
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS['neutral']};
                color: {THEME_COLORS['borders']};
            }}
        """

    def get_update_button_style(self):
        """Get styling for the update all button."""
        return f"""
            QPushButton {{
                background-color: white;
                color: black;
                border: 1px solid black;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Consolas', 'Courier New', monospace;
                min-width: 120px;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: #808080;
            }}
            QPushButton:pressed {{
                background-color: #d0d0d0;
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS['neutral']};
                color: {THEME_COLORS['borders']};
            }}
        """

    def create_content_area(self):
        """Create the main content area with chart and table tabs."""
        # Create a tab widget for Chart and Table views
        self.content_tab_widget = QtWidgets.QTabWidget()
        self.content_tab_widget.setTabsClosable(False)
        self.content_tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS['borders']};
                background-color: {THEME_COLORS['background']};
                border-radius: 0px;
            }}
            QTabBar::tab {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                padding: 8px 20px;
                margin-right: 2px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {THEME_COLORS['primary_accent']};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {THEME_COLORS['secondary_accent']};
                color: white;
            }}
        """)

        # Create chart tab
        chart_widget = self.create_chart_widget()
        self.content_tab_widget.addTab(chart_widget, "Chart View")

        # Create table tab
        table_widget = self.create_table_widget()
        self.content_tab_widget.addTab(table_widget, "Data View")

        return self.content_tab_widget

    def create_chart_widget(self):
        """Create the candlestick chart widget."""
        chart_widget = QtWidgets.QWidget()
        chart_layout = QtWidgets.QVBoxLayout(chart_widget)
        chart_layout.setContentsMargins(10, 10, 10, 10)
        chart_layout.setSpacing(10)

        # Create PyQtGraph plot widget
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground(self.chart_colors['background'])
        self.chart_widget.setLabel('left', 'Price', color=self.chart_colors['text'])
        self.chart_widget.setLabel('bottom', 'Time Index', color=self.chart_colors['text'])

        # Configure chart appearance
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.getAxis('left').setPen(color=self.chart_colors['axis'])
        self.chart_widget.getAxis('bottom').setPen(color=self.chart_colors['axis'])
        self.chart_widget.getAxis('left').setTextPen(color=self.chart_colors['text'])
        self.chart_widget.getAxis('bottom').setTextPen(color=self.chart_colors['text'])

        chart_layout.addWidget(self.chart_widget)
        return chart_widget

    def create_table_widget(self):
        """Create the CSV table widget."""
        table_widget = QtWidgets.QWidget()
        table_layout = QtWidgets.QVBoxLayout(table_widget)
        table_layout.setContentsMargins(10, 10, 10, 10)
        table_layout.setSpacing(10)

        # Create table view
        self.table_view = QtWidgets.QTableView()
        self.table_view.setStyleSheet(f"""
            QTableView {{
                background-color: {THEME_COLORS['background']};
                color: {THEME_COLORS['text']};
                gridline-color: {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
                alternate-background-color: {THEME_COLORS['control_panel']};
                font-family: 'Consolas', 'Courier New', monospace;
            }}
            QHeaderView::section {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                padding: 8px;
                border: 1px solid {THEME_COLORS['borders']};
                font-weight: bold;
                font-family: 'Consolas', 'Courier New', monospace;
            }}
        """)

        # Configure table view
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_view.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.table_view.verticalHeader().setDefaultSectionSize(25)

        # Create and set model
        self.table_model = OHLCTableModel()
        self.table_view.setModel(self.table_model)

        table_layout.addWidget(self.table_view)
        return table_widget

    def refresh_data_files(self):
        """Scan the data folder and refresh the list of available CSV files."""
        try:
            # Clear existing data
            self.data_files.clear()
            self.file_combo.clear()

            # Get data folder path
            data_folder = os.path.join(os.getcwd(), 'data')

            if not os.path.exists(data_folder):
                self.status_label.setText("Error: Data folder not found")
                return

            # Find all CSV files in data folder
            csv_pattern = os.path.join(data_folder, '*.csv')
            csv_files = glob.glob(csv_pattern)

            if not csv_files:
                self.status_label.setText("No CSV files found in data folder")
                return

            # Process each CSV file
            for file_path in sorted(csv_files):
                file_name = os.path.basename(file_path)
                self.data_files[file_name] = file_path
                self.file_combo.addItem(file_name)

            # Enable update all button if files are available
            self.update_all_btn.setEnabled(len(csv_files) > 0)

            self.status_label.setText(f"Found {len(csv_files)} CSV files")

        except Exception as e:
            self.status_label.setText(f"Error scanning data folder: {str(e)}")

    def on_file_selected(self, file_name):
        """Handle file selection from combo box."""
        if not file_name or file_name not in self.data_files:
            return

        try:
            # Load the selected file
            file_path = self.data_files[file_name]
            self.current_file = file_name

            # Read CSV file
            self.current_data = pd.read_csv(file_path)

            # Validate required columns
            required_columns = ['datetime', 'open', 'high', 'low', 'close']
            if not all(col in self.current_data.columns.str.lower() for col in required_columns):
                self.status_label.setText(f"Error: Missing required columns in {file_name}")
                return

            # Standardize column names
            self.current_data.columns = self.current_data.columns.str.lower()

            # Convert datetime column
            self.current_data['datetime'] = pd.to_datetime(self.current_data['datetime'])

            # Update displays
            self.update_chart()
            self.update_table()

            self.status_label.setText(f"Loaded {len(self.current_data)} rows from {file_name}")

        except Exception as e:
            self.status_label.setText(f"Error loading {file_name}: {str(e)}")

    def update_chart(self):
        """Update the candlestick chart with current data."""
        if self.current_data is None or self.current_data.empty:
            return

        try:
            # Clear existing chart items
            self.chart_widget.clear()

            # Prepare OHLC data for candlestick chart
            ohlc_data = []
            for i, row in self.current_data.iterrows():
                ohlc_data.append((i, row['open'], row['high'], row['low'], row['close']))

            # Create candlestick item
            self.candlestick_item = CandlestickItem(
                ohlc_data,
                bullish_color=self.chart_colors['bullish'],
                bearish_color=self.chart_colors['bearish']
            )

            # Add to chart
            self.chart_widget.addItem(self.candlestick_item)

            # Set appropriate view range
            if len(ohlc_data) > 0:
                # Show last 100 candles or all if less than 100
                start_idx = max(0, len(ohlc_data) - 100)
                end_idx = len(ohlc_data)
                self.chart_widget.setXRange(start_idx, end_idx + 5)

                # Set Y range based on visible data
                visible_data = self.current_data.iloc[start_idx:end_idx]
                y_min = min(visible_data['low'].min(), visible_data['open'].min(), visible_data['close'].min())
                y_max = max(visible_data['high'].max(), visible_data['open'].max(), visible_data['close'].max())
                y_padding = (y_max - y_min) * 0.1
                self.chart_widget.setYRange(y_min - y_padding, y_max + y_padding)

        except Exception as e:
            self.status_label.setText(f"Error updating chart: {str(e)}")

    def update_table(self):
        """Update the CSV table with current data."""
        if self.current_data is None or self.current_data.empty:
            return

        try:
            # Prepare data for table display
            display_data = self.current_data.copy()

            # Format datetime column for display
            if 'datetime' in display_data.columns:
                display_data['datetime'] = display_data['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')

            # Rename columns for better display
            column_mapping = {
                'datetime': 'DateTime',
                'symbol': 'Symbol',
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }

            # Only rename columns that exist
            existing_columns = {k: v for k, v in column_mapping.items() if k in display_data.columns}
            display_data = display_data.rename(columns=existing_columns)

            # Update table model
            self.table_model.setData(display_data)

        except Exception as e:
            self.status_label.setText(f"Error updating table: {str(e)}")

    def parse_filename_for_symbol_info(self, filename):
        """Parse filename to extract symbol, exchange, and timeframe information."""
        try:
            # Remove .csv extension
            name_without_ext = filename.replace('.csv', '')

            # Split by underscore
            parts = name_without_ext.split('_')

            if len(parts) >= 4:
                # Format: CME_MINI_ES1!_1D_20250619_201023
                # parts[0] = CME, parts[1] = MINI, parts[2] = ES1!, parts[3] = 1D, parts[4] = timestamp
                exchange_part1 = parts[0]  # CME
                exchange_part2 = parts[1]  # MINI
                symbol = parts[2]          # ES1!
                timeframe = parts[3]       # 1D

                # Combine exchange parts
                exchange = f"{exchange_part1}_{exchange_part2}"  # CME_MINI

                return {
                    'symbol': symbol,
                    'exchange': exchange,
                    'timeframe': timeframe
                }
            elif len(parts) >= 3:
                # Fallback format: EXCHANGE_SYMBOL_TIMEFRAME
                exchange = parts[0]
                symbol = parts[1]
                timeframe = parts[2]

                return {
                    'symbol': symbol,
                    'exchange': exchange,
                    'timeframe': timeframe
                }
            else:
                # Ultimate fallback: try to extract basic info
                return {
                    'symbol': parts[0] if parts else 'UNKNOWN',
                    'exchange': 'CME_MINI',  # Default exchange
                    'timeframe': '1D'  # Default timeframe
                }
        except Exception:
            # Ultimate fallback
            return {
                'symbol': 'UNKNOWN',
                'exchange': 'CME_MINI',
                'timeframe': '1D'
            }

    def update_all_data(self):
        """Update all CSV files in the data warehouse using TradingView scraper."""
        if not self.data_files:
            QtWidgets.QMessageBox.warning(self, "Update Error", "No data files found to update")
            return

        try:
            # Show confirmation dialog
            reply = QtWidgets.QMessageBox.question(
                self,
                "Confirm Data Warehouse Update",
                f"Update all {len(self.data_files)} CSV files with latest data from TradingView?\n\n"
                f"This will fetch the latest data for all symbols and overwrite existing files.\n"
                f"This process may take several minutes to complete.\n\n"
                f"Files to update:\n" + "\n".join([f"• {filename}" for filename in list(self.data_files.keys())[:5]]) +
                (f"\n• ... and {len(self.data_files) - 5} more" if len(self.data_files) > 5 else ""),
                QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No
            )

            if reply == QtWidgets.QMessageBox.StandardButton.Yes:
                self.start_bulk_data_update()

        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Update Error", f"Failed to start update: {str(e)}")

    def start_bulk_data_update(self):
        """Start the bulk data update process for all files."""
        try:
            # Disable update button and show progress
            self.update_all_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(self.data_files))
            self.progress_bar.setValue(0)

            # Initialize bulk update state
            self.bulk_update_files = list(self.data_files.keys())
            self.bulk_update_current_index = 0
            self.bulk_update_results = {
                'success': [],
                'failed': [],
                'total': len(self.bulk_update_files)
            }

            # Update status
            self.status_label.setText(f"Starting bulk update of {len(self.bulk_update_files)} files...")

            # Start updating the first file
            self.update_next_file()

        except Exception as e:
            self.on_update_error(f"Failed to start bulk update: {str(e)}")

    def update_next_file(self):
        """Update the next file in the bulk update queue."""
        if self.bulk_update_current_index >= len(self.bulk_update_files):
            # All files processed
            self.on_bulk_update_complete()
            return

        try:
            # Get current file
            current_file = self.bulk_update_files[self.bulk_update_current_index]
            symbol_info = self.parse_filename_for_symbol_info(current_file)

            # Update progress
            self.progress_bar.setValue(self.bulk_update_current_index)
            self.status_label.setText(
                f"Updating {current_file} ({self.bulk_update_current_index + 1}/{len(self.bulk_update_files)}) - "
                f"{symbol_info['symbol']} {symbol_info['timeframe']}"
            )

            # Create worker thread for data fetching
            self.update_worker = DataFetchWorker(
                symbol=symbol_info['symbol'],
                exchange=symbol_info['exchange'],
                timeframe=symbol_info['timeframe'],
                days_to_load=9999,  # Get maximum available data
                username="",  # Use default credentials
                password=""
            )

            # Connect signals
            self.update_worker.progress.connect(self.on_update_progress)
            self.update_worker.data_ready.connect(lambda data, symbol, exchange, timeframe:
                                                self.on_bulk_update_data_ready(data, symbol, exchange, timeframe, current_file))
            self.update_worker.error.connect(lambda error: self.on_bulk_update_error(error, current_file))
            self.update_worker.finished.connect(self.on_single_update_finished)

            # Start the worker
            self.update_worker.start()

        except Exception as e:
            self.on_bulk_update_error(f"Failed to start update for {current_file}: {str(e)}", current_file)

    def on_update_progress(self, message):
        """Handle update progress messages."""
        self.status_label.setText(f"Update: {message}")

    def on_bulk_update_data_ready(self, data, symbol, exchange, timeframe, filename):
        """Handle successful data update for bulk update."""
        try:
            if data is None or data.empty:
                self.bulk_update_results['failed'].append(f"{filename}: No data received")
            else:
                # Save updated data to the original file
                file_path = self.data_files[filename]

                # Prepare data for saving (ensure proper format)
                save_data = data.copy()
                save_data.reset_index(inplace=True)

                # Ensure column names match expected format
                if 'datetime' not in save_data.columns and save_data.index.name:
                    save_data.rename(columns={save_data.index.name: 'datetime'}, inplace=True)

                # Add symbol column if not present
                if 'symbol' not in save_data.columns:
                    save_data['symbol'] = f"{exchange}:{symbol}"

                # Save to CSV
                save_data.to_csv(file_path, index=False)

                # Record success
                self.bulk_update_results['success'].append(f"{filename}: {len(data)} bars")

                # Update displays if this is the currently selected file
                if self.file_combo.currentText() == filename:
                    self.on_file_selected(filename)

        except Exception as e:
            self.bulk_update_results['failed'].append(f"{filename}: {str(e)}")

    def on_bulk_update_error(self, error_message, filename):
        """Handle bulk update errors for individual files."""
        self.bulk_update_results['failed'].append(f"{filename}: {error_message}")

    def on_single_update_finished(self):
        """Handle completion of a single file update in bulk process."""
        # Clean up current worker
        if self.update_worker:
            self.update_worker.deleteLater()
            self.update_worker = None

        # Move to next file
        self.bulk_update_current_index += 1

        # Continue with next file or complete
        QtCore.QTimer.singleShot(500, self.update_next_file)  # Small delay between updates

    def on_bulk_update_complete(self):
        """Handle completion of bulk update process."""
        # Update progress bar
        self.progress_bar.setValue(len(self.bulk_update_files))

        # Re-enable update button and hide progress after a delay
        QtCore.QTimer.singleShot(2000, self.reset_update_ui)

        # Show completion summary
        success_count = len(self.bulk_update_results['success'])
        failed_count = len(self.bulk_update_results['failed'])

        summary_message = f"Bulk Update Complete!\n\n"
        summary_message += f"Successfully updated: {success_count} files\n"
        summary_message += f"Failed updates: {failed_count} files\n\n"

        if self.bulk_update_results['success']:
            summary_message += "✓ Successful updates:\n"
            for success in self.bulk_update_results['success'][:5]:  # Show first 5
                summary_message += f"  • {success}\n"
            if len(self.bulk_update_results['success']) > 5:
                summary_message += f"  • ... and {len(self.bulk_update_results['success']) - 5} more\n"

        if self.bulk_update_results['failed']:
            summary_message += "\n✗ Failed updates:\n"
            for failure in self.bulk_update_results['failed'][:3]:  # Show first 3
                summary_message += f"  • {failure}\n"
            if len(self.bulk_update_results['failed']) > 3:
                summary_message += f"  • ... and {len(self.bulk_update_results['failed']) - 3} more\n"

        # Update status
        self.status_label.setText(f"Bulk update complete: {success_count} success, {failed_count} failed")

        # Show summary dialog
        QtWidgets.QMessageBox.information(self, "Bulk Update Complete", summary_message)

    def reset_update_ui(self):
        """Reset the update UI after bulk update completion."""
        self.update_all_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

    def on_data_fetched_universal(self, symbol, data):
        """
        Handle data fetched from universal controls (for future integration).

        Args:
            symbol: Symbol name
            data: DataFrame with OHLC data
        """
        # This method can be used for future integration with universal controls
        # For now, we focus on displaying files from the data folder
        pass
