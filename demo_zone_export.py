#!/usr/bin/env python3
"""
Demo script showing the zone export functionality in the candlestick chart.
This script demonstrates how to use the "Export Zones to TV" button.
"""

import sys
import os
from PyQt6 import QtWidgets, QtCore
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from candlestick_chart import CandlestickChart

class ZoneExportDemo:
    """Demo class for zone export functionality"""
    
    def __init__(self):
        self.app = QtWidgets.QApplication(sys.argv)
        self.chart = None
        
    def create_demo_chart(self):
        """Create a demo candlestick chart with sample data and zones"""
        try:
            # Create chart instance
            self.chart = CandlestickChart()
            
            # Set window properties
            self.chart.setWindowTitle("Zone Export Demo - Candlestick Chart")
            self.chart.resize(1200, 800)
            
            # Create sample OHLC data
            dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
            np.random.seed(42)  # For reproducible data
            
            # Generate realistic price data
            base_price = 400.0
            price_changes = np.random.normal(0, 2, 100)
            prices = base_price + np.cumsum(price_changes)
            
            # Create OHLC data
            ohlc_data = []
            for i, price in enumerate(prices):
                high = price + np.random.uniform(0.5, 3.0)
                low = price - np.random.uniform(0.5, 3.0)
                open_price = prices[i-1] if i > 0 else price
                close_price = price
                
                ohlc_data.append({
                    'Date': dates[i],
                    'Open': open_price,
                    'High': high,
                    'Low': low,
                    'Close': close_price,
                    'Volume': np.random.randint(1000000, 5000000)
                })
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlc_data)
            df.set_index('Date', inplace=True)
            
            # Set the data in the chart
            self.chart.data = df
            self.chart.ohlc_data = df
            
            # Set symbol
            self.chart.symbol_input.setText("DEMO")
            
            # Update the chart display
            self.chart.update_chart()
            
            print("Demo chart created with sample data")
            return True
            
        except Exception as e:
            print(f"Error creating demo chart: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def add_sample_zones(self):
        """Add sample zones to demonstrate export functionality"""
        try:
            import pyqtgraph as pg
            
            # Create sample volatility zones
            zone1 = pg.LinearRegionItem(
                values=[395.0, 405.0],
                orientation='horizontal',
                brush=pg.mkBrush(color=(0, 255, 0, 50)),  # Green with transparency
                pen=pg.mkPen(color='green', width=2),
                movable=False
            )
            
            zone2 = pg.LinearRegionItem(
                values=[415.0, 425.0],
                orientation='horizontal',
                brush=pg.mkBrush(color=(255, 0, 0, 50)),  # Red with transparency
                pen=pg.mkPen(color='red', width=2),
                movable=False
            )
            
            zone3 = pg.LinearRegionItem(
                values=[430.0, 440.0],
                orientation='horizontal',
                brush=pg.mkBrush(color=(0, 0, 255, 50)),  # Blue with transparency
                pen=pg.mkPen(color='blue', width=2),
                movable=False
            )
            
            # Add zones to the chart
            self.chart.price_plot.addItem(zone1)
            self.chart.price_plot.addItem(zone2)
            self.chart.price_plot.addItem(zone3)
            
            # Store zones in chart's arrays for export
            self.chart.volatility_zones.extend([zone1, zone2])
            self.chart.ak_density_zones.append(zone3)
            
            print("Added 3 sample zones to the chart:")
            print("- Green support zone: 395.0 - 405.0")
            print("- Red resistance zone: 415.0 - 425.0") 
            print("- Blue density zone: 430.0 - 440.0")
            
            return True
            
        except Exception as e:
            print(f"Error adding sample zones: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def show_instructions(self):
        """Show instructions for using the export feature"""
        instructions = """
        Zone Export Demo Instructions:
        =============================
        
        1. The chart now displays sample price data with 3 colored zones:
           - Green zone (395-405): Support zone
           - Red zone (415-425): Resistance zone  
           - Blue zone (430-440): Density zone
        
        2. To export these zones to TradingView:
           - Click the "Export Zones to TV" button in the control panel
           - A popup dialog will show the Pine Script code
           - Click "Copy to Clipboard" or select all text and copy (Ctrl+A, Ctrl+C)

        3. To use in TradingView:
           - Open TradingView Pine Editor
           - Paste the script and click "Add to Chart"
           - The zones will appear as colored boxes on your TradingView chart
        
        4. The popup dialog will:
           - Show the complete Pine Script code
           - Allow easy copying to clipboard
           - Provide instructions for TradingView import
           - Optionally save to file if needed

        5. The exported zones will:
           - Extend infinitely in both directions
           - Show labels with zone names
           - Use colors matching the original zones
           - Include transparency for better visibility
        
        Click OK to continue to the demo chart.
        """
        
        msg = QtWidgets.QMessageBox()
        msg.setWindowTitle("Zone Export Demo")
        msg.setText(instructions)
        msg.setIcon(QtWidgets.QMessageBox.Icon.Information)
        msg.exec()
    
    def run_demo(self):
        """Run the complete demo"""
        print("Starting Zone Export Demo...")
        
        # Show instructions
        self.show_instructions()
        
        # Create chart with sample data
        if not self.create_demo_chart():
            print("Failed to create demo chart")
            return False
        
        # Add sample zones
        if not self.add_sample_zones():
            print("Failed to add sample zones")
            return False
        
        # Show the chart
        self.chart.show()
        
        print("\nDemo chart is now displayed!")
        print("Look for the 'Export Zones to TV' button in the control panel.")
        print("Click it to see the Pine Script in a popup dialog with copy functionality.")
        
        # Run the application
        return self.app.exec()

def main():
    """Main demo function"""
    demo = ZoneExportDemo()
    return demo.run_demo()

if __name__ == "__main__":
    sys.exit(main())
