"""
Login Page Component for Schwab API Integration

This module provides a main login page component that can be integrated
into the application's main window or used as a standalone widget.
"""

import logging
from typing import Optional
from PyQt6 import QtWidgets, QtCore, QtGui
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGroupBox, QProgressBar, QTextEdit
)

from login_dialog import LoginDialog
from schwab_api import schwab_api
from user_management import get_user_manager

logger = logging.getLogger(__name__)

class LoginPage(QWidget):
    """
    Main login page component for Schwab API integration.
    Can be embedded in the main application or used standalone.
    """

    # Signals
    login_successful = pyqtSignal()
    login_failed = pyqtSignal(str)
    logout_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_manager = get_user_manager()
        self.login_dialog = None

        self.init_ui()
        self.connect_signals()
        self.update_ui_state()

        # Apply theme
        self.apply_theme()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Title section
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)

        title_label = QLabel("Schwab API Integration")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("Connect to Charles Schwab for live market data")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("font-size: 14px; color: #666; margin-bottom: 20px;")
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_frame)

        # Status section
        self.status_group = QGroupBox("Connection Status")
        status_layout = QVBoxLayout(self.status_group)

        # Connection status indicator
        status_indicator_layout = QHBoxLayout()

        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet("color: red; font-size: 20px;")
        status_indicator_layout.addWidget(self.status_indicator)

        self.status_text = QLabel("Not connected")
        self.status_text.setStyleSheet("font-weight: bold;")
        status_indicator_layout.addWidget(self.status_text)

        status_indicator_layout.addStretch()
        status_layout.addLayout(status_indicator_layout)

        # User info
        self.user_info_label = QLabel("No user logged in")
        self.user_info_label.setStyleSheet("color: #666; margin: 5px 0;")
        status_layout.addWidget(self.user_info_label)

        # Progress bar for operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        layout.addWidget(self.status_group)

        # Action buttons section
        self.actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(self.actions_group)

        # Login button
        self.login_btn = QPushButton("Connect to Schwab API")
        self.login_btn.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.login_btn.clicked.connect(self.show_login_dialog)
        actions_layout.addWidget(self.login_btn)

        # Logout button
        self.logout_btn = QPushButton("Disconnect")
        self.logout_btn.setMinimumHeight(35)
        self.logout_btn.clicked.connect(self.logout)
        self.logout_btn.setVisible(False)
        actions_layout.addWidget(self.logout_btn)

        # Test connection button
        self.test_btn = QPushButton("Test Connection")
        self.test_btn.clicked.connect(self.test_connection)
        self.test_btn.setVisible(False)
        actions_layout.addWidget(self.test_btn)

        layout.addWidget(self.actions_group)

        # Information section
        info_group = QGroupBox("Information")
        info_layout = QVBoxLayout(info_group)

        info_text = QTextEdit()
        info_text.setMaximumHeight(150)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <b>Schwab API Benefits:</b><br>
        • Real-time market data<br>
        • Live options chains<br>
        • Account information access<br>
        • Professional-grade data quality<br>
        • Direct broker integration<br><br>

        <b>Requirements:</b><br>
        • Schwab developer account<br>
        • Approved application<br>
        • Valid API credentials
        """)
        info_layout.addWidget(info_text)

        layout.addWidget(info_group)

        # Add stretch to push everything to top
        layout.addStretch()

    def apply_theme(self):
        """Apply consistent theming"""
        try:
            # Try to import theme colors from the main application
            import theme
            colors = theme.DEFAULT

            self.setStyleSheet(f"""
                QWidget {{
                    background-color: {colors['background']};
                    color: {colors['text']};
                }}
                QGroupBox {{
                    font-weight: bold;
                    border: 2px solid {colors['borders']};
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 15px;
                }}
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    left: 15px;
                    padding: 0 8px 0 8px;
                }}
                QPushButton {{
                    padding: 10px 20px;
                    border: 2px solid {colors['borders']};
                    border-radius: 6px;
                    background-color: {colors['control_panel']};
                    color: {colors['text']};
                }}
                QPushButton:hover {{
                    background-color: {colors['primary_accent']};
                    border-color: {colors['primary_accent']};
                }}
                QPushButton:pressed {{
                    background-color: {colors['pressed_accent']};
                }}
                QPushButton:disabled {{
                    background-color: {colors['borders']};
                    color: #888;
                    border-color: #555;
                }}
                QTextEdit {{
                    border: 1px solid {colors['borders']};
                    border-radius: 4px;
                    background-color: {colors['control_panel']};
                    color: {colors['text']};
                    padding: 10px;
                }}
            """)
        except ImportError:
            # Fallback styling if theme module not available
            pass

    def connect_signals(self):
        """Connect internal signals"""
        # Connect to Schwab API signals
        schwab_api.connection_status_changed.connect(self.on_connection_status_changed)
        schwab_api.error_occurred.connect(self.on_api_error)

        # Connect to user manager signals
        self.user_manager.login_successful.connect(self.on_login_successful)
        self.user_manager.login_failed.connect(self.on_login_failed)
        self.user_manager.logout_completed.connect(self.on_logout_completed)

    def show_login_dialog(self):
        """Show the login dialog"""
        if self.login_dialog is None:
            self.login_dialog = LoginDialog(self)
            self.login_dialog.authentication_successful.connect(self.on_authentication_credentials)
            self.login_dialog.authentication_failed.connect(self.on_authentication_failed)
            self.login_dialog.skip_oauth_requested.connect(self.on_skip_oauth_credentials)

        self.login_dialog.show()

    def on_authentication_credentials(self, api_key: str, app_secret: str, callback_url: str):
        """Handle authentication credentials from dialog"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.login_btn.setEnabled(False)

        # Start authentication process
        self.user_manager.login(api_key, app_secret, callback_url)

    def on_skip_oauth_credentials(self, api_key: str, app_secret: str, callback_url: str):
        """Handle skip OAuth credentials from dialog"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.login_btn.setEnabled(False)

        # Start skip OAuth login process
        self.user_manager.skip_oauth_login(api_key, app_secret, callback_url)

    def on_authentication_failed(self, error_message: str):
        """Handle authentication failure"""
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)

        if self.login_dialog:
            self.login_dialog.authentication_completed(False, error_message)

        self.login_failed.emit(error_message)

    def on_login_successful(self):
        """Handle successful login"""
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)

        if self.login_dialog:
            self.login_dialog.authentication_completed(True)

        self.update_ui_state()
        self.login_successful.emit()

        logger.info("Schwab API login successful")

    def on_login_failed(self, error_message: str):
        """Handle login failure"""
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)

        if self.login_dialog:
            self.login_dialog.authentication_completed(False, error_message)

        self.login_failed.emit(error_message)

        logger.error(f"Schwab API login failed: {error_message}")

    def on_logout_completed(self):
        """Handle logout completion"""
        self.update_ui_state()
        self.logout_requested.emit()

        logger.info("Schwab API logout completed")

    def on_connection_status_changed(self, connected: bool):
        """Handle connection status changes"""
        self.update_ui_state()

        if connected:
            self.status_indicator.setStyleSheet("color: green; font-size: 20px;")
            self.status_text.setText("Connected")
        else:
            self.status_indicator.setStyleSheet("color: red; font-size: 20px;")
            self.status_text.setText("Not connected")

    def on_api_error(self, error_message: str):
        """Handle API errors"""
        logger.error(f"Schwab API error: {error_message}")

        # Show error in status
        self.status_text.setText(f"Error: {error_message}")
        self.status_indicator.setStyleSheet("color: orange; font-size: 20px;")

    def test_connection(self):
        """Test the API connection"""
        if not schwab_api.is_connected():
            self.status_text.setText("Not connected - cannot test")
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.test_btn.setEnabled(False)

        # Test with the improved connection test method
        try:
            connection_ok = schwab_api.test_connection()
            if connection_ok:
                self.status_text.setText("Connection test successful - market data access confirmed")
                self.status_indicator.setStyleSheet("color: green; font-size: 20px;")
            else:
                self.status_text.setText("Connection test failed - check credentials and permissions")
                self.status_indicator.setStyleSheet("color: orange; font-size: 20px;")
        except Exception as e:
            self.status_text.setText(f"Test failed: {str(e)}")
            self.status_indicator.setStyleSheet("color: red; font-size: 20px;")

        self.progress_bar.setVisible(False)
        self.test_btn.setEnabled(True)

    def logout(self):
        """Logout from Schwab API"""
        self.user_manager.logout()

    def update_ui_state(self):
        """Update UI based on current state"""
        is_connected = schwab_api.is_connected()
        current_user = self.user_manager.get_current_user()

        # Update button visibility
        self.login_btn.setVisible(not is_connected)
        self.logout_btn.setVisible(is_connected)
        self.test_btn.setVisible(is_connected)

        # Update user info
        if current_user:
            self.user_info_label.setText(f"Logged in with API key: {current_user.get('api_key_preview', 'Unknown')}")
        else:
            self.user_info_label.setText("No user logged in")

        # Update connection status
        if is_connected:
            self.status_indicator.setStyleSheet("color: green; font-size: 20px;")
            self.status_text.setText("Connected to Schwab API")
        else:
            self.status_indicator.setStyleSheet("color: red; font-size: 20px;")
            self.status_text.setText("Not connected")

    def is_logged_in(self) -> bool:
        """Check if user is logged in"""
        return schwab_api.is_connected()

    def get_connection_status(self) -> dict:
        """Get current connection status information"""
        return {
            'connected': schwab_api.is_connected(),
            'user': self.user_manager.get_current_user(),
            'api_available': True
        }
