"""
State-of-the-Art Loading Screen Components for Market Odds and Options Analyzer

This module provides modern, animated loading screen overlays with glassmorphism effects,
particle systems, and advanced animations that can be displayed during chart loading
and other time-consuming operations.
"""

import os
import json
import subprocess
import time
import sys
import atexit
import math
import random
from PyQt6 import QtWidgets, QtCore, QtGui

class Particle:
    """A single particle for the particle system animation."""

    def __init__(self, x, y, vx, vy, size, color, life_span=3000):
        self.x = x
        self.y = y
        self.vx = vx  # velocity x
        self.vy = vy  # velocity y
        self.size = size
        self.color = color
        self.life_span = life_span  # in milliseconds
        self.age = 0
        self.opacity = 1.0

    def update(self, dt):
        """Update particle position and properties."""
        self.x += self.vx * dt
        self.y += self.vy * dt
        self.age += dt

        # Fade out over time
        self.opacity = max(0, 1.0 - (self.age / self.life_span))

        # Slow down over time
        self.vx *= 0.99
        self.vy *= 0.99

    def is_alive(self):
        """Check if particle is still alive."""
        return self.age < self.life_span and self.opacity > 0.01

class ParticleSystem(QtWidgets.QWidget):
    """Advanced particle system for modern loading animations."""

    def __init__(self, size=200, primary_color=None, secondary_color=None):
        super().__init__()

        self.size = size
        self.primary_color = primary_color or QtGui.QColor("#007acc")
        self.secondary_color = secondary_color or QtGui.QColor("#4CAF50")
        self.accent_color = QtGui.QColor("#ff6b6b")

        self.setFixedSize(size, size)

        # Particle system
        self.particles = []
        self.max_particles = 50

        # Animation properties
        self.animation_time = 0
        self.last_update = time.time()

        # Timer for updates
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_particles)
        self.animation_timer.start(16)  # ~60 FPS

    def create_particle(self):
        """Create a new particle."""
        center_x = self.size / 2
        center_y = self.size / 2

        # Random position around center
        angle = random.uniform(0, 2 * math.pi)
        radius = random.uniform(10, 40)
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)

        # Random velocity
        vx = random.uniform(-30, 30)
        vy = random.uniform(-30, 30)

        # Random size and color
        size = random.uniform(2, 6)
        colors = [self.primary_color, self.secondary_color, self.accent_color]
        color = random.choice(colors)

        # Random lifespan
        life_span = random.uniform(2000, 4000)

        return Particle(x, y, vx, vy, size, color, life_span)

    def update_particles(self):
        """Update all particles."""
        current_time = time.time()
        dt = (current_time - self.last_update) * 1000  # Convert to milliseconds
        self.last_update = current_time
        self.animation_time += dt

        # Update existing particles
        self.particles = [p for p in self.particles if p.is_alive()]
        for particle in self.particles:
            particle.update(dt)

        # Create new particles
        while len(self.particles) < self.max_particles:
            if random.random() < 0.3:  # 30% chance per frame
                self.particles.append(self.create_particle())

        self.update()

    def paintEvent(self, _event):
        """Paint the particle system."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Draw particles
        for particle in self.particles:
            color = QtGui.QColor(particle.color)
            color.setAlphaF(particle.opacity)

            painter.setBrush(color)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)

            painter.drawEllipse(
                QtCore.QPointF(particle.x, particle.y),
                particle.size, particle.size
            )

        # Draw central glow effect
        center = QtCore.QPointF(self.size / 2, self.size / 2)
        glow_radius = 30 + 10 * math.sin(self.animation_time / 500)

        gradient = QtGui.QRadialGradient(center, glow_radius)
        glow_color = QtGui.QColor(self.primary_color)
        glow_color.setAlphaF(0.3)
        gradient.setColorAt(0, glow_color)
        glow_color.setAlphaF(0)
        gradient.setColorAt(1, glow_color)

        painter.setBrush(gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(center, glow_radius, glow_radius)

class DataDrivenIcon(QtWidgets.QWidget):
    """
    A widget that draws a data-driven icon.
    """

    def __init__(self, size=64, primary_color=None, secondary_color=None):
        """
        Initialize the data-driven icon.

        Args:
            size: Size of the icon in pixels
            primary_color: Primary color for the icon (QColor)
            secondary_color: Secondary color for the icon (QColor)
        """
        super().__init__()

        # Store parameters
        self.size = size
        self.primary_color = primary_color or QtGui.QColor("#007acc")  # Blue
        self.secondary_color = secondary_color or QtGui.QColor("#4CAF50")  # Green

        # Set fixed size
        self.setFixedSize(size, size)

        # Animation properties
        self.animation_offset = 0
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)  # Update every 50ms

    def update_animation(self):
        """Update the animation offset."""
        self.animation_offset = (self.animation_offset + 5) % 360
        self.update()

    def paintEvent(self, _event):
        """Custom paint event to draw the data-driven icon."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Calculate center and radius
        center = QtCore.QPointF(self.size / 2, self.size / 2)
        radius = self.size / 2 - 4  # Slightly smaller than widget

        # Draw outer circle
        painter.setPen(QtGui.QPen(self.primary_color, 3))
        painter.drawEllipse(center, radius, radius)

        # Draw animated data points
        painter.setPen(QtGui.QPen(self.secondary_color, 2))
        painter.setBrush(self.secondary_color)

        # Draw 8 data points around the circle
        for i in range(8):
            angle = (i * 45 + self.animation_offset) % 360
            rad_angle = angle * 3.14159 / 180
            x = center.x() + radius * 0.7 * math.cos(rad_angle)
            y = center.y() + radius * 0.7 * math.sin(rad_angle)

            # Draw data point
            point_size = 4 + 2 * math.sin((angle + self.animation_offset) * 3.14159 / 180)
            painter.drawEllipse(QtCore.QPointF(x, y), point_size, point_size)

        # Draw connecting lines
        painter.setPen(QtGui.QPen(self.primary_color.lighter(150), 1))
        for i in range(8):
            angle1 = (i * 45 + self.animation_offset) % 360
            angle2 = ((i + 1) * 45 + self.animation_offset) % 360
            rad_angle1 = angle1 * 3.14159 / 180
            rad_angle2 = angle2 * 3.14159 / 180

            x1 = center.x() + radius * 0.7 * math.cos(rad_angle1)
            y1 = center.y() + radius * 0.7 * math.sin(rad_angle1)
            x2 = center.x() + radius * 0.7 * math.cos(rad_angle2)
            y2 = center.y() + radius * 0.7 * math.sin(rad_angle2)

            painter.drawLine(QtCore.QPointF(x1, y1), QtCore.QPointF(x2, y2))

class LoadingScreen(QtWidgets.QWidget):
    """
    A state-of-the-art loading screen with glassmorphism effects, particle systems,
    and advanced animations.

    This class provides a modern loading screen that can be displayed over any widget
    to indicate that a time-consuming operation is in progress.
    """

    def __init__(self, parent=None, message="Loading...", theme_colors=None, use_data_driven_icon=False,
                 loading_style="modern"):
        """
        Initialize the loading screen.

        Args:
            parent: Parent widget
            message: Message to display
            theme_colors: Dictionary of theme colors
            use_data_driven_icon: Whether to use the data-driven icon instead of the spinner
            loading_style: Style of loading animation ("modern", "particles", "glassmorphism", "minimal")
        """
        super().__init__(parent)

        # Store parameters
        self.parent = parent
        self.message = message
        self.use_data_driven_icon = use_data_driven_icon
        self.loading_style = loading_style

        # Use provided theme colors or defaults
        self.theme_colors = theme_colors or {
            'background': '#1e1e1e',
            'text': '#E0E0E0',
            'primary_accent': '#007acc',
            'glass_bg': 'rgba(255, 255, 255, 0.1)',
            'glass_border': 'rgba(255, 255, 255, 0.2)',
            'glow': '#007acc'
        }

        # Animation properties
        self.angle = 0
        self.pulse_phase = 0
        self.glow_intensity = 0.5
        self.animation_time = 0
        self.last_frame_time = time.time()

        # Glassmorphism properties
        self.glass_opacity = 0.0
        self.blur_radius = 0

        # Set up the UI
        self.init_ui()

        # High-performance animation timer (60 FPS)
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(16)  # 16ms = ~60 FPS

        # Fade-in animation
        self.fade_in_animation = QtCore.QPropertyAnimation(self, b"windowOpacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QtCore.QEasingCurve.Type.OutCubic)

        # Hide initially
        self.hide()

    def init_ui(self):
        """Initialize the modern user interface with glassmorphism effects."""
        # Set up size
        if self.parent:
            self.setGeometry(self.parent.rect())
            self.parent.installEventFilter(self)

        # Set up appearance for glassmorphism
        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowFlags(QtCore.Qt.WindowType.FramelessWindowHint)

        # Create layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create glassmorphism container
        self.container = QtWidgets.QWidget()
        self.container.setObjectName("loading_container")

        # Set container size based on loading style
        if self.loading_style == "particles":
            self.container.setFixedSize(350, 300)
        elif self.loading_style == "glassmorphism":
            self.container.setFixedSize(400, 280)
        elif self.use_data_driven_icon:
            self.container.setFixedSize(250, 220)
        else:
            self.container.setFixedSize(320, 280)

        # Modern glassmorphism styling
        self.container.setStyleSheet(f"""
            #loading_container {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.5 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.12));
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.25);
                backdrop-filter: blur(10px);
            }}
        """)

        # Container layout
        container_layout = QtWidgets.QVBoxLayout(self.container)
        container_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        container_layout.setSpacing(25)

        # Create animation widget based on style
        if self.loading_style == "particles":
            # Create particle system
            primary_color = QtGui.QColor(self.theme_colors['primary_accent'])
            secondary_color = QtGui.QColor(self.theme_colors.get('bullish', '#4CAF50'))
            self.animation_widget = ParticleSystem(size=200, primary_color=primary_color, secondary_color=secondary_color)
            container_layout.addWidget(self.animation_widget, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        elif self.use_data_driven_icon:
            # Create enhanced data-driven icon
            primary_color = QtGui.QColor(self.theme_colors['primary_accent'])
            secondary_color = QtGui.QColor(self.theme_colors.get('bullish', '#4CAF50'))
            self.animation_widget = DataDrivenIcon(size=120, primary_color=primary_color, secondary_color=secondary_color)
            container_layout.addWidget(self.animation_widget, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        else:
            # Modern spinner widget
            self.animation_widget = QtWidgets.QWidget()
            self.animation_widget.setFixedSize(140, 140)
            container_layout.addWidget(self.animation_widget, 0, QtCore.Qt.AlignmentFlag.AlignCenter)

        # Enhanced message label with glow effect
        self.message_label = QtWidgets.QLabel(self.message)
        self.message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.message_label.setStyleSheet(f"""
            color: {self.theme_colors['text']};
            font-size: 18px;
            font-weight: 600;
            font-family: 'Segoe UI', Arial, sans-serif;
            text-shadow: 0 0 10px rgba(0, 122, 204, 0.5);
            margin: 10px;
        """)
        container_layout.addWidget(self.message_label)

        # Add subtle progress indicator dots
        self.progress_dots = QtWidgets.QLabel("●●●")
        self.progress_dots.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.progress_dots.setStyleSheet(f"""
            color: {self.theme_colors['primary_accent']};
            font-size: 24px;
            margin: 5px;
        """)
        container_layout.addWidget(self.progress_dots)

        # Add container to main layout
        layout.addWidget(self.container, 0, QtCore.Qt.AlignmentFlag.AlignCenter)

    def eventFilter(self, obj, event):
        """Handle parent resize events."""
        if obj == self.parent and event.type() == QtCore.QEvent.Type.Resize:
            self.adjust_size(event)
        return super().eventFilter(obj, event)

    def adjust_size(self, event):
        """Adjust size when parent is resized."""
        if self.parent:
            self.setGeometry(self.parent.rect())
        if event:
            event.accept()

    def set_message(self, message):
        """Update the loading message with smooth animation."""
        self.set_message_with_animation(message)

    def update_animation(self):
        """Update the advanced animations with smooth timing."""
        current_time = time.time()
        dt = current_time - self.last_frame_time
        self.last_frame_time = current_time
        self.animation_time += dt * 1000  # Convert to milliseconds

        # Update rotation
        self.angle = (self.angle + 8) % 360

        # Update pulse phase for glow effects
        self.pulse_phase = (self.pulse_phase + dt * 2) % (2 * math.pi)
        self.glow_intensity = 0.3 + 0.4 * (math.sin(self.pulse_phase) + 1) / 2

        # Update glassmorphism opacity
        if self.isVisible():
            self.glass_opacity = min(1.0, self.glass_opacity + dt * 3)
            self.blur_radius = min(15, self.blur_radius + dt * 30)

        # Animate progress dots
        dot_phase = int(self.animation_time / 300) % 4
        if dot_phase == 0:
            self.progress_dots.setText("●○○")
        elif dot_phase == 1:
            self.progress_dots.setText("○●○")
        elif dot_phase == 2:
            self.progress_dots.setText("○○●")
        else:
            self.progress_dots.setText("●●●")

        # Update animation widget
        if hasattr(self, 'animation_widget'):
            self.animation_widget.update()

        # Update the entire widget
        self.update()

    def paintEvent(self, _event):
        """Custom paint event with advanced glassmorphism and modern effects."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QtGui.QPainter.RenderHint.SmoothPixmapTransform)

        # Draw sophisticated background with gradient
        bg_gradient = QtGui.QLinearGradient(0, 0, self.width(), self.height())
        bg_gradient.setColorAt(0, QtGui.QColor(0, 0, 0, 160))
        bg_gradient.setColorAt(0.5, QtGui.QColor(20, 20, 30, 180))
        bg_gradient.setColorAt(1, QtGui.QColor(0, 0, 0, 160))
        painter.fillRect(self.rect(), bg_gradient)

        # Draw modern spinner if not using other animation widgets
        if (not self.use_data_driven_icon and self.loading_style not in ["particles"]
            and hasattr(self, 'animation_widget') and self.animation_widget.isVisible()):

            self.draw_modern_spinner(painter)

        # Add subtle ambient glow around container
        if hasattr(self, 'container'):
            self.draw_ambient_glow(painter)

    def draw_modern_spinner(self, painter):
        """Draw a modern, sophisticated spinner animation."""
        if not hasattr(self, 'animation_widget'):
            return

        # Get spinner widget geometry
        spinner_rect = self.animation_widget.geometry()
        center = QtCore.QPointF(
            spinner_rect.x() + spinner_rect.width() / 2,
            spinner_rect.y() + spinner_rect.height() / 2
        )

        painter.save()
        painter.translate(center)

        # Draw multiple rotating rings with different speeds
        for ring in range(3):
            painter.save()

            ring_angle = (self.angle * (1 + ring * 0.3)) % 360
            painter.rotate(ring_angle)

            radius = 35 + ring * 15
            thickness = 4 - ring

            # Create gradient for each ring
            gradient = QtGui.QConicalGradient(0, 0, -ring_angle)

            primary = QtGui.QColor(self.theme_colors['primary_accent'])
            primary.setAlphaF(0.8 - ring * 0.2)

            transparent = QtGui.QColor(primary)
            transparent.setAlphaF(0)

            gradient.setColorAt(0, transparent)
            gradient.setColorAt(0.3, primary)
            gradient.setColorAt(0.7, primary.lighter(120))
            gradient.setColorAt(1, transparent)

            pen = QtGui.QPen(QtGui.QBrush(gradient), thickness)
            pen.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
            painter.setPen(pen)

            # Draw arc with glow effect
            arc_length = 280 - ring * 40
            painter.drawArc(-radius, -radius, radius * 2, radius * 2, 0, arc_length * 16)

            painter.restore()

        painter.restore()

    def draw_ambient_glow(self, painter):
        """Draw ambient glow effect around the container."""
        if not hasattr(self, 'container'):
            return

        container_rect = self.container.geometry()
        center = QtCore.QPointF(
            container_rect.x() + container_rect.width() / 2,
            container_rect.y() + container_rect.height() / 2
        )

        # Create radial gradient for glow
        glow_radius = max(container_rect.width(), container_rect.height()) * 0.8
        gradient = QtGui.QRadialGradient(center, glow_radius)

        glow_color = QtGui.QColor(self.theme_colors['primary_accent'])
        glow_color.setAlphaF(self.glow_intensity * 0.15)
        gradient.setColorAt(0, glow_color)

        glow_color.setAlphaF(0)
        gradient.setColorAt(1, glow_color)

        painter.setBrush(gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawEllipse(center, glow_radius, glow_radius)

    def show(self):
        """Show the loading screen with smooth fade-in animation."""
        super().show()
        self.fade_in_animation.start()

    def hide_with_animation(self):
        """Hide the loading screen with smooth fade-out animation."""
        fade_out = QtCore.QPropertyAnimation(self, b"windowOpacity")
        fade_out.setDuration(200)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QtCore.QEasingCurve.Type.InCubic)
        fade_out.finished.connect(self.hide)
        fade_out.start()

        # Store reference to prevent garbage collection
        self._fade_out_animation = fade_out

    def showEvent(self, event):
        """Start animation when shown with enhanced initialization."""
        self.animation_timer.start()
        self.last_frame_time = time.time()
        self.animation_time = 0
        self.glass_opacity = 0.0
        self.blur_radius = 0
        super().showEvent(event)

    def hideEvent(self, event):
        """Stop animation when hidden."""
        self.animation_timer.stop()
        super().hideEvent(event)

    def set_loading_style(self, style):
        """Change the loading animation style dynamically."""
        if style != self.loading_style:
            self.loading_style = style
            # Reinitialize UI with new style
            self.init_ui()

    def set_message_with_animation(self, message):
        """Update message with smooth transition animation."""
        self.message = message

        # Create fade animation for message
        fade_out = QtCore.QPropertyAnimation(self.message_label, b"windowOpacity")
        fade_out.setDuration(150)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)

        def update_text():
            self.message_label.setText(message)
            fade_in = QtCore.QPropertyAnimation(self.message_label, b"windowOpacity")
            fade_in.setDuration(150)
            fade_in.setStartValue(0.0)
            fade_in.setEndValue(1.0)
            fade_in.start()
            # Store reference
            self._fade_in_animation = fade_in

        fade_out.finished.connect(update_text)
        fade_out.start()
        # Store reference
        self._fade_out_animation = fade_out


class SkeletonLoadingScreen(QtWidgets.QWidget):
    """
    Modern skeleton loading screen that shows content placeholders
    while data is being fetched.
    """

    def __init__(self, parent=None, theme_colors=None):
        super().__init__(parent)

        self.parent = parent
        self.theme_colors = theme_colors or {
            'skeleton_bg': '#2d2d2d',
            'skeleton_highlight': '#404040',
            'background': '#1e1e1e'
        }

        # Animation properties
        self.shimmer_position = 0
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_shimmer)
        self.animation_timer.start(50)

        self.setup_ui()

    def setup_ui(self):
        """Set up the skeleton UI."""
        if self.parent:
            self.setGeometry(self.parent.rect())

        self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)

        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)

        # Create skeleton elements
        for i in range(5):
            skeleton_bar = QtWidgets.QWidget()
            height = 60 if i == 0 else 40  # First bar is taller (title)
            skeleton_bar.setFixedHeight(height)
            skeleton_bar.setStyleSheet(f"""
                background-color: {self.theme_colors['skeleton_bg']};
                border-radius: {height // 4}px;
            """)
            layout.addWidget(skeleton_bar)

        layout.addStretch()

    def update_shimmer(self):
        """Update the shimmer animation."""
        self.shimmer_position = (self.shimmer_position + 5) % (self.width() + 200)
        self.update()

    def paintEvent(self, _event):
        """Paint the skeleton with shimmer effect."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Draw background
        painter.fillRect(self.rect(), QtGui.QColor(self.theme_colors['background']))

        # Draw shimmer effect
        shimmer_gradient = QtGui.QLinearGradient(
            self.shimmer_position - 100, 0,
            self.shimmer_position + 100, 0
        )

        transparent = QtGui.QColor(255, 255, 255, 0)
        highlight = QtGui.QColor(255, 255, 255, 30)

        shimmer_gradient.setColorAt(0, transparent)
        shimmer_gradient.setColorAt(0.5, highlight)
        shimmer_gradient.setColorAt(1, transparent)

        painter.setBrush(shimmer_gradient)
        painter.setPen(QtCore.Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())


def create_loading_screen(parent=None, message="Loading...", style="modern", theme_colors=None):
    """
    Factory function to create different types of loading screens.

    Args:
        parent: Parent widget
        message: Loading message
        style: Loading style ("modern", "particles", "glassmorphism", "skeleton", "data_driven")
        theme_colors: Theme color dictionary

    Returns:
        LoadingScreen instance configured with the specified style
    """
    if style == "skeleton":
        return SkeletonLoadingScreen(parent, theme_colors)
    elif style == "data_driven":
        return LoadingScreen(parent, message, theme_colors, use_data_driven_icon=True, loading_style="modern")
    elif style in ["modern", "particles", "glassmorphism"]:
        return LoadingScreen(parent, message, theme_colors, use_data_driven_icon=False, loading_style=style)
    else:
        # Default to modern style
        return LoadingScreen(parent, message, theme_colors, use_data_driven_icon=False, loading_style="modern")


class LoadingScreenManager:
    """
    Utility class to manage the standalone loading screen process.
    """

    def __init__(self, use_background_mode=True):
        """Initialize the loading screen manager.

        Args:
            use_background_mode: If True, disable external loading screens entirely
        """
        self.process = None
        self.status_file = "loading_status.json"
        self.use_background_mode = use_background_mode

        # Register cleanup function
        atexit.register(self.cleanup)

    def start(self, message="Loading chart data...", position=None, size=None):
        """
        Start the loading screen process.

        Args:
            message: Message to display
            position: Position of the window (x, y)
            size: Size of the window (width, height)
        """
        # If background mode is enabled, skip external loading screen
        if self.use_background_mode:
            return True

        # Create status file
        self._update_status("loading", message)

        # Build command
        cmd = [sys.executable, "standalone_loading_screen.py"]

        # Add message
        cmd.extend(["--message", message])

        # Add position if provided
        if position:
            cmd.extend(["--position", str(position[0]), str(position[1])])

        # Add size if provided
        if size:
            # Use provided size without minimum restrictions
            width, height = size
            cmd.extend(["--size", str(width), str(height)])

        # Start process
        try:
            # Use comprehensive flags to hide the process completely
            creation_flags = (
                subprocess.DETACHED_PROCESS |           # Run independently
                subprocess.CREATE_NEW_PROCESS_GROUP |   # New process group
                subprocess.CREATE_NO_WINDOW |           # No console window
                0x08000000                              # CREATE_NO_WINDOW (additional flag)
            )

            self.process = subprocess.Popen(
                cmd,
                creationflags=creation_flags,
                stdout=subprocess.DEVNULL,              # Redirect stdout to null
                stderr=subprocess.DEVNULL,              # Redirect stderr to null
                stdin=subprocess.DEVNULL                # Redirect stdin to null
            )

            # Give the process time to start
            time.sleep(0.1)

            return True
        except Exception as e:
            print(f"Error starting loading screen: {str(e)}")
            return False

    def update_message(self, message):
        """
        Update the loading message.

        Args:
            message: New message to display
        """
        # If background mode is enabled, skip updating external loading screen
        if self.use_background_mode:
            return

        self._update_status("loading", message)

    def stop(self):
        """Stop the loading screen process."""
        # If background mode is enabled, skip stopping external loading screen
        if self.use_background_mode:
            return

        # Update status file to indicate loading is complete
        self._update_status("complete")

        # Give the process time to read the status file and close itself
        time.sleep(0.2)

        # If process is still running, terminate it
        if self.process and self.process.poll() is None:
            try:
                # Since we're on Windows, use Windows-specific termination
                self.process.terminate()
            except:
                pass

        self.process = None

    def _update_status(self, status, message=None):
        """
        Update the status file.

        Args:
            status: Status to set ('loading' or 'complete')
            message: Message to display (optional)
        """
        try:
            data = {"status": status}
            if message:
                data["message"] = message

            with open(self.status_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            print(f"Error updating status file: {str(e)}")

    def cleanup(self):
        """Clean up resources."""
        # Stop the loading screen if it's running
        self.stop()

        # Remove the status file
        if os.path.exists(self.status_file):
            try:
                os.remove(self.status_file)
            except:
                pass
