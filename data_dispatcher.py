"""
Data Dispatcher Module

This module provides a centralized mechanism for dispatching data updates
to various components of the application, reducing redundant data fetches
and improving performance. Supports TradingView scraper for OHLCV data,
Yahoo Finance for options data, and Schwab API for live quotes.
"""

from PyQt6 import QtCore
import pandas as pd
import threading
import yfinance as yf
import time
import logging
from data_cache import DataCache
from ticker_converter import ticker_converter

# Import Schwab API for price and options data
try:
    from schwab_api import schwab_api
    SCHWAB_AVAILABLE = True
except ImportError:
    SCHWAB_AVAILABLE = False
    schwab_api = None

# Import TradingView scraper for OHLCV data
try:
    from TVscraper import TradingViewScraper, Config
    TRADINGVIEW_AVAILABLE = True
except ImportError:
    TRADINGVIEW_AVAILABLE = False
    TradingViewScraper = None
    Config = None

logger = logging.getLogger(__name__)

class DataFetchThread(QtCore.QThread):
    """Thread for fetching data to keep UI responsive"""
    data_ready = QtCore.pyqtSignal(str, object)
    progress = QtCore.pyqtSignal(int, str)
    error = QtCore.pyqtSignal(str)

    def __init__(self, symbol, timeframe, days_to_load, data_source='auto'):
        """
        Initialize the data fetch thread.

        Args:
            symbol: Stock symbol to fetch
            timeframe: Data timeframe (e.g., '1m', '5m', '1d')
            days_to_load: Number of days of data to load
            data_source: Data source to use ('tradingview', 'yfinance', 'schwab', 'auto')
        """
        super().__init__()
        self.symbol = symbol
        self.timeframe = timeframe
        self.days_to_load = days_to_load
        self.data_source = data_source
        self.cache = DataCache.get_instance()

    def run(self):
        """Run the data fetch operation in a separate thread."""
        try:
            # Check cache first
            cache_key = f"{self.symbol}_{self.timeframe}_{self.days_to_load}_{self.data_source}"
            cached_data = self.cache.get_data(self.symbol, self.timeframe, self.days_to_load)
            if cached_data is not None:
                self.progress.emit(100, f"Using cached data for {self.symbol}")
                self.data_ready.emit(self.symbol, cached_data)
                return

            # Emit progress updates
            self.progress.emit(10, f"Initializing data fetch for {self.symbol}...")

            # Determine which data source to use
            use_tradingview = self._should_use_tradingview(self.data_source)
            use_schwab = self._should_use_schwab(self.timeframe, self.days_to_load)

            if use_tradingview:
                try:
                    # Add timeout protection for TradingView fetching
                    import signal

                    def timeout_handler(signum, frame):
                        raise TimeoutError("TradingView data fetch timed out")

                    # Set timeout for TradingView fetch (60 seconds)
                    if hasattr(signal, 'SIGALRM'):  # Unix systems only
                        signal.signal(signal.SIGALRM, timeout_handler)
                        signal.alarm(60)

                    try:
                        data = self._fetch_tradingview_data()
                    finally:
                        if hasattr(signal, 'SIGALRM'):
                            signal.alarm(0)  # Cancel the alarm

                    if data is None or data.empty:
                        logger.warning(f"TradingView scraper returned no data for {self.symbol}, falling back to Yahoo Finance")
                        self.progress.emit(50, f"TradingView failed, falling back to Yahoo Finance...")
                        data = self._fetch_yfinance_data()
                except (Exception, TimeoutError) as e:
                    logger.error(f"TradingView scraper error: {e}")
                    self.progress.emit(50, f"TradingView failed ({type(e).__name__}), falling back to Yahoo Finance...")
                    try:
                        data = self._fetch_yfinance_data()
                    except Exception as fallback_error:
                        logger.error(f"Yahoo Finance fallback also failed: {fallback_error}")
                        self.error.emit(f"Both TradingView and Yahoo Finance failed for {self.symbol}")
                        return
            elif use_schwab:
                try:
                    data = self._fetch_schwab_data()
                    if data is None or data.empty:
                        # Provide specific guidance for futures symbols
                        from ticker_converter import ticker_converter
                        if ticker_converter.is_futures_symbol(self.symbol):
                            yahoo_equivalent = ticker_converter.to_yahoo_format(self.symbol)
                            self.error.emit(f"Schwab API returned no data for futures symbol {self.symbol}. "
                                          f"This may be due to: 1) Extended trading hours limitations, "
                                          f"2) Symbol not available in Schwab API, or 3) Market closed. "
                                          f"Try using Yahoo Finance with symbol '{yahoo_equivalent}' or check during regular trading hours.")
                        else:
                            self.error.emit(f"Schwab API returned no data for symbol {self.symbol}. Check if symbol is valid and market is open.")
                        return
                except Exception as e:
                    self.error.emit(f"Schwab API error for {self.symbol}: {str(e)}")
                    return
            else:
                try:
                    data = self._fetch_yfinance_data()
                    if data is None or data.empty:
                        self.error.emit(f"Yahoo Finance returned no data for symbol {self.symbol}. Check if symbol is valid.")
                        return
                except Exception as e:
                    self.error.emit(f"Yahoo Finance error for {self.symbol}: {str(e)}")
                    return

            if data is None or data.empty:
                self.error.emit(f"No data available for symbol {self.symbol}")
                return

            self.progress.emit(70, f"Processing data for {self.symbol}...")

            # Remove timezone info if present
            if data.index.tz is not None:
                data.index = data.index.tz_localize(None)

            # Store the symbol in the DataFrame attributes
            data.attrs['symbol'] = self.symbol
            if use_tradingview:
                data.attrs['data_source'] = 'tradingview'
            elif use_schwab:
                data.attrs['data_source'] = 'schwab'
            else:
                data.attrs['data_source'] = 'yfinance'

            # Cache the data
            self.cache.store_data(self.symbol, self.timeframe, self.days_to_load, data)

            self.progress.emit(100, f"Data ready for {self.symbol}")
            self.data_ready.emit(self.symbol, data)

        except Exception as e:
            logger.error(f"Data fetch error: {e}")
            self.error.emit(f"Error: {str(e)}")

    def _should_use_schwab(self, timeframe: str, days: int) -> bool:
        """
        Determine whether to use Schwab API or yfinance based on data type.

        DISABLED: Historical data loading from Schwab API is completely disabled.
        This method now always returns False to prevent any historical data requests.

        Strategy:
        - 'yfinance': Always use Yahoo Finance
        - 'schwab': DISABLED - Never use Schwab for historical data
        - 'auto': DISABLED - Never use Schwab for historical data
        """
        # Historical data loading from Schwab API is completely disabled
        logger.warning(f"Schwab API historical data loading is DISABLED. Request for {days} days of {timeframe} data was blocked.")
        return False

    def _fetch_schwab_data(self) -> pd.DataFrame:
        """
        Fetch data using Schwab API with automatic fallback to Yahoo Finance

        DISABLED: This method has been disabled to prevent any historical data loading from Schwab API.
        It now immediately falls back to Yahoo Finance.
        """
        logger.warning(f"Schwab API historical data loading is DISABLED. Falling back to Yahoo Finance for {self.symbol}")
        self.progress.emit(40, f"Schwab API historical data disabled - using Yahoo Finance for {self.symbol}...")

        # Always fall back to Yahoo Finance since Schwab historical data is disabled
        return self._fetch_yfinance_data()

    def _fetch_yfinance_data(self) -> pd.DataFrame:
        """Fetch data using yfinance"""
        # Convert symbol to Yahoo Finance format
        yahoo_symbol = ticker_converter.to_yahoo_format(self.symbol)

        # Log symbol conversion if it occurred
        if yahoo_symbol != self.symbol:
            logger.info(f"Converted symbol for Yahoo Finance: {self.symbol} -> {yahoo_symbol}")

        self.progress.emit(30, f"Requesting {self.timeframe} data from Yahoo Finance for {yahoo_symbol}...")

        ticker = yf.Ticker(yahoo_symbol)
        period = f"{self.days_to_load}d"

        data = ticker.history(period=period, interval=self.timeframe)

        if data.empty:
            raise Exception("Yahoo Finance returned no data")

        logger.info(f"Fetched {len(data)} rows from Yahoo Finance for {yahoo_symbol}")
        return data

    def _should_use_tradingview(self, data_source: str) -> bool:
        """
        Determine whether to use TradingView scraper for OHLCV data.

        Args:
            data_source: The requested data source

        Returns:
            True if TradingView should be used, False otherwise
        """
        if not TRADINGVIEW_AVAILABLE:
            return False

        # Use TradingView for 'tradingview' or 'auto' data source requests
        return data_source in ['tradingview', 'auto']

    def _fetch_tradingview_data(self) -> pd.DataFrame:
        """Fetch data using TradingView scraper with auto exchange detection"""
        if not TRADINGVIEW_AVAILABLE:
            raise Exception("TradingView scraper not available")

        self.progress.emit(20, f"Auto-detecting exchange for {self.symbol}...")

        # Use auto exchange detection
        detected_exchange = Config.auto_detect_exchange(self.symbol)
        logger.info(f"Auto-detected exchange for {self.symbol}: {detected_exchange}")

        self.progress.emit(30, f"Requesting {self.timeframe} data from TradingView ({detected_exchange}) for {self.symbol}...")

        # Convert timeframe to TradingView format
        tv_timeframe = self._convert_timeframe_to_tradingview(self.timeframe)

        # Request more bars than needed to ensure we get enough data
        if tv_timeframe.lower() == '1d':
            bars = self.days_to_load * 3  # Account for weekends/holidays
        else:
            bars = self._calculate_bars_from_days(self.days_to_load, tv_timeframe) * 2

        # Ensure reasonable limits
        bars = max(bars, 50)  # Minimum 50 bars
        bars = min(bars, 5000)  # Maximum 5000 bars (TradingView limit)

        # Use TradingView scraper with robust error handling
        scraper = None
        try:
            self.progress.emit(40, f"Fetching {bars} bars of {tv_timeframe} data from TradingView...")

            scraper = TradingViewScraper()
            raw_data = scraper.get_historical_data(
                symbol=self.symbol,
                exchange=detected_exchange,
                timeframe=tv_timeframe,
                bars=bars,
                validate=True,
                extended_session="both"  # Include both regular and extended hours
            )

            if raw_data is None or raw_data.empty:
                raise Exception("TradingView scraper returned no data")

            # Filter data to get exactly the requested number of days
            data = self._filter_data_by_days(raw_data, self.days_to_load, tv_timeframe)

        except Exception as e:
            logger.error(f"TradingView scraper error for {self.symbol}: {e}")
            raise
        finally:
            # Ensure proper cleanup of TradingView scraper resources
            if scraper is not None:
                try:
                    # Clean up WebSocket connections
                    if hasattr(scraper, 'websocket_scraper') and scraper.websocket_scraper:
                        if hasattr(scraper.websocket_scraper, 'disconnect'):
                            scraper.websocket_scraper.disconnect()
                    # Clean up any other resources
                    if hasattr(scraper, '__exit__'):
                        scraper.__exit__(None, None, None)
                except Exception as cleanup_error:
                    logger.warning(f"Error during TradingView scraper cleanup: {cleanup_error}")
                    # Don't re-raise cleanup errors

        # Normalize column names to match Yahoo Finance format (Title Case)
        # TradingView uses lowercase, but the application expects Title Case
        column_mapping = {
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }

        # Rename columns if they exist
        for old_name, new_name in column_mapping.items():
            if old_name in data.columns:
                data = data.rename(columns={old_name: new_name})

        logger.info(f"Fetched {len(data)} rows from TradingView for {self.symbol} on {detected_exchange}")
        logger.info(f"TradingView data columns: {list(data.columns)}")
        return data

    def _convert_timeframe_to_tradingview(self, timeframe: str) -> str:
        """Convert standard timeframe to TradingView format"""
        # TradingView timeframe mapping based on Config.TIMEFRAMES
        timeframe_map = {
            '1m': '1m',      # 1 minute
            '2m': '1m',      # 2m not supported, fallback to 1m
            '5m': '5m',      # 5 minutes
            '15m': '15m',    # 15 minutes
            '30m': '30m',    # 30 minutes
            '60m': '1h',     # 60 minutes = 1 hour
            '1h': '1h',      # 1 hour
            '1d': '1D',      # 1 day
            '1wk': '1W',     # 1 week
            '1mo': '1M'      # 1 month
        }
        converted = timeframe_map.get(timeframe, timeframe)
        logger.info(f"Converting timeframe: {timeframe} -> {converted}")
        return converted

    def _calculate_bars_from_days(self, days: int, timeframe: str) -> int:
        """Calculate approximate number of bars from days and timeframe"""
        # Approximate bars per day for different timeframes (using TradingView format)
        bars_per_day = {
            '1m': 390,   # 1 minute: ~6.5 hours * 60 minutes
            '5m': 78,    # 5 minutes: ~6.5 hours * 12 bars per hour
            '15m': 26,   # 15 minutes: ~6.5 hours * 4 bars per hour
            '30m': 13,   # 30 minutes: ~6.5 hours * 2 bars per hour
            '1h': 6.5,   # 1 hour: ~6.5 trading hours per day
            '1D': 1,     # 1 day
            '1W': 0.2,   # 1 week: ~5 trading days per week
            '1M': 0.05   # 1 month: ~20 trading days per month
        }

        multiplier = bars_per_day.get(timeframe, 1)
        bars = int(days * multiplier)

        # Ensure minimum and maximum limits
        bars = max(bars, 10)  # Minimum 10 bars
        bars = min(bars, 5000)  # Maximum 5000 bars (TradingView limit)

        logger.info(f"Calculated {bars} bars for {days} days with {timeframe} timeframe")
        return bars

    def _filter_data_by_days(self, data, days_requested, timeframe):
        """Filter data to get exactly the requested number of days"""
        try:
            from datetime import datetime, timedelta

            if data is None or data.empty:
                return data

            # For daily timeframe, simply take the last N days
            if timeframe.lower() in ['1d', '1D']:
                filtered_data = data.tail(days_requested)
                logger.info(f"Filtered daily data: {len(data)} -> {len(filtered_data)} bars for {days_requested} days")
                return filtered_data

            # For intraday timeframes, calculate the date range
            # Get the last date in the data
            last_date = data.index[-1]

            # Calculate the start date (days_requested ago)
            if hasattr(last_date, 'date'):
                # If it's a datetime, get just the date part
                end_date = last_date.date()
            else:
                # If it's already a date, use it directly
                end_date = last_date

            start_date = end_date - timedelta(days=days_requested)

            # Filter data to include only the requested date range
            filtered_data = data[data.index.date >= start_date]

            logger.info(f"Filtered intraday data: {len(data)} -> {len(filtered_data)} bars for {days_requested} days")
            return filtered_data

        except Exception as e:
            # If filtering fails, return the original data
            logger.warning(f"Could not filter data by days: {e}")
            return data


class DataDispatcher(QtCore.QObject):
    """
    Centralized data dispatcher for the application.

    This class manages data fetching and distribution to various components,
    ensuring that data is fetched only once and shared efficiently.
    Supports TradingView scraper for OHLCV data, Yahoo Finance for options data,
    and Schwab API for live quotes.
    """

    # Signal to notify when data is fetched and ready
    data_fetched = QtCore.pyqtSignal(str, object)

    # Signal for progress updates
    progress = QtCore.pyqtSignal(int, str)

    # Signal for errors
    error = QtCore.pyqtSignal(str)

    # Signal for data source changes
    data_source_changed = QtCore.pyqtSignal(str)  # new data source

    _instance = None
    _lock = threading.RLock()

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the DataDispatcher."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DataDispatcher()
            return cls._instance

    def __init__(self):
        """Initialize the data dispatcher."""
        super().__init__()
        self.fetch_threads = {}
        self.cache = DataCache.get_instance()
        self.preferred_data_source = 'tradingview'  # Use TradingView for OHLCV data

    def set_data_source(self, source: str):
        """
        Set the preferred data source.

        Args:
            source: Data source ('tradingview', 'yfinance', 'schwab', 'auto')
        """
        valid_sources = ['tradingview', 'yfinance', 'schwab', 'auto']
        if source in valid_sources:
            self.preferred_data_source = source
            self.data_source_changed.emit(source)
            logger.info(f"Data source changed to: {source}")
        else:
            logger.warning(f"Invalid data source '{source}', keeping current: {self.preferred_data_source}")

    def get_data_source(self) -> str:
        """Get the current preferred data source."""
        return self.preferred_data_source

    def get_available_sources(self) -> list:
        """Get list of available data sources."""
        sources = ['yfinance']
        if TRADINGVIEW_AVAILABLE:
            sources.append('tradingview')
        if SCHWAB_AVAILABLE and schwab_api and schwab_api.is_connected():
            sources.append('schwab')
        return sources

    def fetch_data(self, symbol, timeframe, days_to_load, data_source=None):
        """
        Fetch data for the specified parameters.

        Args:
            symbol: Stock symbol
            timeframe: Data timeframe
            days_to_load: Number of days of data to load
            data_source: Override data source for this request (optional)
        """
        # Use provided data source or fall back to preferred
        source = data_source or self.preferred_data_source

        # Check if we're already fetching this data
        thread_key = (symbol, timeframe, days_to_load, source)
        if thread_key in self.fetch_threads and self.fetch_threads[thread_key].isRunning():
            # Already fetching this data, just return
            return

        # Create and start the data fetch thread
        fetch_thread = DataFetchThread(symbol, timeframe, days_to_load, source)
        fetch_thread.data_ready.connect(self._on_data_ready)
        fetch_thread.progress.connect(self._on_progress)
        fetch_thread.error.connect(self._on_error)
        fetch_thread.finished.connect(lambda: self._on_thread_finished(thread_key))

        self.fetch_threads[thread_key] = fetch_thread
        fetch_thread.start()

    def _on_data_ready(self, symbol, data):
        """Handle data ready signal from fetch thread."""
        self.data_fetched.emit(symbol, data)

    def _on_progress(self, value, message):
        """Handle progress signal from fetch thread."""
        self.progress.emit(value, message)

    def _on_error(self, error_message):
        """Handle error signal from fetch thread."""
        self.error.emit(error_message)

    def _on_thread_finished(self, thread_key):
        """Clean up after thread finishes."""
        try:
            if thread_key in self.fetch_threads:
                thread = self.fetch_threads[thread_key]
                # Ensure thread is properly cleaned up
                if thread.isRunning():
                    thread.quit()
                    thread.wait(1000)  # Wait up to 1 second for thread to finish
                del self.fetch_threads[thread_key]
                logger.debug(f"Cleaned up fetch thread for {thread_key}")
        except Exception as e:
            logger.warning(f"Error cleaning up thread {thread_key}: {e}")
            # Remove the thread key anyway to prevent memory leaks
            if thread_key in self.fetch_threads:
                del self.fetch_threads[thread_key]

    def clear_cache(self):
        """Clear the data cache."""
        self.cache.clear()

    def clear_symbol_cache(self, symbol):
        """Clear cached data for a specific symbol."""
        self.cache.clear_symbol(symbol)

    def get_cache_stats(self):
        """Get cache statistics."""
        return self.cache.get_stats()

    def cleanup_all_threads(self):
        """Force cleanup of all running fetch threads to prevent crashes."""
        try:
            logger.info(f"Cleaning up {len(self.fetch_threads)} fetch threads...")
            threads_to_cleanup = list(self.fetch_threads.keys())

            for thread_key in threads_to_cleanup:
                try:
                    thread = self.fetch_threads.get(thread_key)
                    if thread and thread.isRunning():
                        logger.debug(f"Terminating running thread: {thread_key}")
                        thread.quit()
                        if not thread.wait(2000):  # Wait up to 2 seconds
                            logger.warning(f"Thread {thread_key} did not finish gracefully, terminating...")
                            thread.terminate()
                            thread.wait(1000)  # Wait for termination

                    # Remove from dictionary
                    if thread_key in self.fetch_threads:
                        del self.fetch_threads[thread_key]

                except Exception as e:
                    logger.warning(f"Error cleaning up thread {thread_key}: {e}")
                    # Remove anyway to prevent memory leaks
                    if thread_key in self.fetch_threads:
                        del self.fetch_threads[thread_key]

            logger.info("Thread cleanup completed")

        except Exception as e:
            logger.error(f"Error during thread cleanup: {e}")
            # Clear the dictionary anyway
            self.fetch_threads.clear()
