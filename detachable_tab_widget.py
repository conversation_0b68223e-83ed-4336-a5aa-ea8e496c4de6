"""
Detachable Tab Widget for PyQt6

This module provides a QTabWidget subclass that allows tabs to be detached
from the main window and displayed as separate floating windows.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
from universal_controls import UniversalControlPanel


class TabInfo:
    """Class to store tab information when detaching/reattaching tabs."""
    def __init__(self):
        self.widget = None
        self.text = None
        self.icon = None
        self.tool_tip = None
        self.whats_this = None


class DetachableTabWidget(QtWidgets.QTabWidget):
    """
    A QTabWidget subclass that supports detachable tabs.

    Tabs can be dragged out of the tab bar to create a new window.
    """

    def __init__(self, parent=None):
        """Initialize the detachable tab widget."""
        super().__init__(parent)

        # Enable tab movement
        self.setMovable(True)

        # Initialize drag state
        self.drag_start_pos = None
        self.dragging = False
        self.drag_tab_index = -1
        self.drag_initiated = False

        # Set the tab bar to accept drops
        self.tabBar().setAcceptDrops(True)

        # Install event filter on tab bar to handle mouse events
        self.tabBar().installEventFilter(self)

        # Track detached tabs
        self.detached_tabs = {}

    def eventFilter(self, obj, event):
        """
        Filter events for the tab bar to handle tab dragging.

        Args:
            obj: Object that triggered the event
            event: Event that was triggered

        Returns:
            bool: True if the event was handled, False otherwise
        """
        # Only process events for the tab bar
        if obj != self.tabBar():
            return False

        # Handle mouse press events to start drag
        if event.type() == QtCore.QEvent.Type.MouseButtonPress and event.button() == QtCore.Qt.MouseButton.LeftButton:
            self.drag_start_pos = event.pos()
            self.drag_tab_index = self.tabBar().tabAt(event.pos())
            return False

        # Handle mouse move events to detect drag
        elif event.type() == QtCore.QEvent.Type.MouseMove and self.drag_start_pos is not None:
            # Check if we've moved far enough to start a drag
            if not self.dragging and (event.pos() - self.drag_start_pos).manhattanLength() > QtWidgets.QApplication.startDragDistance():
                self.dragging = True

            # If we're dragging and haven't initiated a drag yet
            if self.dragging and not self.drag_initiated and self.drag_tab_index >= 0:
                # Check if we've moved outside the tab bar
                if not self.tabBar().rect().contains(event.pos()):
                    self.detach_tab(self.drag_tab_index)
                    self.drag_initiated = True
                    return True

            return False

        # Handle mouse release events to end drag
        elif event.type() == QtCore.QEvent.Type.MouseButtonRelease:
            self.drag_start_pos = None
            self.dragging = False
            self.drag_initiated = False
            self.drag_tab_index = -1
            return False

        return False

    def detach_tab(self, index):
        """
        Detach a tab from the tab widget and create a new window.
        Instead of removing the tab, create a copy of it in a new window.

        Args:
            index: Index of the tab to detach
        """
        if index < 0 or index >= self.count():
            return

        # Get tab information
        tab_info = TabInfo()
        tab_info.widget = self.widget(index)
        tab_info.text = self.tabText(index)
        tab_info.icon = self.tabIcon(index)
        tab_info.tool_tip = self.tabToolTip(index)
        tab_info.whats_this = self.tabWhatsThis(index)

        # Get the global position for the new window
        point = self.mapToGlobal(self.tabBar().tabRect(index).center())

        # Create a new window for the detached tab
        # We'll create a clone of the tab widget instead of moving the original
        # Use a smaller default size that can be resized freely
        new_window = self.create_new_window(QtCore.QRect(point.x(), point.y(), 400, 300), tab_info, clone=True)

        # Store the new window with a unique key (use the cloned widget)
        for key, window in new_window.tab_widget_map.items():
            self.detached_tabs[key] = window

    def create_new_window(self, win_rect, tab_info, clone=False):
        """
        Create a new window for a detached tab.

        Args:
            win_rect: Rectangle for the new window
            tab_info: Information about the tab
            clone: If True, clone the tab widget instead of moving it

        Returns:
            DetachableTabWindow: The new window
        """
        # Create a new detachable tab window
        new_window = DetachableTabWindow(self)

        # Add the tab to the new window (clone it if requested)
        new_window.add_tab(tab_info, clone=clone)

        # Set the window geometry
        new_window.setGeometry(win_rect)

        # Show the window
        new_window.show()

        return new_window

    def add_tab(self, tab_info):
        """
        Add a tab to the tab widget.

        Args:
            tab_info: Information about the tab

        Returns:
            int: Index of the new tab
        """
        index = self.addTab(tab_info.widget, tab_info.icon, tab_info.text)
        self.setTabToolTip(index, tab_info.tool_tip)
        self.setTabWhatsThis(index, tab_info.whats_this)
        return index


class DetachableTabWindow(QtWidgets.QMainWindow):
    """
    A window that contains a detached tab.

    This window can be closed to reattach the tab to the original tab widget.
    """

    def __init__(self, original_tab_widget):
        """
        Initialize the detachable tab window.

        Args:
            original_tab_widget: The tab widget that the tab was detached from
        """
        super().__init__()

        # Store the original tab widget
        self.original_tab_widget = original_tab_widget

        # Create a central widget to hold both the universal controls and the tab widget
        central_widget = QtWidgets.QWidget()
        central_layout = QtWidgets.QVBoxLayout(central_widget)
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(0)

        # Create the universal control panel
        self.universal_controls = UniversalControlPanel(self)
        central_layout.addWidget(self.universal_controls)

        # Create a new tab widget for this window
        self.tab_widget = DetachableTabWidget(self)
        self.tab_widget.setTabsClosable(False)
        central_layout.addWidget(self.tab_widget)

        # Set the central widget
        self.setCentralWidget(central_widget)

        # Set window properties
        self.setWindowTitle("Detached Tab")



        # Try to copy the universal control settings from the main window
        self.copy_universal_control_settings()

        # Dictionary to map cloned widgets to their original widgets
        self.tab_widget_map = {}

    def copy_universal_control_settings(self):
        """
        Copy the universal control settings from the main window.

        This ensures that the detached window has the same symbol, timeframe, etc.
        as the main window.
        """
        try:
            # Find the main window
            for widget in QtWidgets.QApplication.topLevelWidgets():
                if hasattr(widget, 'universal_controls'):
                    # Copy symbol
                    if hasattr(widget.universal_controls, 'symbol_input') and hasattr(self.universal_controls, 'symbol_input'):
                        self.universal_controls.symbol_input.setText(widget.universal_controls.symbol_input.text())

                    # Copy timeframe
                    if hasattr(widget.universal_controls, 'timeframe_combo') and hasattr(self.universal_controls, 'timeframe_combo'):
                        self.universal_controls.timeframe_combo.setCurrentText(widget.universal_controls.timeframe_combo.currentText())

                    # Copy vector length
                    if hasattr(widget.universal_controls, 'vector_length_spin') and hasattr(self.universal_controls, 'vector_length_spin'):
                        self.universal_controls.vector_length_spin.setValue(widget.universal_controls.vector_length_spin.value())

                    # Copy days to load
                    if hasattr(widget.universal_controls, 'days_spin') and hasattr(self.universal_controls, 'days_spin'):
                        self.universal_controls.days_spin.setValue(widget.universal_controls.days_spin.value())

                    # Connect the data_fetched signal from this window's universal controls to the tab
                    if hasattr(self.universal_controls, 'data_fetched') and self.tab_widget.count() > 0:
                        tab_widget = self.tab_widget.widget(0)
                        if hasattr(tab_widget, 'update_data_from_universal'):
                            self.universal_controls.data_fetched.connect(tab_widget.update_data_from_universal)
                        elif hasattr(tab_widget, 'refresh_data'):
                            self.universal_controls.data_fetched.connect(tab_widget.refresh_data)

                    # Set up synchronization between main window and detached window
                    self.setup_control_synchronization(widget.universal_controls)

                    # Only need to copy from the first main window found
                    break
        except Exception as e:
            print(f"Error copying universal control settings: {e}")

    def setup_control_synchronization(self, main_controls):
        """
        Set up synchronization between the main window's controls and this window's controls.

        Args:
            main_controls: The universal controls from the main window
        """
        try:
            # Synchronize symbol input
            if hasattr(main_controls, 'symbol_input') and hasattr(self.universal_controls, 'symbol_input'):
                # When main window changes, update this window
                main_controls.symbol_input.textChanged.connect(self.universal_controls.symbol_input.setText)
                # When this window changes, update main window
                self.universal_controls.symbol_input.textChanged.connect(main_controls.symbol_input.setText)

            # Synchronize timeframe combo
            if hasattr(main_controls, 'timeframe_combo') and hasattr(self.universal_controls, 'timeframe_combo'):
                # When main window changes, update this window
                main_controls.timeframe_combo.currentTextChanged.connect(self.universal_controls.timeframe_combo.setCurrentText)
                # When this window changes, update main window
                self.universal_controls.timeframe_combo.currentTextChanged.connect(main_controls.timeframe_combo.setCurrentText)

            # Synchronize vector length spin
            if hasattr(main_controls, 'vector_length_spin') and hasattr(self.universal_controls, 'vector_length_spin'):
                # When main window changes, update this window
                main_controls.vector_length_spin.valueChanged.connect(self.universal_controls.vector_length_spin.setValue)
                # When this window changes, update main window
                self.universal_controls.vector_length_spin.valueChanged.connect(main_controls.vector_length_spin.setValue)

            # Synchronize days spin
            if hasattr(main_controls, 'days_spin') and hasattr(self.universal_controls, 'days_spin'):
                # When main window changes, update this window
                main_controls.days_spin.valueChanged.connect(self.universal_controls.days_spin.setValue)
                # When this window changes, update main window
                self.universal_controls.days_spin.valueChanged.connect(main_controls.days_spin.setValue)
        except Exception as e:
            print(f"Error setting up control synchronization: {e}")

    def add_tab(self, tab_info, clone=False):
        """
        Add a tab to the window's tab widget.

        Args:
            tab_info: Information about the tab
            clone: If True, clone the tab widget instead of using the original

        Returns:
            int: Index of the new tab
        """
        if clone:
            # Create a clone of the tab widget
            cloned_widget = self.clone_widget(tab_info.widget)

            # Create a new tab info with the cloned widget
            cloned_tab_info = TabInfo()
            cloned_tab_info.widget = cloned_widget
            cloned_tab_info.text = tab_info.text
            cloned_tab_info.icon = tab_info.icon
            cloned_tab_info.tool_tip = tab_info.tool_tip
            cloned_tab_info.whats_this = tab_info.whats_this

            # Add the cloned tab to the tab widget
            tab_index = self.tab_widget.add_tab(cloned_tab_info)

            # Store the mapping between the cloned widget and the original widget
            self.tab_widget_map[cloned_widget] = tab_info.widget
        else:
            # Add the original tab to the tab widget
            tab_index = self.tab_widget.add_tab(tab_info)

        # Set the window title to match the tab title
        self.setWindowTitle(f"Detached: {tab_info.text}")

        # Connect the universal controls to the tab if possible
        try:
            tab_widget = self.tab_widget.widget(tab_index)
            if hasattr(self.universal_controls, 'data_fetched'):
                if hasattr(tab_widget, 'update_data_from_universal'):
                    self.universal_controls.data_fetched.connect(tab_widget.update_data_from_universal)
                elif hasattr(tab_widget, 'refresh_data'):
                    self.universal_controls.data_fetched.connect(tab_widget.refresh_data)
        except Exception as e:
            print(f"Error connecting universal controls to tab: {e}")

        return tab_index

    def clone_widget(self, widget):
        """
        Create a clone of a widget.

        This is a simplified cloning mechanism that creates a new instance of the same class
        and tries to copy over the essential properties and methods.

        Args:
            widget: The widget to clone

        Returns:
            The cloned widget
        """
        try:
            # Get the class of the widget
            widget_class = widget.__class__

            # Create a new instance of the same class
            cloned_widget = widget_class()

            # Try to copy over any update methods
            if hasattr(widget, 'update_data_from_universal'):
                cloned_widget.update_data_from_universal = widget.update_data_from_universal

            if hasattr(widget, 'refresh_data'):
                cloned_widget.refresh_data = widget.refresh_data

            # Return the cloned widget
            return cloned_widget
        except Exception as e:
            print(f"Error cloning widget: {e}")
            # If cloning fails, create a simple QWidget with a label
            fallback_widget = QtWidgets.QWidget()
            layout = QtWidgets.QVBoxLayout(fallback_widget)
            label = QtWidgets.QLabel("This is a copy of the original tab. Some functionality may be limited.")
            layout.addWidget(label)
            return fallback_widget

    def on_tab_close_requested(self, index):
        """
        Handle a request to close a tab.

        Args:
            index: Index of the tab to close
        """
        # Get the widget at the specified index
        widget = self.tab_widget.widget(index)

        # Remove the tab from this window
        self.tab_widget.removeTab(index)

        # Remove this widget from the tab_widget_map
        if widget in self.tab_widget_map:
            del self.tab_widget_map[widget]

        # Remove this window from the original tab widget's list of detached tabs if needed
        for key, window in list(self.original_tab_widget.detached_tabs.items()):
            if window == self:
                del self.original_tab_widget.detached_tabs[key]
                break

        # Close this window if it has no more tabs
        if self.tab_widget.count() == 0:
            self.close()

    def closeEvent(self, event):
        """
        Handle the window close event.

        Args:
            event: Close event
        """
        # Clean up all tabs
        while self.tab_widget.count() > 0:
            # Get the widget at index 0
            widget = self.tab_widget.widget(0)

            # Remove the tab from this window
            self.tab_widget.removeTab(0)

            # Remove this widget from the tab_widget_map
            if widget in self.tab_widget_map:
                del self.tab_widget_map[widget]

        # Remove this window from the original tab widget's list of detached tabs
        for key, window in list(self.original_tab_widget.detached_tabs.items()):
            if window == self:
                del self.original_tab_widget.detached_tabs[key]

        # Accept the close event
        event.accept()
