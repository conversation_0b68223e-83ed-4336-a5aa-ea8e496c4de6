"""
Enhanced feature engineering for the Crossing Classifier.
This module provides advanced technical indicators and market context features
to improve the predictive power of the crossing classifier.
"""

import numpy as np
import pandas as pd
import logging
from lru_cache import LRUCache
from datetime import datetime, time


class FeatureExtractor:
    """
    Feature extractor for the Crossing Classifier.
    Provides methods to extract various technical indicators and market context features.
    """

    def __init__(self, cache_size=1000):
        """
        Initialize the feature extractor.

        Parameters:
        - cache_size: Size of the LRU cache for feature caching
        """
        self.cache = LRUCache(cache_size)
        self.feature_names = []
        self._initialize_feature_names()

    def _initialize_feature_names(self):
        """Initialize the list of feature names."""
        # Basic features (from original implementation)
        basic_features = [
            'price_change', 'vector_change', 'price_vector_distance',
            'volatility', 'price_vs_high', 'price_vs_low', 'previous_relation',
            'volume_change', 'relative_volume', 'volume_trend', 'market_regime'
        ]

        # Technical indicators
        tech_indicators = [
            'rsi', 'rsi_change', 'rsi_divergence',
            'macd', 'macd_signal', 'macd_histogram', 'macd_crossover',
            'atr', 'atr_ratio',
            'stoch_k', 'stoch_d', 'stoch_crossover',
            'obv', 'obv_change', 'obv_divergence'
        ]

        # Market context features
        market_context = [
            'distance_to_peak', 'distance_to_trough',
            'peak_trough_range', 'position_in_range',
            'trend_strength', 'trend_direction',
            'volatility_regime', 'momentum_regime'
        ]

        # Time-based features
        time_features = [
            'hour_of_day', 'day_of_week', 'week_of_month', 'month_of_year',
            'is_market_open', 'time_since_open', 'time_to_close'
        ]

        # Combine all features
        self.feature_names = basic_features + tech_indicators + market_context + time_features

    def extract_features(self, data, idx, vector_values, peaks=None, troughs=None):
        """
        Extract all features for a given crossing point.

        Parameters:
        - data: DataFrame containing OHLCV data
        - idx: Index of the crossing point
        - vector_values: Series containing vector values
        - peaks: List of peak indices (optional)
        - troughs: List of trough indices (optional)

        Returns:
        - numpy array of features
        """
        # Use caching for performance
        cache_key = f"{idx}"
        cached_features = self.cache.get(cache_key)
        if cached_features is not None:
            return cached_features

        # Extract basic features (these would come from the original implementation)
        basic_features = self._extract_basic_features(data, idx, vector_values)

        # Extract technical indicators
        tech_indicators = self._extract_technical_indicators(data, idx)

        # Extract market context features
        market_context = self._extract_market_context(data, idx, vector_values, peaks, troughs)

        # Extract time-based features
        time_features = self._extract_time_features(data, idx)

        # Make sure all feature arrays are properly initialized
        if basic_features is None:
            basic_features = np.zeros(11)  # 11 basic features
        if tech_indicators is None:
            tech_indicators = np.zeros(15)  # 15 technical indicators
        if market_context is None:
            market_context = np.zeros(8)  # 8 market context features
        if time_features is None:
            time_features = np.zeros(7)  # 7 time-based features

        # Combine all features
        all_features = np.concatenate([basic_features, tech_indicators, market_context, time_features])

        # Verify we have the expected number of features (41)
        if len(all_features) != 41:
            # Use logger instead of print
            logger = logging.getLogger(__name__)
            logger.error(f"Feature count mismatch in FeatureExtractor. Expected 41, got {len(all_features)}")
            # Debug information only logged at error level
            logger.error(f"  Basic features: {len(basic_features)}, Technical indicators: {len(tech_indicators)}, Market context: {len(market_context)}, Time features: {len(time_features)}")

        # Cache the result
        self.cache.put(cache_key, all_features)
        return all_features

    def _extract_basic_features(self, data, idx, vector_values):
        """
        Extract basic features from the original implementation.

        Parameters:
        - data: DataFrame containing OHLCV data
        - idx: Index of the crossing point
        - vector_values: Series containing vector values

        Returns:
        - numpy array of basic features
        """
        if idx < 5:  # Need at least some history
            return np.zeros(11)  # Return zeros for all basic features

        # Get data window
        start_idx = max(0, idx - 5)  # Use a 5-bar lookback window
        end_idx = idx + 1
        window_data = data.iloc[start_idx:end_idx]
        window_vector = vector_values.iloc[start_idx:end_idx]

        # Extract price data
        close_prices = window_data['Close'].values
        high_prices = window_data['High'].values
        low_prices = window_data['Low'].values
        vector_prices = window_vector.values

        # Calculate features
        features = np.zeros(11)

        # Percent change in price and vector
        features[0] = (close_prices[-1] / close_prices[0] - 1) * 100 if len(close_prices) > 1 else 0
        features[1] = (vector_prices[-1] / vector_prices[0] - 1) * 100 if len(vector_prices) > 1 else 0

        # Distance between price and vector
        if len(close_prices) > 1 and len(vector_prices) > 1:
            features[2] = abs((close_prices[-2] / vector_prices[-2] - 1) * 100)
        else:
            features[2] = 0

        features[3] = np.mean((high_prices / low_prices - 1)) * 100 if len(high_prices) > 0 and len(low_prices) > 0 else 0
        recent_high = np.max(high_prices) if len(high_prices) > 0 else 1
        recent_low = np.min(low_prices) if len(low_prices) > 0 else 1
        features[4] = close_prices[-1] / recent_high if len(close_prices) > 0 else 1
        features[5] = close_prices[-1] / recent_low if len(close_prices) > 0 else 1

        if len(close_prices) > 1 and len(vector_prices) > 1:
            features[6] = 1 if close_prices[-2] < vector_prices[-2] else -1
        else:
            features[6] = 0

        # Extract volume data if available
        has_volume = "Volume" in window_data.columns
        volume_data = window_data["Volume"].values if has_volume else np.ones_like(close_prices)

        # Volume features
        if has_volume and len(volume_data) > 0:
            # Volume change (percent change in volume)
            features[7] = (volume_data[-1] / max(volume_data[0], 1) - 1) * 100 if len(volume_data) > 1 else 0

            # Relative volume (current volume compared to average volume in window)
            avg_volume = np.mean(volume_data)
            features[8] = volume_data[-1] / max(avg_volume, 1)

            # Volume trend (correlation between price and volume)
            if len(volume_data) > 2 and np.std(volume_data) > 0 and np.std(close_prices) > 0:
                features[9] = np.corrcoef(close_prices, volume_data)[0, 1]
            else:
                features[9] = 0
        else:
            features[7:10] = 0  # Zero out volume features if not available

        # Market regime feature
        # Calculate simple market regime based on recent price action
        # Positive = uptrend, negative = downtrend, near zero = sideways
        if len(close_prices) >= 3:
            price_sma_fast = np.mean(close_prices[-3:])
            price_sma_slow = np.mean(close_prices)
            features[10] = (price_sma_fast / price_sma_slow - 1) * 100
        else:
            features[10] = 0

        return features

    def _extract_technical_indicators(self, data, idx):
        """
        Extract technical indicators.

        Parameters:
        - data: DataFrame containing OHLCV data
        - idx: Index of the crossing point

        Returns:
        - numpy array of technical indicator features
        """
        if idx < 30:  # Need sufficient history for indicators
            return np.zeros(15)  # Number of technical indicator features

        # Extract relevant data for indicator calculation
        prices = data['Close'].iloc[:idx+1]
        highs = data['High'].iloc[:idx+1]
        lows = data['Low'].iloc[:idx+1]
        volumes = data['Volume'].iloc[:idx+1] if 'Volume' in data.columns else None

        # Calculate RSI (Relative Strength Index)
        rsi = self._calculate_rsi(prices, period=14)
        rsi_value = rsi.iloc[-1] if not rsi.empty else 50
        rsi_change = rsi.iloc[-1] - rsi.iloc[-2] if len(rsi) >= 2 else 0

        # Check for RSI divergence (price making new high/low but RSI isn't)
        price_trend = 1 if prices.iloc[-1] > prices.iloc[-5] else -1
        rsi_trend = 1 if rsi.iloc[-1] > rsi.iloc[-5] else -1
        rsi_divergence = 1 if price_trend != rsi_trend else 0

        # Calculate MACD (Moving Average Convergence Divergence)
        macd, macd_signal, macd_hist = self._calculate_macd(prices)
        macd_value = macd.iloc[-1] if not macd.empty else 0
        macd_signal_value = macd_signal.iloc[-1] if not macd_signal.empty else 0
        macd_hist_value = macd_hist.iloc[-1] if not macd_hist.empty else 0

        # MACD crossover (1 for bullish, -1 for bearish, 0 for none)
        macd_crossover = 0
        if len(macd) >= 2 and len(macd_signal) >= 2:
            if macd.iloc[-2] < macd_signal.iloc[-2] and macd.iloc[-1] > macd_signal.iloc[-1]:
                macd_crossover = 1  # Bullish crossover
            elif macd.iloc[-2] > macd_signal.iloc[-2] and macd.iloc[-1] < macd_signal.iloc[-1]:
                macd_crossover = -1  # Bearish crossover

        # Calculate ATR (Average True Range)
        atr = self._calculate_atr(highs, lows, prices, period=14)
        atr_value = atr.iloc[-1] if not atr.empty else 0

        # ATR as a percentage of price
        atr_ratio = atr_value / prices.iloc[-1] if prices.iloc[-1] > 0 else 0

        # Calculate Stochastic Oscillator
        stoch_k, stoch_d = self._calculate_stochastic(highs, lows, prices)
        stoch_k_value = stoch_k.iloc[-1] if not stoch_k.empty else 50
        stoch_d_value = stoch_d.iloc[-1] if not stoch_d.empty else 50

        # Stochastic crossover (1 for bullish, -1 for bearish, 0 for none)
        stoch_crossover = 0
        if len(stoch_k) >= 2 and len(stoch_d) >= 2:
            if stoch_k.iloc[-2] < stoch_d.iloc[-2] and stoch_k.iloc[-1] > stoch_d.iloc[-1]:
                stoch_crossover = 1  # Bullish crossover
            elif stoch_k.iloc[-2] > stoch_d.iloc[-2] and stoch_k.iloc[-1] < stoch_d.iloc[-1]:
                stoch_crossover = -1  # Bearish crossover

        # Calculate OBV (On-Balance Volume) if volume data is available
        obv_value = 0
        obv_change = 0
        obv_divergence = 0

        if volumes is not None and not volumes.empty:
            obv = self._calculate_obv(prices, volumes)
            obv_value = obv.iloc[-1] if not obv.empty else 0
            obv_change = obv.iloc[-1] - obv.iloc[-5] if len(obv) >= 5 else 0

            # Check for OBV divergence
            price_trend = 1 if prices.iloc[-1] > prices.iloc[-5] else -1
            obv_trend = 1 if obv_value > obv.iloc[-5] else -1
            obv_divergence = 1 if price_trend != obv_trend else 0

        # Combine all technical indicator features
        return np.array([
            rsi_value / 100.0,  # Normalize to [0, 1]
            rsi_change / 100.0,  # Normalize
            rsi_divergence,
            macd_value,
            macd_signal_value,
            macd_hist_value,
            macd_crossover,
            atr_value,
            atr_ratio,
            stoch_k_value / 100.0,  # Normalize to [0, 1]
            stoch_d_value / 100.0,  # Normalize to [0, 1]
            stoch_crossover,
            obv_value / 1000000.0 if obv_value != 0 else 0,  # Normalize
            obv_change / 1000000.0 if obv_change != 0 else 0,  # Normalize
            obv_divergence
        ])

    def _extract_market_context(self, data, idx, vector_values, peaks=None, troughs=None):
        """
        Extract market context features.

        Parameters:
        - data: DataFrame containing OHLCV data
        - idx: Index of the crossing point
        - vector_values: Series containing vector values
        - peaks: List of peak indices (optional)
        - troughs: List of trough indices (optional)

        Returns:
        - numpy array of market context features
        """
        if idx < 30:  # Need sufficient history
            return np.zeros(8)  # Number of market context features

        # Extract relevant data
        prices = data['Close'].iloc[:idx+1]
        current_price = prices.iloc[-1]

        # Distance to nearest peak/trough
        distance_to_peak = 1.0
        distance_to_trough = 1.0
        peak_trough_range = 0.0
        position_in_range = 0.5

        if peaks is not None and troughs is not None:
            # Find nearest peak and trough before current index
            nearest_peak_idx = None
            nearest_trough_idx = None

            for peak_idx in reversed(peaks):
                if peak_idx < idx:
                    nearest_peak_idx = peak_idx
                    break

            for trough_idx in reversed(troughs):
                if trough_idx < idx:
                    nearest_trough_idx = trough_idx
                    break

            if nearest_peak_idx is not None and nearest_trough_idx is not None:
                peak_price = data['High'].iloc[nearest_peak_idx]
                trough_price = data['Low'].iloc[nearest_trough_idx]

                # Calculate distances as percentages
                distance_to_peak = (peak_price - current_price) / current_price if current_price > 0 else 0
                distance_to_trough = (current_price - trough_price) / current_price if current_price > 0 else 0

                # Calculate range and position
                peak_trough_range = (peak_price - trough_price) / trough_price if trough_price > 0 else 0
                if peak_trough_range > 0:
                    position_in_range = (current_price - trough_price) / (peak_price - trough_price)

        # Calculate trend strength and direction
        trend_strength = 0.0
        trend_direction = 0.0

        if len(prices) >= 20:
            # Simple trend calculation using linear regression slope
            x = np.arange(20)
            y = prices.iloc[-20:].values
            slope, _ = np.polyfit(x, y, 1)

            trend_direction = 1.0 if slope > 0 else -1.0
            trend_strength = abs(slope) / prices.iloc[-20] if prices.iloc[-20] > 0 else 0

        # Calculate volatility regime
        volatility_regime = 0.0
        if len(prices) >= 20:
            recent_volatility = prices.iloc[-20:].pct_change().std() * np.sqrt(252)  # Annualized
            long_volatility = prices.pct_change().std() * np.sqrt(252)  # Annualized

            volatility_regime = recent_volatility / long_volatility if long_volatility > 0 else 1.0

        # Calculate momentum regime
        momentum_regime = 0.0
        if len(prices) >= 20:
            short_return = prices.iloc[-1] / prices.iloc[-5] - 1 if prices.iloc[-5] > 0 else 0
            medium_return = prices.iloc[-1] / prices.iloc[-20] - 1 if prices.iloc[-20] > 0 else 0

            momentum_regime = short_return / medium_return if abs(medium_return) > 0.001 else 0

        # Combine all market context features
        return np.array([
            distance_to_peak,
            distance_to_trough,
            peak_trough_range,
            position_in_range,
            trend_strength,
            trend_direction,
            volatility_regime,
            momentum_regime
        ])

    def _extract_time_features(self, data, idx):
        """
        Extract time-based features.

        Parameters:
        - data: DataFrame containing OHLCV data
        - idx: Index of the crossing point

        Returns:
        - numpy array of time-based features
        """
        # Default values if timestamp is not available
        hour_of_day = 0.0
        day_of_week = 0.0
        week_of_month = 0.0
        month_of_year = 0.0
        is_market_open = 1.0
        time_since_open = 0.5
        time_to_close = 0.5

        # Try to extract timestamp
        try:
            if isinstance(data.index, pd.DatetimeIndex):
                timestamp = data.index[idx]

                # Extract time components
                hour_of_day = timestamp.hour / 24.0  # Normalize to [0, 1]
                day_of_week = timestamp.dayofweek / 6.0  # Normalize to [0, 1]
                week_of_month = (timestamp.day - 1) // 7 / 4.0  # Normalize to [0, 1]
                month_of_year = (timestamp.month - 1) / 11.0  # Normalize to [0, 1]

                # Check if market is open (simplified)
                market_open = time(9, 30)
                market_close = time(16, 0)
                current_time = timestamp.time()

                is_market_open = 1.0 if market_open <= current_time <= market_close else 0.0

                # Calculate time since open and time to close
                if is_market_open > 0:
                    market_open_seconds = market_open.hour * 3600 + market_open.minute * 60
                    market_close_seconds = market_close.hour * 3600 + market_close.minute * 60
                    current_seconds = current_time.hour * 3600 + current_time.minute * 60

                    total_market_seconds = market_close_seconds - market_open_seconds

                    time_since_open = (current_seconds - market_open_seconds) / total_market_seconds
                    time_to_close = (market_close_seconds - current_seconds) / total_market_seconds
        except (AttributeError, IndexError):
            # If timestamp extraction fails, use default values
            pass

        # Combine all time-based features
        return np.array([
            hour_of_day,
            day_of_week,
            week_of_month,
            month_of_year,
            is_market_open,
            time_since_open,
            time_to_close
        ])

    # Technical indicator calculation methods

    def _calculate_rsi(self, prices, period=14):
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _calculate_macd(self, prices, fast_period=12, slow_period=26, signal_period=9):
        """Calculate MACD (Moving Average Convergence Divergence)."""
        ema_fast = prices.ewm(span=fast_period, adjust=False).mean()
        ema_slow = prices.ewm(span=slow_period, adjust=False).mean()

        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal_period, adjust=False).mean()
        macd_hist = macd - macd_signal

        return macd, macd_signal, macd_hist

    def _calculate_atr(self, highs, lows, closes, period=14):
        """Calculate Average True Range."""
        tr1 = highs - lows
        tr2 = abs(highs - closes.shift())
        tr3 = abs(lows - closes.shift())

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()

        return atr

    def _calculate_stochastic(self, highs, lows, closes, k_period=14, d_period=3):
        """Calculate Stochastic Oscillator."""
        lowest_low = lows.rolling(window=k_period).min()
        highest_high = highs.rolling(window=k_period).max()

        stoch_k = 100 * (closes - lowest_low) / (highest_high - lowest_low)
        stoch_d = stoch_k.rolling(window=d_period).mean()

        return stoch_k, stoch_d

    def _calculate_obv(self, closes, volumes):
        """Calculate On-Balance Volume."""
        # Make sure we're working with pandas Series
        closes = pd.Series(closes) if not isinstance(closes, pd.Series) else closes
        volumes = pd.Series(volumes) if not isinstance(volumes, pd.Series) else volumes

        # Create a numpy array for OBV calculation (more efficient than Series operations)
        obv_values = np.zeros(len(closes))
        obv_values[0] = volumes.iloc[0] if not volumes.empty else 0

        # Calculate OBV using numpy for better performance and to avoid pandas indexing issues
        for i in range(1, len(closes)):
            if i >= len(volumes):
                # Handle case where volumes array is shorter than closes
                obv_values[i] = obv_values[i-1]
                continue

            try:
                close_current = closes.iloc[i]
                close_prev = closes.iloc[i-1]
                volume_current = volumes.iloc[i]

                if close_current > close_prev:
                    obv_values[i] = obv_values[i-1] + volume_current
                elif close_current < close_prev:
                    obv_values[i] = obv_values[i-1] - volume_current
                else:
                    obv_values[i] = obv_values[i-1]
            except Exception as e:
                # Fallback in case of any indexing errors
                print(f"OBV calculation error at index {i}: {str(e)}")
                obv_values[i] = obv_values[i-1] if i > 0 else 0

        # Convert back to pandas Series with the same index as closes
        obv = pd.Series(obv_values, index=closes.index)
        return obv
