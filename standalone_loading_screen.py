"""
Standalone Loading Screen Application

This is a buttonless loading screen application that can be launched as a separate process
while the main application is fetching data.
"""

import sys
import os
import time
import json
from PyQt6 import QtWidgets, QtCore, QtGui

class DataDrivenIcon(QtWidgets.QWidget):
    """
    A widget that draws a data-driven icon.
    """

    def __init__(self, size=64, primary_color=None, secondary_color=None):
        """
        Initialize the data-driven icon.

        Args:
            size: Size of the icon in pixels
            primary_color: Primary color for the icon (QColor)
            secondary_color: Secondary color for the icon (QColor)
        """
        super().__init__()

        # Store parameters
        self.size = size
        self.primary_color = primary_color or QtGui.QColor("#007acc")  # Blue
        self.secondary_color = secondary_color or QtGui.QColor("#FFFFFF")  # White (changed from Green)

        # Set fixed size
        self.setFixedSize(size, size)

        # Animation properties
        self.animation_offset = 0
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)  # Update every 50ms

    def update_animation(self):
        """Update the animation."""
        self.animation_offset = (self.animation_offset + 1) % 10
        self.update()

    def paintEvent(self, _):
        """Custom paint event to draw the data-driven icon."""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

        # Draw a data visualization icon

        # Draw a chart background (rounded rectangle)
        # Adjust the rectangle to better fit the bars
        chart_rect = QtCore.QRectF(self.size * 0.1, self.size * 0.15, self.size * 0.8, self.size * 0.75)
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255, 40), 1))
        painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255, 20)))
        painter.drawRoundedRect(chart_rect, self.size * 0.1, self.size * 0.1)

        # Draw a bar chart
        bar_width = self.size * 0.1
        bar_spacing = self.size * 0.05
        # Adjust bar_bottom to move bars down a bit
        bar_bottom = self.size * 0.75

        # Calculate total width of all bars and spacing
        total_bars = 5
        total_width = (bar_width * total_bars) + (bar_spacing * (total_bars - 1))
        # Calculate starting x position to center the bars
        start_x = (self.size - total_width) / 2

        # Bar heights (percentage of chart height)
        # Add animation by cycling through different heights
        bar_heights = [
            0.3 + (0.1 if (self.animation_offset + 0) % 5 == 0 else 0),
            0.5 + (0.1 if (self.animation_offset + 1) % 5 == 0 else 0),
            0.2 + (0.1 if (self.animation_offset + 2) % 5 == 0 else 0),
            0.6 + (0.1 if (self.animation_offset + 3) % 5 == 0 else 0),
            0.4 + (0.1 if (self.animation_offset + 4) % 5 == 0 else 0)
        ]

        # Draw bars
        for i, height_pct in enumerate(bar_heights):
            bar_height = self.size * height_pct
            bar_x = start_x + i * (bar_width + bar_spacing)
            bar_y = bar_bottom - bar_height

            # Alternate colors
            if i % 2 == 0:
                painter.setBrush(QtGui.QBrush(self.primary_color))
            else:
                painter.setBrush(QtGui.QBrush(self.secondary_color))

            painter.setPen(QtCore.Qt.PenStyle.NoPen)
            painter.drawRoundedRect(QtCore.QRectF(bar_x, bar_y, bar_width, bar_height),
                                   self.size * 0.02, self.size * 0.02)

        # Draw a trend line
        painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255, 200), 2))

        # Create points for the line
        points = []
        for i, height_pct in enumerate(bar_heights):
            point_x = start_x + i * (bar_width + bar_spacing) + bar_width / 2
            # Adjust point_y to match new bar positions
            point_y = bar_bottom - (self.size * height_pct) - (self.size * 0.03)
            points.append(QtCore.QPointF(point_x, point_y))

        # Draw the line connecting the points
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])

        # Draw points at each data point
        for point in points:
            painter.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255)))
            painter.drawEllipse(point, self.size * 0.03, self.size * 0.03)

class StandaloneLoadingScreen(QtWidgets.QWidget):
    """
    A standalone loading screen application that can be launched as a separate process.
    """

    def __init__(self, message="Loading chart data...", position=None, size=None):
        """
        Initialize the loading screen.

        Args:
            message: Message to display
            position: Position of the window (x, y)
            size: Size of the window (width, height)
        """
        super().__init__()

        # Store parameters
        self.message = message

        # Theme colors
        self.theme_colors = {
            'background': '#1e1e1e',
            'text': '#E0E0E0',
            'primary_accent': '#007acc'
        }

        # Set window flags
        self.setWindowFlags(
            QtCore.Qt.WindowType.FramelessWindowHint |  # No window frame
            QtCore.Qt.WindowType.WindowStaysOnTopHint |  # Stay on top
            QtCore.Qt.WindowType.Tool  # Don't show in taskbar
        )

        # Set window opacity - removed transparency
        self.setWindowOpacity(1.0)

        # Set position and size
        if position and size:
            self.setGeometry(position[0], position[1], size[0], size[1])
        else:
            # Default size - increased for better visibility when fullscreened
            self.resize(500, 400)
            # Center on screen
            self.center_on_screen()

        # Set up the UI
        self.init_ui()

        # Animation properties
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.check_status_file)
        self.animation_timer.start(100)  # Check status every 100ms

        # Status file path
        self.status_file = "loading_status.json"

    def init_ui(self):
        """Initialize the user interface."""
        # Set up appearance
        self.setStyleSheet(f"background-color: {self.theme_colors['background']}; border-radius: 10px;")

        # Create layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)  # Increased margins
        layout.setSpacing(25)  # Increased spacing
        layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)  # Center everything

        # Add data-driven icon - increased size for better visibility
        self.data_icon = DataDrivenIcon(
            size=160,  # Increased from 120 to 160
            primary_color=QtGui.QColor(self.theme_colors['primary_accent']),
            secondary_color=QtGui.QColor("#FFFFFF")  # White (changed from Green)
        )
        layout.addWidget(self.data_icon, 0, QtCore.Qt.AlignmentFlag.AlignCenter)

        # Add a label for "DATA DRIVEN"
        data_driven_label = QtWidgets.QLabel("DATA DRIVEN")
        data_driven_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        data_driven_label.setStyleSheet(f"""
            color: {self.theme_colors['text']};
            font-size: 28px;  # Increased from 22px
            font-weight: bold;
            margin-top: 15px;
        """)
        layout.addWidget(data_driven_label)

        # Message label
        self.message_label = QtWidgets.QLabel(self.message)
        self.message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.message_label.setStyleSheet(f"""
            color: {self.theme_colors['text']};
            font-size: 22px;  # Increased from 18px
            margin-top: 20px;
        """)
        layout.addWidget(self.message_label)

    def center_on_screen(self):
        """Center the window on the screen."""
        screen_geometry = QtWidgets.QApplication.primaryScreen().geometry()
        window_geometry = self.geometry()

        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2

        self.move(x, y)

    def set_message(self, message):
        """Update the loading message."""
        self.message = message
        self.message_label.setText(message)

    def check_status_file(self):
        """Check the status file to see if loading is complete."""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r') as f:
                    status = json.load(f)

                # Update message if provided
                if 'message' in status:
                    self.set_message(status['message'])

                # Check if loading is complete
                if status.get('status') == 'complete':
                    # Close the application
                    self.close()
                    QtWidgets.QApplication.quit()
        except Exception as e:
            print(f"Error checking status file: {str(e)}")

    def closeEvent(self, event):
        """Handle close event."""
        # Clean up status file
        if os.path.exists(self.status_file):
            try:
                os.remove(self.status_file)
            except:
                pass

        event.accept()

def main():
    """Main function."""
    # Hide console window on Windows
    if os.name == 'nt':
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
        except:
            pass

    app = QtWidgets.QApplication(sys.argv)

    # Get command line arguments
    message = "Loading chart data..."
    position = None
    size = None

    # Parse command line arguments
    for i, arg in enumerate(sys.argv):
        if arg == "--message" and i + 1 < len(sys.argv):
            message = sys.argv[i + 1]
        elif arg == "--position" and i + 2 < len(sys.argv):
            try:
                x = int(sys.argv[i + 1])
                y = int(sys.argv[i + 2])
                position = (x, y)
            except:
                pass
        elif arg == "--size" and i + 2 < len(sys.argv):
            try:
                width = int(sys.argv[i + 1])
                height = int(sys.argv[i + 2])
                size = (width, height)
            except:
                pass

    # Create and show the loading screen
    loading_screen = StandaloneLoadingScreen(message, position, size)
    loading_screen.show()

    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
