"""
Candlestick Chart Module

This module provides classes for rendering candlestick charts and volume profiles
using PyQtGraph, as well as a complete CandlestickChart widget.

It also includes a VectorItem class that implements the PineScript version of 'The Line'
with background coloring (green when rising, red when falling).
"""

import numpy as np
import pandas as pd
import yfinance as yf
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
from datetime import datetime, timedelta
import traceback
import sys
import os

# Add the parent directory to sys.path to ensure vector module can be imported
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class PriceBox(pg.TextItem):
    """A text box with a background for displaying price information."""
    def __init__(self, text, color='white', border_color='gray'):
        super().__init__(text, color=color)
        self.border_color = border_color
        self.bg_color = '#2b2b2b'
        # Set HTML formatting for better text appearance
        self.setHtml(text)
        # Add padding to the text
        self.setTextWidth(200)

    def paint(self, p, *args):
        # Draw a semi-transparent background with rounded corners
        p.setPen(pg.mkPen(self.border_color, width=1.5))
        p.setBrush(pg.mkBrush(QtGui.QColor(43, 43, 43, 230)))  # Semi-transparent dark background

        # Get the bounding rectangle and add some padding
        bound = self.boundingRect()
        # Add padding (5 pixels on each side)
        padded_rect = QtCore.QRectF(
            bound.x() - 5,
            bound.y() - 5,
            bound.width() + 10,
            bound.height() + 10
        )

        # Draw rounded rectangle
        p.drawRoundedRect(padded_rect, 5, 5)

        # Draw the text
        super().paint(p, *args)

    def setText(self, text):
        """Override setText to use HTML formatting for better appearance"""
        # Format the text with HTML for better appearance
        html_text = f"<div style='font-family: monospace; font-size: 12pt;'>{text}</div>"
        self.setHtml(html_text)


# No need for a custom CrosshairLabel class anymore


class VectorItem(pg.GraphicsObject):
    def __init__(self, data, vector_values, wave_length=10,
                 line_color='purple', line_width=2,
                 bg_up_color=(0, 100, 0, 15), bg_down_color=(139, 0, 0, 15), data_already_processed=False):
        pg.GraphicsObject.__init__(self)
        # Store the data and The Line values
        self.data = data  # List of (t, open, high, low, close)
        self.vector_values = vector_values  # List of The Line values
        self.wave_length = wave_length

        # Style settings
        self.line_color = line_color
        self.line_width = line_width
        self.bg_up_color = bg_up_color
        self.bg_down_color = bg_down_color

        # Direction tracking - this matches market_odds.py logic
        self.current_direction = None

        # Flag to indicate if data is already processed (from market_odds.py)
        self.data_already_processed = data_already_processed

        # Generate the picture
        self.generatePicture()

    def generatePicture(self):
        """Generate The Line and background coloring."""
        self.picture = QtGui.QPicture()
        p = QtGui.QPainter(self.picture)

        # Skip if no data
        if not self.data or not self.vector_values or len(self.data) != len(self.vector_values):
            p.end()
            return

        # Create arrays for x and y coordinates
        x_values = [item[0] for item in self.data]

        # Draw background coloring based on vector direction
        # This exactly matches the PineScript logic where each bar gets its own background color
        # based on whether the vector is rising or falling

        # In PineScript:
        # currentBg := wavePrice > wavePrice[1] ? fillUp : wavePrice < wavePrice[1] ? fillDown : currentBg

        # Initialize background colors array (equivalent to 'var color currentBg = na' in PineScript)

        # Calculate chart boundaries once (for efficiency)
        chart_high = max([item[2] for item in self.data])
        chart_low = min([item[3] for item in self.data])
        price_range = chart_high - chart_low

        # Add margins to chart boundaries
        y_min = chart_low - price_range * 0.05  # Lowest price with margin
        y_max = chart_high + price_range * 0.05  # Highest price with margin

        # In TradingView/PineScript, the background color is applied to the current bar
        # based on the comparison between the current and previous vector values

        # First, determine the background color for each bar
        bar_colors = [None] * len(self.vector_values)

        # Track vector direction for each bar - this matches market_odds.py logic
        directions = [None] * len(self.vector_values)

        # Set initial color and direction based on first two vector values (if available)
        if len(self.vector_values) > 1:
            if self.vector_values[1] > self.vector_values[0]:
                # Vector rising - green background (above pivot in market odds)
                bar_colors[0] = self.bg_up_color
                directions[0] = 'up'
                self.current_direction = 'up'
            elif self.vector_values[1] < self.vector_values[0]:
                # Vector falling - red background (below pivot in market odds)
                bar_colors[0] = self.bg_down_color
                directions[0] = 'down'
                self.current_direction = 'down'

        for i in range(1, len(self.vector_values)):
            # Compare current Line value with previous value
            if self.vector_values[i] > self.vector_values[i-1]:
                # The Line rising - green background (above pivot in market odds)
                bar_colors[i] = self.bg_up_color
                directions[i] = 'up'
                self.current_direction = 'up'
            elif self.vector_values[i] < self.vector_values[i-1]:
                # The Line falling - red background (below pivot in market odds)
                bar_colors[i] = self.bg_down_color
                directions[i] = 'down'
                self.current_direction = 'down'
            else:
                # The Line flat - maintain previous color and direction
                bar_colors[i] = bar_colors[i-1]
                directions[i] = directions[i-1] if directions[i-1] is not None else 'up'
                self.current_direction = directions[i]

        # Debug print to check if colors are being assigned
        print(f"Vector values: {self.vector_values[:5]}")
        print(f"Bar colors: {[c is not None for c in bar_colors[:5]]}")

        # Now draw the background for each bar
        candle_width = 1.0

        # Debug print to check if we're entering the drawing loop
        print(f"Drawing backgrounds for {len(self.vector_values)} bars")

        if not self.data_already_processed:
            # Get dynamic skip value from parameter registry only if data is not already processed
            try:
                from parameter_registry import default_registry
                vector_length = default_registry.get_value('vector_length')
                skip_candles = vector_length + 1
            except:
                skip_candles = 2  # Fallback to static 2 if parameter registry is not available
        else:
            skip_candles = 0  # No skipping needed for already processed data

        for i in range(len(self.vector_values)):
            # Make the first skip_candles vector backgrounds invisible (skip rendering them)
            if i < skip_candles:
                continue

            # Skip if we don't have a background color for this bar
            if bar_colors[i] is None:
                continue

            # Calculate bar coordinates aligned with candle borders
            # In TradingView, the background aligns with the candle borders
            x1 = x_values[i] - candle_width/2  # Left edge of current candle
            x2 = x_values[i] + candle_width/2  # Right edge of current candle

            # Debug print for the first few bars
            if i < 5:
                print(f"Bar {i}: x1={x1}, x2={x2}, color={bar_colors[i] is not None}")

            # In PineScript, each bar gets its own background that extends to the top and bottom of the chart
            # We use the pre-calculated chart boundaries with margins

            # Draw the background for this specific bar
            p.setPen(pg.mkPen(None))  # No border
            p.setBrush(pg.mkBrush(bar_colors[i]))

            # Draw a rectangle for this bar that extends from top to bottom of the chart
            # This matches PineScript's behavior where each bar has its own background
            p.drawRect(QtCore.QRectF(x1, y_min, x2-x1, y_max-y_min))

            # Debug print for the first few rectangles
            if i < 5:
                print(f"Drawing rect at ({x1}, {y_min}) with width {x2-x1} and height {y_max-y_min}")

        # Draw The Line as a step line (like in PineScript)
        p.setPen(pg.mkPen(self.line_color, width=self.line_width))

        # Create a path for the step line instead of individual line segments
        # This provides better rendering and avoids the "thick line" issue
        path = QtGui.QPainterPath()

        # In TradingView/PineScript, The Line is drawn as a step line
        # that aligns with the borders of the candles, not the middle

        # Calculate candle width (assuming uniform spacing)
        candle_width = 1.0

        # Start the path at index skip_candles (make first skip_candles vector points invisible)
        if len(self.vector_values) > skip_candles:
            # Adjust x position to align with left edge of the candle at index skip_candles
            start_x = x_values[skip_candles] - candle_width/2
            path.moveTo(QtCore.QPointF(start_x, self.vector_values[skip_candles]))

            # Add step segments to the path starting from index skip_candles + 1
            for i in range(skip_candles + 1, len(self.vector_values)):
                # Calculate x position aligned with candle border
                curr_x = x_values[i] - candle_width/2    # Left edge of current candle

                # Add horizontal segment to right edge of previous candle
                path.lineTo(QtCore.QPointF(curr_x, self.vector_values[i-1]))

                # Add vertical segment at the current candle
                path.lineTo(QtCore.QPointF(curr_x, self.vector_values[i]))

        # Draw the path
        p.drawPath(path)

        p.end()

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return QtCore.QRectF(self.picture.boundingRect())

    def get_current_direction(self):
        """Return the current Line direction ('up' or 'down').

        This method allows other components (like market_odds.py) to access
        the current Line direction to ensure consistent behavior between
        the candlestick chart and market odds tab.

        Returns:
            str: 'up' if The Line is rising (green background), 'down' if falling (red background)
        """
        return self.current_direction


class CandlestickItem(pg.GraphicsObject):
    """Custom PyQtGraph item for rendering candlestick charts with signal markers."""
    def __init__(self, data, bullish_color='g', bearish_color='r', signals=None, show_signals=True, chart_colors=None, show_pullbacks=True, show_reversals=True, data_already_processed=False):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.bullish_color = bullish_color
        self.bearish_color = bearish_color
        self.signals = signals or {}  # Dictionary mapping candle index to signal type ('BullishPullback', 'BearishPullback', 'BullishReversal', 'BearishReversal')
        self.show_signals = show_signals
        self.show_pullbacks = show_pullbacks
        self.show_reversals = show_reversals
        self.chart_colors = chart_colors or {}
        self.signal_markers = {}
        self.signal_positions = {}  # Dictionary to store signal positions for tooltip detection
        self.data_already_processed = data_already_processed  # Flag to indicate if data is already processed (from market_odds.py)
        self.generatePicture()

    def generatePicture(self):
        self.picture = QtGui.QPicture()
        p = QtGui.QPainter(self.picture)
        w = 0.4
        if not self.data_already_processed:
            # Get dynamic skip value from parameter registry only if data is not already processed
            try:
                from parameter_registry import default_registry
                vector_length = default_registry.get_value('vector_length')
                skip_candles = vector_length + 1
            except:
                skip_candles = 2  # Fallback to static 2 if parameter registry is not available
        else:
            skip_candles = 0  # No skipping needed for already processed data

        for (t, open, high, low, close) in self.data:
            # Make the first skip_candles candles invisible (skip rendering them)
            if t < skip_candles:
                continue

            if close >= open:
                p.setPen(pg.mkPen(self.bullish_color))
                p.setBrush(pg.mkBrush(self.bullish_color))
            else:
                p.setPen(pg.mkPen(self.bearish_color))
                p.setBrush(pg.mkBrush(self.bearish_color))
            p.drawLine(QtCore.QPointF(t, low), QtCore.QPointF(t, high))
            p.drawRect(QtCore.QRectF(t - w, open, w * 2, close - open))

            # Draw signal markers directly in the picture
            if self.show_signals and t in self.signals:
                signal_type = self.signals[t]
                # Check if this signal type should be shown based on user preferences
                is_pullback = any(pullback_type in signal_type for pullback_type in ['Pullback', 'BullishPullback', 'BearishPullback'])
                is_reversal = any(reversal_type in signal_type for reversal_type in ['Reversal', 'BullishReversal', 'BearishReversal'])

                if (is_pullback and not self.show_pullbacks) or (is_reversal and not self.show_reversals):
                    continue  # Skip this signal if its type is not enabled

                # Determine if candle is above or below pivot (0 line)
                is_above_pivot = close >= 0

                # Set position based on whether candle is above or below pivot
                if is_above_pivot:
                    # Position above the candle
                    signal_y = high + 0.5  # Above the high point
                else:
                    # Position below the candle
                    signal_y = low - 0.5   # Below the low point

                # Set color and shape based on signal type
                signal_info = {
                    'type': signal_type,
                    'time': t,
                    'price': close,
                    'position': (t, signal_y)
                }

                if 'BullishPullback' in signal_type:
                    color = self.chart_colors.get('bullish_pullback', '#00BCD4')
                    # Draw triangle pointing up with glow
                    self.draw_triangle_with_glow(p, t, signal_y, color, pointing_up=True)
                    signal_info['description'] = "Bullish Pullback Signal"
                    signal_info['color'] = color
                elif 'BearishPullback' in signal_type:
                    color = self.chart_colors.get('bearish_pullback', '#FF5722')
                    # Draw triangle pointing down with glow
                    self.draw_triangle_with_glow(p, t, signal_y, color, pointing_up=False)
                    signal_info['description'] = "Bearish Pullback Signal"
                    signal_info['color'] = color
                elif 'BullishReversal' in signal_type:
                    color = self.chart_colors.get('bullish_reversal', '#4CAF50')
                    # Draw circle with glow
                    self.draw_circle_with_glow(p, t, signal_y, color)
                    signal_info['description'] = "Bullish Reversal Signal"
                    signal_info['color'] = color
                elif 'BearishReversal' in signal_type:
                    color = self.chart_colors.get('bearish_reversal', '#F44336')
                    # Draw circle with glow
                    self.draw_circle_with_glow(p, t, signal_y, color)
                    signal_info['description'] = "Bearish Reversal Signal"
                    signal_info['color'] = color
                elif 'Pullback' in signal_type:  # Legacy type
                    color = self.chart_colors.get('pullback', '#00BCD4')
                    # Draw triangle pointing up with glow
                    self.draw_triangle_with_glow(p, t, signal_y, color, pointing_up=True)
                    signal_info['description'] = "Pullback Signal"
                    signal_info['color'] = color
                elif 'Reversal' in signal_type:  # Legacy type
                    color = self.chart_colors.get('reversal', '#F44336')
                    # Draw circle with glow
                    self.draw_circle_with_glow(p, t, signal_y, color)
                    signal_info['description'] = "Reversal Signal"
                    signal_info['color'] = color

                # Store signal position and info for tooltip detection
                # Use a unique key with a small offset to ensure it's detectable
                self.signal_positions[(t, signal_y)] = signal_info
                print(f"Added signal at ({t}, {signal_y}): {signal_info['description']}")
        p.end()

    def draw_circle_with_glow(self, painter, x, y, color):
        """Draw a circle with glow effect."""
        try:
            # Parse the color
            qcolor = QtGui.QColor(color)

            # Create a semi-transparent version for the glow
            glow_color = QtGui.QColor(qcolor)

            # Outer glow (most transparent)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)
            glow_color.setAlphaF(0.3)  # 30% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawEllipse(QtCore.QPointF(x, y), 0.6, 0.6)  # Largest radius for outer glow

            # Middle glow
            glow_color.setAlphaF(0.45)  # 45% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawEllipse(QtCore.QPointF(x, y), 0.5, 0.5)  # Medium-large radius for middle glow

            # Inner glow
            glow_color.setAlphaF(0.6)  # 60% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawEllipse(QtCore.QPointF(x, y), 0.4, 0.4)  # Medium radius for inner glow

            # Main circle
            painter.setPen(pg.mkPen(color, width=1))
            painter.setBrush(QtGui.QBrush(qcolor))
            painter.drawEllipse(QtCore.QPointF(x, y), 0.3, 0.3)  # Normal radius for main circle
        except Exception as e:
            print(f"Error drawing circle with glow: {str(e)}")

    def draw_triangle_with_glow(self, painter, x, y, color, pointing_up=True):
        """Draw a triangle with glow effect."""
        try:
            # Parse the color
            qcolor = QtGui.QColor(color)

            # Create a semi-transparent version for the glow
            glow_color = QtGui.QColor(qcolor)
            glow_color.setAlphaF(0.4)  # 40% opacity

            # Define triangle points based on direction
            if pointing_up:
                # Triangle pointing up
                main_points = [
                    QtCore.QPointF(x, y - 0.3),  # Top
                    QtCore.QPointF(x - 0.3, y + 0.3),  # Bottom left
                    QtCore.QPointF(x + 0.3, y + 0.3)   # Bottom right
                ]
                # Create more even glow by using proportional scaling
                outer_glow_points = [
                    QtCore.QPointF(x, y - 0.6),  # Top
                    QtCore.QPointF(x - 0.6, y + 0.6),  # Bottom left
                    QtCore.QPointF(x + 0.6, y + 0.6)   # Bottom right
                ]
                middle_glow_points = [
                    QtCore.QPointF(x, y - 0.5),  # Top
                    QtCore.QPointF(x - 0.5, y + 0.5),  # Bottom left
                    QtCore.QPointF(x + 0.5, y + 0.5)   # Bottom right
                ]
                inner_glow_points = [
                    QtCore.QPointF(x, y - 0.4),  # Top
                    QtCore.QPointF(x - 0.4, y + 0.4),  # Bottom left
                    QtCore.QPointF(x + 0.4, y + 0.4)   # Bottom right
                ]
            else:
                # Triangle pointing down
                main_points = [
                    QtCore.QPointF(x, y + 0.3),  # Bottom
                    QtCore.QPointF(x - 0.3, y - 0.3),  # Top left
                    QtCore.QPointF(x + 0.3, y - 0.3)   # Top right
                ]
                # Create more even glow by using proportional scaling
                outer_glow_points = [
                    QtCore.QPointF(x, y + 0.6),  # Bottom
                    QtCore.QPointF(x - 0.6, y - 0.6),  # Top left
                    QtCore.QPointF(x + 0.6, y - 0.6)   # Top right
                ]
                middle_glow_points = [
                    QtCore.QPointF(x, y + 0.5),  # Bottom
                    QtCore.QPointF(x - 0.5, y - 0.5),  # Top left
                    QtCore.QPointF(x + 0.5, y - 0.5)   # Top right
                ]
                inner_glow_points = [
                    QtCore.QPointF(x, y + 0.4),  # Bottom
                    QtCore.QPointF(x - 0.4, y - 0.4),  # Top left
                    QtCore.QPointF(x + 0.4, y - 0.4)   # Top right
                ]

            # Draw outer glow (most transparent)
            painter.setPen(QtCore.Qt.PenStyle.NoPen)
            glow_color.setAlphaF(0.3)  # 30% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawPolygon(QtGui.QPolygonF(outer_glow_points))

            # Draw middle glow
            glow_color.setAlphaF(0.45)  # 45% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawPolygon(QtGui.QPolygonF(middle_glow_points))

            # Draw inner glow
            glow_color.setAlphaF(0.6)  # 60% opacity
            painter.setBrush(QtGui.QBrush(glow_color))
            painter.drawPolygon(QtGui.QPolygonF(inner_glow_points))

            # Draw main triangle
            painter.setPen(pg.mkPen(color, width=1))
            painter.setBrush(QtGui.QBrush(qcolor))
            painter.drawPolygon(QtGui.QPolygonF(main_points))
        except Exception as e:
            print(f"Error drawing triangle with glow: {str(e)}")

    def updateColors(self, bullish_color, bearish_color):
        self.bullish_color = bullish_color
        self.bearish_color = bearish_color
        self.generatePicture()
        self.update()

    def updateSignals(self, signals):
        try:
            self.signals = signals
            self.signal_positions = {}  # Clear signal positions
            self.generatePicture()
            self.update()
        except Exception as e:
            print(f"Error in updateSignals: {str(e)}")

    def is_near_signal(self, pos, threshold=1.0):
        """Check if position is near a signal marker"""
        try:
            x, y = pos.x(), pos.y()
            # Print for debugging
            print(f"Mouse position: ({x:.2f}, {y:.2f})")
            print(f"Number of signals: {len(self.signal_positions)}")

            for (signal_x, signal_y), signal_info in self.signal_positions.items():
                # Calculate distance
                distance = ((signal_x - x) ** 2 + (signal_y - y) ** 2) ** 0.5
                print(f"Signal at ({signal_x:.2f}, {signal_y:.2f}), distance: {distance:.2f}")
                if distance <= threshold:
                    print(f"Signal detected! Type: {signal_info['description']}")
                    return signal_info
            return None
        except Exception as e:
            print(f"Error checking signal proximity: {str(e)}")
            return None

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return QtCore.QRectF(self.picture.boundingRect())


class DrawingTools:
    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.current_tool = None
        self.drawing_items = []
        self.current_item = None
        self.start_pos = None
        self.drawing = False
        self.selected_item = None
        self.moving_item = False
        self.item_labels = {}
        self.colors = {
            'White': '#FFFFFF',
            'Red': '#FF0000',
            'Green': '#00FF00',
            'Blue': '#0000FF',
            'Yellow': '#FFFF00',
            'Magenta': '#FF00FF',
            'Cyan': '#00FFFF',
            'Orange': '#FFA500'
        }

    def start_drawing(self, pos, tool_type):
        self.current_tool = tool_type
        self.start_pos = pos
        self.drawing = True
        if tool_type == 'line':
            self.current_item = pg.PlotDataItem(pen=pg.mkPen('w', width=2))
        elif tool_type == 'rectangle':
            self.current_item = pg.PlotDataItem(pen=pg.mkPen('w', width=2))
        elif tool_type == 'horizontal':
            self.current_item = pg.InfiniteLine(angle=0, movable=True, pen=pg.mkPen('w', width=2))
        elif tool_type == 'vertical':
            self.current_item = pg.InfiniteLine(angle=90, movable=True, pen=pg.mkPen('w', width=2))
        elif tool_type == 'text':
            self.current_item = pg.TextItem(text="", color='w')
        if self.current_item:
            self.plot_widget.addItem(self.current_item)

    def update_drawing(self, pos):
        if not self.drawing or not self.current_item:
            return
        if self.current_tool == 'line':
            self.current_item.setData([self.start_pos.x(), pos.x()], [self.start_pos.y(), pos.y()])
        elif self.current_tool == 'rectangle':
            x = [self.start_pos.x(), self.start_pos.x(), pos.x(), pos.x(), self.start_pos.x()]
            y = [self.start_pos.y(), pos.y(), pos.y(), self.start_pos.y(), self.start_pos.y()]
            self.current_item.setData(x, y)
        elif self.current_tool == 'horizontal':
            self.current_item.setPos(pos.y())
        elif self.current_tool == 'vertical':
            self.current_item.setPos(pos.x())
        elif self.current_tool == 'text':
            self.current_item.setPos(pos.x(), pos.y())

    def finish_drawing(self, pos=None):
        if self.current_item:
            if pos:
                self.update_drawing(pos)
            self.drawing_items.append(self.current_item)
        self.current_item = None
        self.start_pos = None
        self.drawing = False

    def add_text(self, pos, text):
        text_item = pg.TextItem(text=text, color='w')
        text_item.setPos(pos.x(), pos.y())
        self.plot_widget.addItem(text_item)
        self.drawing_items.append(text_item)

    def select_item(self, pos):
        for item in self.drawing_items:
            if isinstance(item, pg.PlotDataItem):
                data = item.getData()
                if data[0] is not None:
                    x, y = data
                    for i in range(len(x)-1):
                        if self._point_near_line(pos, QtCore.QPointF(x[i], y[i]), QtCore.QPointF(x[i+1], y[i+1])):
                            self.selected_item = item
                            return True
            elif isinstance(item, pg.InfiniteLine):
                if abs(item.value() - (pos.y() if item.angle == 0 else pos.x())) < 5:
                    self.selected_item = item
                    return True
            elif isinstance(item, pg.TextItem):
                if abs(item.pos().x() - pos.x()) < 5 and abs(item.pos().y() - pos.y()) < 5:
                    self.selected_item = item
                    return True
        return False

    def _point_near_line(self, point, line_start, line_end):
        # Calculate distance from point to line segment
        line_len = ((line_end.x() - line_start.x())**2 + (line_end.y() - line_start.y())**2)**0.5
        if line_len == 0:
            return False

        # Calculate distance from point to line
        dist = abs((line_end.y() - line_start.y()) * point.x() -
                  (line_end.x() - line_start.x()) * point.y() +
                  line_end.x() * line_start.y() -
                  line_end.y() * line_start.x()) / line_len

        # Check if point is near the line
        return dist < 5

    def change_item_color(self, item, color):
        if isinstance(item, pg.PlotDataItem):
            item.setPen(pg.mkPen(color, width=2))
        elif isinstance(item, pg.InfiniteLine):
            item.setPen(pg.mkPen(color, width=2))
        elif isinstance(item, pg.TextItem):
            item.setColor(color)

    def delete_item(self, item):
        if item in self.drawing_items:
            self.plot_widget.removeItem(item)
            self.drawing_items.remove(item)
            if item in self.item_labels:
                self.plot_widget.removeItem(self.item_labels[item])
                del self.item_labels[item]
        self.selected_item = None

    def clear_all(self):
        for item in self.drawing_items:
            self.plot_widget.removeItem(item)
            if item in self.item_labels:
                self.plot_widget.removeItem(self.item_labels[item])
        self.drawing_items = []
        self.item_labels = {}
        self.selected_item = None

    def add_label_to_item(self, item, text):
        if item in self.item_labels:
            label = self.item_labels[item]
            label.setText(text)
        else:
            if isinstance(item, pg.PlotDataItem):
                data = item.getData()
                if data[0] is not None:
                    x, y = data
                    mid_x = (x[0] + x[-1]) / 2
                    mid_y = (y[0] + y[-1]) / 2
                    label = pg.TextItem(text=text, color='w', anchor=(0.5, 0.5))
                    label.setPos(mid_x, mid_y)
                    self.plot_widget.addItem(label)
                    self.item_labels[item] = label
            elif isinstance(item, pg.InfiniteLine):
                if item.angle == 0:  # Horizontal line
                    label = pg.TextItem(text=text, color='w', anchor=(0, 0.5))
                    label.setPos(self.plot_widget.getViewBox().viewRange()[0][0], item.value())
                else:  # Vertical line
                    label = pg.TextItem(text=text, color='w', anchor=(0.5, 0))
                    label.setPos(item.value(), self.plot_widget.getViewBox().viewRange()[1][0])
                self.plot_widget.addItem(label)
                self.item_labels[item] = label


class VolumeProfileItem(pg.GraphicsObject):
    """Custom pyqtgraph item for rendering volume profile"""
    def __init__(self, data, price_range, num_bars=200, bar_thickness=1, bar_length_mult=20,
                 right_offset=70, volume_type='Both', bar_color=(100, 100, 100, 128),
                 display_poc=True, poc_line_color='r', poc_line_thickness=1,
                 display_va=True, va_percent=68, va_bar_color=(0, 0, 255, 128),
                 display_va_lines=True, va_lines_thickness=1, right_aligned=True):
        pg.GraphicsObject.__init__(self)
        # Store the candlestick data (time, open, high, low, close, volume)
        self.data = data
        # Price range to display the volume profile
        self.highest_price, self.lowest_price = price_range
        # Volume profile settings
        self.num_bars = num_bars
        self.bar_thickness = bar_thickness
        self.bar_length_mult = bar_length_mult
        self.right_offset = right_offset
        self.volume_type = volume_type
        self.bar_color = bar_color
        # Point of Control settings
        self.display_poc = display_poc
        self.poc_line_color = poc_line_color
        self.poc_line_thickness = poc_line_thickness
        # Value Area settings
        self.display_va = display_va
        self.va_percent = va_percent
        self.va_bar_color = va_bar_color
        self.display_va_lines = display_va_lines
        self.va_lines_thickness = va_lines_thickness
        # Position setting
        self.right_aligned = right_aligned

        # Calculate price interval
        self.price_interval = (self.highest_price - self.lowest_price) / (self.num_bars - 1)

        # Create array to store volumes at each price level
        self.volumes = np.zeros(self.num_bars)

        # Calculate volume profile
        self.calculate_vp()
        self.generatePicture()

    def calculate_vp(self):
        """Calculate volume at each price level"""
        self.volumes.fill(0)

        for i in range(len(self.data)):
            t, open_price, high, low, close, volume = self.data[i]

            # Determine if bullish or bearish candle
            is_bullish = close >= open_price

            # Check if we should include this volume based on volume type
            include_vol = (self.volume_type == 'Both' or
                          (self.volume_type == 'Bullish' and is_bullish) or
                          (self.volume_type == 'Bearish' and not is_bullish))

            if include_vol:
                # Distribute volume across price levels within candle's range
                for j in range(self.num_bars):
                    price_level = self.lowest_price + self.price_interval * j
                    if low <= price_level < high:
                        self.volumes[j] += volume

    def calculate_va(self):
        """Calculate Value Area (VA) based on percentage of total volume"""
        max_idx = np.argmax(self.volumes)
        max_vol = self.volumes[max_idx]

        sum_vol = np.sum(self.volumes)
        va_vol = sum_vol * self.va_percent / 100

        va_up = max_idx
        va_dn = max_idx
        va_sum = max_vol

        while va_sum < va_vol and (va_up < self.num_bars - 1 or va_dn > 0):
            v_up = self.volumes[va_up + 1] if va_up < self.num_bars - 1 else 0
            v_dn = self.volumes[va_dn - 1] if va_dn > 0 else 0

            if v_up == 0 and v_dn == 0:
                break

            if v_up >= v_dn:
                va_sum += v_up
                va_up += 1
            else:
                va_sum += v_dn
                va_dn -= 1

        return va_dn, va_up, max_idx

    def generatePicture(self):
        self.picture = QtGui.QPicture()
        p = QtGui.QPainter(self.picture)

        # Get max volume for scaling
        max_vol = np.max(self.volumes)
        if max_vol == 0:
            p.end()
            return

        # Calculate Value Area range
        va_dn, va_up, max_idx = self.calculate_va()

        # Find the maximum x-coordinate in the data
        max_x = 0
        for i in range(len(self.data)):
            if self.data[i][0] > max_x:
                max_x = self.data[i][0]

        # Calculate x position for bars
        if self.right_aligned:
            # Position the profile at the right edge with small offset
            bar_width = self.bar_length_mult
            x_right = max_x + 0.5  # Right edge of chart with small offset
            x_left = x_right - bar_width  # Left edge of the profile
        else:
            # Original positioning
            x_right = max_x + self.right_offset

        # Draw volume bars
        for i in range(self.num_bars):
            # Scale volume
            vol_scaled = self.volumes[i] / max_vol * self.bar_length_mult

            # Skip empty volume bars
            if vol_scaled == 0:
                continue

            # Calculate coordinates
            if self.right_aligned:
                x1 = x_left
                x2 = x1 + vol_scaled
            else:
                x1 = x_right - vol_scaled
                x2 = x_right

            y = self.lowest_price + self.price_interval * i

            # Set pen and brush
            if self.display_va and va_dn <= i <= va_up:
                if i == max_idx and self.display_poc:
                    p.setPen(pg.mkPen(self.poc_line_color, width=self.bar_thickness))
                    p.setBrush(pg.mkBrush(self.poc_line_color))
                else:
                    p.setPen(pg.mkPen(self.va_bar_color, width=self.bar_thickness))
                    p.setBrush(pg.mkBrush(self.va_bar_color))
            else:
                if i == max_idx and self.display_poc:
                    p.setPen(pg.mkPen(self.poc_line_color, width=self.bar_thickness))
                    p.setBrush(pg.mkBrush(self.poc_line_color))
                else:
                    p.setPen(pg.mkPen(self.bar_color, width=self.bar_thickness))
                    p.setBrush(pg.mkBrush(self.bar_color))

            # Draw horizontal line for volume bar
            p.drawLine(QtCore.QPointF(x1, y), QtCore.QPointF(x2, y))

        # Draw POC (Point of Control) line
        if self.display_poc:
            y_poc = self.lowest_price + self.price_interval * max_idx

            if self.right_aligned:
                # Draw a shorter POC line to the left
                poc_x1 = x_left - 10  # Left of the volume profile
                poc_x2 = x_left       # Just to the edge of the profile
            else:
                poc_x1 = 0
                poc_x2 = x_right - (self.volumes[max_idx] / max_vol * self.bar_length_mult) - 10

            p.setPen(pg.mkPen(self.poc_line_color, width=self.poc_line_thickness))
            p.drawLine(QtCore.QPointF(poc_x1, y_poc), QtCore.QPointF(poc_x2, y_poc))

        # Draw VA (Value Area) lines
        if self.display_va and self.display_va_lines:
            # Value Area High
            y_vah = self.lowest_price + self.price_interval * va_up

            if self.right_aligned:
                # Draw a shorter VA lines to the left
                vah_x1 = x_left - 10  # Left of the volume profile
                vah_x2 = x_left       # Just to the edge of the profile
            else:
                vah_x1 = 0
                vah_x2 = x_right - (self.volumes[va_up] / max_vol * self.bar_length_mult) - 10

            # Value Area Low
            y_val = self.lowest_price + self.price_interval * va_dn

            if self.right_aligned:
                # Draw a shorter VA lines to the left
                val_x1 = x_left - 10  # Left of the volume profile
                val_x2 = x_left       # Just to the edge of the profile
            else:
                val_x1 = 0
                val_x2 = x_right - (self.volumes[va_dn] / max_vol * self.bar_length_mult) - 10

            p.setPen(pg.mkPen(self.va_bar_color, width=self.va_lines_thickness))
            p.drawLine(QtCore.QPointF(vah_x1, y_vah), QtCore.QPointF(vah_x2, y_vah))
            p.drawLine(QtCore.QPointF(val_x1, y_val), QtCore.QPointF(val_x2, y_val))

        p.end()

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return QtCore.QRectF(self.picture.boundingRect())


class CandlestickChartSettingsDialog(QtWidgets.QDialog):
    # Define a custom signal that passes the new settings as dictionaries.
    settingsChanged = QtCore.pyqtSignal(dict, dict)  # First dict: chart colors; Second: display options

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Candlestick Chart Settings")

        # Import theme colors
        try:
            import theme
            self.theme_colors = theme.DEFAULT
        except ImportError:
            # Default theme colors if theme module is not available
            self.theme_colors = {
                'background': '#1e1e1e',
                'text': '#e0e0e0',
                'grid': '#2d2d2d',
                'bullish': '#4CAF50',
                'bearish': '#F44336',
                'axis': '#666666',
                'atr_1d': '#2196F3',
                'borders': '#3e3e3e',
                'primary_accent': '#007acc',
                'secondary_accent': '#0098ff',
                'control_panel': '#2d2d2d',
                'vp_poc_color': 'red',
                'vp_va_color': 'blue',
                'vp_bar_color': 'gray'
            }

        # Apply theme to dialog
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.theme_colors['background']};
                color: {self.theme_colors['text']};
            }}
            QGroupBox {{
                border: 1px solid {self.theme_colors['borders']};
                margin-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
                color: {self.theme_colors['text']};
            }}
            QLabel {{
                color: {self.theme_colors['text']};
            }}
            QPushButton {{
                background-color: {self.theme_colors['primary_accent']};
                color: {self.theme_colors['text']};
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {self.theme_colors['secondary_accent']};
            }}
            QCheckBox {{
                color: {self.theme_colors['text']};
            }}
            QSpinBox, QDoubleSpinBox, QComboBox {{
                background-color: {self.theme_colors['control_panel']};
                color: {self.theme_colors['text']};
                border: 1px solid {self.theme_colors['borders']};
                border-radius: 4px;
                padding: 3px;
            }}
        """)

        self.parent = parent
        self.default_colors = {
            'background': self.theme_colors['control_panel'],  # Use theme control panel color
            'text': self.theme_colors['text'],
            'grid': self.theme_colors['grid'],
            'bullish': self.theme_colors['bullish'],
            'bearish': self.theme_colors['bearish'],
            'axis': self.theme_colors['axis'],
            'atr_1d': self.theme_colors['atr_1d']
        }
        # Default drawing settings (if needed later)
        self.default_drawing_settings = {
            'line_width': 2,
            'text_size': 10,
            'default_color': 'White'
        }
        self.initUI()

    def initUI(self):
        main_layout = QtWidgets.QVBoxLayout(self)

        # Create a horizontal layout for the three setting groups
        groups_layout = QtWidgets.QHBoxLayout()
        groups_layout.setSpacing(20)

        # --- Chart Appearance Group ---
        appearance_group = QtWidgets.QGroupBox("Chart Appearance")
        appearance_layout = QtWidgets.QGridLayout()

        # Use parent's chart colors if available; otherwise use defaults.
        current_colors = (self.parent.chart_colors
                          if self.parent is not None and hasattr(self.parent, 'chart_colors')
                          else self.default_colors)

        # Background Color
        appearance_layout.addWidget(QtWidgets.QLabel("Background Color:"), 0, 0)
        self.bg_color_btn = QtWidgets.QPushButton()
        self.bg_color_btn.setFixedSize(100, 30)
        self.bg_color_btn.setStyleSheet(self.get_button_style(current_colors['background']))
        self.bg_color_btn.clicked.connect(lambda: self.choose_color(self.bg_color_btn))
        appearance_layout.addWidget(self.bg_color_btn, 0, 1)

        # Bullish Color
        appearance_layout.addWidget(QtWidgets.QLabel("Bullish Color:"), 1, 0)
        self.bull_color_btn = QtWidgets.QPushButton()
        self.bull_color_btn.setFixedSize(100, 30)
        self.bull_color_btn.setStyleSheet(self.get_button_style(current_colors['bullish']))
        self.bull_color_btn.clicked.connect(lambda: self.choose_color(self.bull_color_btn))
        appearance_layout.addWidget(self.bull_color_btn, 1, 1)

        # Bearish Color
        appearance_layout.addWidget(QtWidgets.QLabel("Bearish Color:"), 2, 0)
        self.bear_color_btn = QtWidgets.QPushButton()
        self.bear_color_btn.setFixedSize(100, 30)
        self.bear_color_btn.setStyleSheet(self.get_button_style(current_colors['bearish']))
        self.bear_color_btn.clicked.connect(lambda: self.choose_color(self.bear_color_btn))
        appearance_layout.addWidget(self.bear_color_btn, 2, 1)

        # Text Color
        appearance_layout.addWidget(QtWidgets.QLabel("Text Color:"), 3, 0)
        self.text_color_btn = QtWidgets.QPushButton()
        self.text_color_btn.setFixedSize(100, 30)
        self.text_color_btn.setStyleSheet(self.get_button_style(current_colors['text']))
        self.text_color_btn.clicked.connect(lambda: self.choose_color(self.text_color_btn))
        appearance_layout.addWidget(self.text_color_btn, 3, 1)

        # Grid Color
        appearance_layout.addWidget(QtWidgets.QLabel("Grid Color:"), 4, 0)
        self.grid_color_btn = QtWidgets.QPushButton()
        self.grid_color_btn.setFixedSize(100, 30)
        self.grid_color_btn.setStyleSheet(self.get_button_style(current_colors['grid']))
        self.grid_color_btn.clicked.connect(lambda: self.choose_color(self.grid_color_btn))
        appearance_layout.addWidget(self.grid_color_btn, 4, 1)

        # Axis Color
        appearance_layout.addWidget(QtWidgets.QLabel("Axis Color:"), 5, 0)
        self.axis_color_btn = QtWidgets.QPushButton()
        self.axis_color_btn.setFixedSize(100, 30)
        self.axis_color_btn.setStyleSheet(self.get_button_style(current_colors['axis']))
        self.axis_color_btn.clicked.connect(lambda: self.choose_color(self.axis_color_btn))
        appearance_layout.addWidget(self.axis_color_btn, 5, 1)

        # ATR Color
        appearance_layout.addWidget(QtWidgets.QLabel("ATR Color:"), 6, 0)
        self.atr_color_btn = QtWidgets.QPushButton()
        self.atr_color_btn.setFixedSize(100, 30)
        self.atr_color_btn.setStyleSheet(self.get_button_style(current_colors['atr_1d']))
        self.atr_color_btn.clicked.connect(lambda: self.choose_color(self.atr_color_btn))
        appearance_layout.addWidget(self.atr_color_btn, 6, 1)

        # Grid Opacity
        appearance_layout.addWidget(QtWidgets.QLabel("Grid Opacity:"), 7, 0)
        self.grid_opacity_spin = QtWidgets.QSpinBox()
        self.grid_opacity_spin.setRange(0, 100)
        self.grid_opacity_spin.setValue(10)
        self.grid_opacity_spin.setSuffix("%")
        appearance_layout.addWidget(self.grid_opacity_spin, 7, 1)

        # ATR Period
        appearance_layout.addWidget(QtWidgets.QLabel("ATR Period:"), 8, 0)
        self.atr_period_spin = QtWidgets.QSpinBox()
        self.atr_period_spin.setRange(1, 30)
        self.atr_period_spin.setValue(5)
        appearance_layout.addWidget(self.atr_period_spin, 8, 1)

        # Reset Colors Button
        reset_btn = QtWidgets.QPushButton("Reset Colors")
        reset_btn.clicked.connect(self.reset_colors)
        appearance_layout.addWidget(reset_btn, 9, 0, 1, 2)

        appearance_group.setLayout(appearance_layout)
        groups_layout.addWidget(appearance_group)

        # --- Display Options Group ---
        display_group = QtWidgets.QGroupBox("Display Options")
        display_layout = QtWidgets.QGridLayout()

        self.show_atr_checkbox = QtWidgets.QCheckBox("Show ATR Bands")
        self.show_atr_checkbox.setChecked(True)
        display_layout.addWidget(self.show_atr_checkbox, 0, 0)

        self.show_grid_checkbox = QtWidgets.QCheckBox("Show Grid")
        self.show_grid_checkbox.setChecked(True)
        display_layout.addWidget(self.show_grid_checkbox, 1, 0)

        display_layout.addWidget(QtWidgets.QLabel("Auto-update Interval (sec):"), 2, 0)
        self.update_interval_spin = QtWidgets.QSpinBox()
        self.update_interval_spin.setRange(1, 300)
        self.update_interval_spin.setValue(60)
        display_layout.addWidget(self.update_interval_spin, 2, 1)

        self.show_vp_checkbox = QtWidgets.QCheckBox("Show Volume Profile")
        current_vp_options = (self.parent.display_options
                              if self.parent is not None and hasattr(self.parent, 'display_options')
                              else {})
        self.show_vp_checkbox.setChecked(current_vp_options.get('show_volume_profile', False))
        display_layout.addWidget(self.show_vp_checkbox, 3, 0, 1, 2)

        # Add placeholders for visual settings checkboxes
        self.show_peak_trough_rays = QtWidgets.QCheckBox("Show Peak/Trough Rays")
        self.show_peak_trough_rays.setChecked(False)

        # The Line settings
        vector_layout = QtWidgets.QHBoxLayout()
        self.show_vector = QtWidgets.QCheckBox("Show The Line")
        self.show_vector.setChecked(False)
        vector_layout.addWidget(self.show_vector)

        vector_length_label = QtWidgets.QLabel("Length:")
        vector_layout.addWidget(vector_length_label)

        self.vector_length_spin = QtWidgets.QSpinBox()
        self.vector_length_spin.setRange(1, 50)
        self.vector_length_spin.setValue(10)  # Default from PineScript
        vector_layout.addWidget(self.vector_length_spin)

        display_layout.addLayout(vector_layout, 4, 0, 1, 2)

        self.show_probability_bands = QtWidgets.QCheckBox("Show Probability Bands")
        self.show_probability_bands.setChecked(False)
        self.show_imprints = QtWidgets.QCheckBox("Show P/T")
        self.show_imprints.setChecked(False)
        self.show_reversals = QtWidgets.QCheckBox("Show Reversals")
        self.show_reversals.setChecked(True)
        self.show_pullbacks = QtWidgets.QCheckBox("Show Pullbacks")
        self.show_pullbacks.setChecked(True)
        self.auto_fetch_check = QtWidgets.QCheckBox("Auto-Fetch Data")
        self.auto_fetch_check.setChecked(False)

        display_group.setLayout(display_layout)
        groups_layout.addWidget(display_group)

        # --- Volume Profile Settings Group ---
        vp_group = QtWidgets.QGroupBox("Volume Profile Settings")
        vp_layout = QtWidgets.QGridLayout()

        current_vp_options = (self.parent.display_options
                              if self.parent is not None and hasattr(self.parent, 'display_options')
                              else {})

        vp_layout.addWidget(QtWidgets.QLabel("Number of Price Levels:"), 0, 0)
        self.vp_num_bars_spin = QtWidgets.QSpinBox()
        self.vp_num_bars_spin.setRange(50, 500)
        self.vp_num_bars_spin.setValue(current_vp_options.get('vp_num_bars', 200))
        vp_layout.addWidget(self.vp_num_bars_spin, 0, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Bar Thickness:"), 1, 0)
        self.vp_thickness_spin = QtWidgets.QSpinBox()
        self.vp_thickness_spin.setRange(1, 5)
        self.vp_thickness_spin.setValue(current_vp_options.get('vp_bar_thickness', 1))
        vp_layout.addWidget(self.vp_thickness_spin, 1, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Bar Length:"), 2, 0)
        self.vp_length_spin = QtWidgets.QSpinBox()
        self.vp_length_spin.setRange(5, 50)
        self.vp_length_spin.setValue(current_vp_options.get('vp_bar_length_mult', 10))
        vp_layout.addWidget(self.vp_length_spin, 2, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Volume Type:"), 3, 0)
        self.vp_volume_type = QtWidgets.QComboBox()
        self.vp_volume_type.addItems(["Both", "Bullish", "Bearish"])
        current_type = current_vp_options.get('vp_volume_type', 'Both')
        index = self.vp_volume_type.findText(current_type)
        if index >= 0:
            self.vp_volume_type.setCurrentIndex(index)
        vp_layout.addWidget(self.vp_volume_type, 3, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Lookback Depth:"), 4, 0)
        self.vp_lookback_spin = QtWidgets.QSpinBox()
        self.vp_lookback_spin.setRange(10, 1000)
        self.vp_lookback_spin.setValue(current_vp_options.get('vp_lookback_depth', 200))
        vp_layout.addWidget(self.vp_lookback_spin, 4, 1)

        self.vp_use_visible_checkbox = QtWidgets.QCheckBox("Use Visible Range Only")
        self.vp_use_visible_checkbox.setChecked(current_vp_options.get('vp_use_visible_range', False))
        vp_layout.addWidget(self.vp_use_visible_checkbox, 5, 0, 1, 2)

        vp_layout.addWidget(QtWidgets.QLabel("Point of Control (POC):"), 6, 0, 1, 2)

        self.vp_display_poc_checkbox = QtWidgets.QCheckBox("Display POC")
        self.vp_display_poc_checkbox.setChecked(current_vp_options.get('vp_display_poc', True))
        vp_layout.addWidget(self.vp_display_poc_checkbox, 7, 0)

        vp_layout.addWidget(QtWidgets.QLabel("POC Line Thickness:"), 8, 0)
        self.vp_poc_thickness_spin = QtWidgets.QSpinBox()
        self.vp_poc_thickness_spin.setRange(1, 5)
        self.vp_poc_thickness_spin.setValue(current_vp_options.get('vp_poc_line_thickness', 1))
        vp_layout.addWidget(self.vp_poc_thickness_spin, 8, 1)

        vp_layout.addWidget(QtWidgets.QLabel("POC Color:"), 9, 0)
        self.vp_poc_color_btn = QtWidgets.QPushButton()
        self.vp_poc_color_btn.setFixedSize(100, 20)
        self.vp_poc_color_btn.setStyleSheet(self.get_button_style(current_vp_options.get('vp_poc_color', self.theme_colors['vp_poc_color'])))
        self.vp_poc_color_btn.clicked.connect(lambda: self.choose_color(self.vp_poc_color_btn))
        vp_layout.addWidget(self.vp_poc_color_btn, 9, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Value Area (VA):"), 10, 0, 1, 2)

        self.vp_display_va_checkbox = QtWidgets.QCheckBox("Display Value Area")
        self.vp_display_va_checkbox.setChecked(current_vp_options.get('vp_display_va', True))
        vp_layout.addWidget(self.vp_display_va_checkbox, 11, 0)

        vp_layout.addWidget(QtWidgets.QLabel("Value Area %:"), 12, 0)
        self.vp_va_percent_spin = QtWidgets.QSpinBox()
        self.vp_va_percent_spin.setRange(50, 90)
        self.vp_va_percent_spin.setValue(current_vp_options.get('vp_va_percent', 68))
        self.vp_va_percent_spin.setSuffix("%")
        vp_layout.addWidget(self.vp_va_percent_spin, 12, 1)

        self.vp_display_va_lines_checkbox = QtWidgets.QCheckBox("Display VA Lines")
        self.vp_display_va_lines_checkbox.setChecked(current_vp_options.get('vp_display_va_lines', True))
        vp_layout.addWidget(self.vp_display_va_lines_checkbox, 13, 0)

        vp_layout.addWidget(QtWidgets.QLabel("VA Lines Thickness:"), 14, 0)
        self.vp_va_thickness_spin = QtWidgets.QSpinBox()
        self.vp_va_thickness_spin.setRange(1, 5)
        self.vp_va_thickness_spin.setValue(current_vp_options.get('vp_va_lines_thickness', 1))
        vp_layout.addWidget(self.vp_va_thickness_spin, 14, 1)

        vp_layout.addWidget(QtWidgets.QLabel("VA Color:"), 15, 0)
        self.vp_va_color_btn = QtWidgets.QPushButton()
        self.vp_va_color_btn.setFixedSize(100, 20)
        self.vp_va_color_btn.setStyleSheet(self.get_button_style(current_vp_options.get('vp_va_color', self.theme_colors['vp_va_color'])))
        self.vp_va_color_btn.clicked.connect(lambda: self.choose_color(self.vp_va_color_btn))
        vp_layout.addWidget(self.vp_va_color_btn, 15, 1)

        vp_layout.addWidget(QtWidgets.QLabel("Bar Color:"), 16, 0)
        self.vp_bar_color_btn = QtWidgets.QPushButton()
        self.vp_bar_color_btn.setFixedSize(100, 20)
        self.vp_bar_color_btn.setStyleSheet(self.get_button_style(current_vp_options.get('vp_bar_color', self.theme_colors['vp_bar_color'])))
        self.vp_bar_color_btn.clicked.connect(lambda: self.choose_color(self.vp_bar_color_btn))
        vp_layout.addWidget(self.vp_bar_color_btn, 16, 1)

        vp_group.setLayout(vp_layout)
        groups_layout.addWidget(vp_group)

        # Add groups layout to main layout
        main_layout.addLayout(groups_layout)

        # --- OK and Cancel Buttons ---
        buttons_layout = QtWidgets.QHBoxLayout()
        buttons_layout.addStretch()  # Push buttons to the right

        # OK button with system theme colors
        ok_btn = QtWidgets.QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme_colors['primary_accent']};
                color: {self.theme_colors['text']};
                border: 1px solid {self.theme_colors['borders']};
                border-radius: {self.theme_colors.get('button_radius', '4px')};
                padding: 6px 12px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background-color: {self.theme_colors['secondary_accent']};
                border: 1px solid {self.theme_colors['secondary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {self.theme_colors.get('pressed_accent', self.theme_colors['primary_accent'])};
                border: 1px solid {self.theme_colors.get('pressed_accent', self.theme_colors['primary_accent'])};
            }}
        """)

        # Cancel button with system theme colors
        cancel_btn = QtWidgets.QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme_colors['control_panel']};
                color: {self.theme_colors['text']};
                border: 1px solid {self.theme_colors['borders']};
                border-radius: {self.theme_colors.get('button_radius', '4px')};
                padding: 6px 12px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background-color: {self.theme_colors['borders']};
                border: 1px solid {self.theme_colors['primary_accent']};
            }}
            QPushButton:pressed {{
                background-color: {self.theme_colors['background']};
                border: 1px solid {self.theme_colors['primary_accent']};
            }}
        """)

        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def get_button_style(self, color):
        """Generate a consistent button style with the given background color"""
        return f"""
            QPushButton {{
                background-color: {color};
                border: 1px solid {self.theme_colors['borders']};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid {self.theme_colors['secondary_accent']};
            }}
        """

    def choose_color(self, button):
        try:
            current_color = button.styleSheet().split("background-color:")[1].split(";")[0].strip()
        except IndexError:
            current_color = "#ffffff"
        color = QtWidgets.QColorDialog.getColor(QtGui.QColor(current_color), self)
        if color.isValid():
            button.setStyleSheet(self.get_button_style(color.name()))

    def get_chart_colors(self):
        """Return a dictionary of chart colors from the Appearance group."""
        def extract_color(btn):
            try:
                return btn.styleSheet().split("background-color:")[1].split(";")[0].strip()
            except IndexError:
                return "#ffffff"

        return {
            "background": extract_color(self.bg_color_btn),
            "bullish": extract_color(self.bull_color_btn),
            "bearish": extract_color(self.bear_color_btn),
            "text": extract_color(self.text_color_btn),
            "grid": extract_color(self.grid_color_btn),
            "axis": extract_color(self.axis_color_btn),
            "atr_1d": extract_color(self.atr_color_btn),
            "volume_profile": extract_color(self.vp_bar_color_btn),
            "poc_line": extract_color(self.vp_poc_color_btn),
            "va_bar": extract_color(self.vp_va_color_btn)
        }

    def get_display_options(self):
        return {
            'show_atr': self.show_atr_checkbox.isChecked(),
            'show_grid': self.show_grid_checkbox.isChecked(),
            'grid_opacity': self.grid_opacity_spin.value() / 100.0,
            'atr_period': self.atr_period_spin.value(),
            'update_interval': self.update_interval_spin.value() * 1000,
            'show_volume_profile': self.show_vp_checkbox.isChecked(),
            'vp_use_visible_range': self.vp_use_visible_checkbox.isChecked(),
            'vp_lookback_depth': self.vp_lookback_spin.value(),
            'vp_num_bars': self.vp_num_bars_spin.value(),
            'vp_bar_thickness': self.vp_thickness_spin.value(),
            'vp_bar_length_mult': self.vp_length_spin.value(),
            'vp_right_offset': 5,
            'vp_volume_type': self.vp_volume_type.currentText(),
            'vp_display_poc': self.vp_display_poc_checkbox.isChecked(),
            'vp_poc_line_thickness': self.vp_poc_thickness_spin.value(),
            'vp_display_va': self.vp_display_va_checkbox.isChecked(),
            'vp_va_percent': self.vp_va_percent_spin.value(),
            'vp_display_va_lines': self.vp_display_va_lines_checkbox.isChecked(),
            'vp_va_lines_thickness': self.vp_va_thickness_spin.value(),
            'vp_right_aligned': True,
            'vp_poc_color': self.vp_poc_color_btn.styleSheet().split('background-color:')[1].split(';')[0].strip(),
            'vp_va_color': self.vp_va_color_btn.styleSheet().split('background-color:')[1].split(';')[0].strip(),
            'vp_bar_color': self.vp_bar_color_btn.styleSheet().split('background-color:')[1].split(';')[0].strip(),
            # Visual settings
            'show_peak_trough_rays': self.show_peak_trough_rays.isChecked(),
            'show_vector': self.show_vector.isChecked(),
            'vector_length': self.vector_length_spin.value(),
            'show_probability_bands': self.show_probability_bands.isChecked(),
            'show_imprints': self.show_imprints.isChecked(),
            'show_reversals': self.show_reversals.isChecked(),
            'show_pullbacks': self.show_pullbacks.isChecked(),
            'auto_fetch': self.auto_fetch_check.isChecked()
        }

    def get_drawing_settings(self):
        return {
            'line_width': self.default_drawing_settings['line_width'],
            'text_size': self.default_drawing_settings['text_size'],
            'default_color': self.default_drawing_settings['default_color']
        }

    def reset_colors(self):
        self.bg_color_btn.setStyleSheet(self.get_button_style(self.default_colors['background']))
        self.bull_color_btn.setStyleSheet(self.get_button_style(self.default_colors['bullish']))
        self.bear_color_btn.setStyleSheet(self.get_button_style(self.default_colors['bearish']))
        self.text_color_btn.setStyleSheet(self.get_button_style(self.default_colors['text']))
        self.grid_color_btn.setStyleSheet(self.get_button_style(self.default_colors['grid']))
        self.axis_color_btn.setStyleSheet(self.get_button_style(self.default_colors['axis']))
        self.atr_color_btn.setStyleSheet(self.get_button_style(self.default_colors['atr_1d']))
        self.vp_poc_color_btn.setStyleSheet(self.get_button_style(self.theme_colors['vp_poc_color']))
        self.vp_va_color_btn.setStyleSheet(self.get_button_style(self.theme_colors['vp_va_color']))
        self.vp_bar_color_btn.setStyleSheet(self.get_button_style(self.theme_colors['vp_bar_color']))
        self.grid_opacity_spin.setValue(10)
        self.atr_period_spin.setValue(5)
        self.vp_num_bars_spin.setValue(200)
        self.vp_thickness_spin.setValue(1)
        self.vp_length_spin.setValue(10)
        self.vp_lookback_spin.setValue(200)
        self.vp_poc_thickness_spin.setValue(1)
        self.vp_va_percent_spin.setValue(68)
        self.vp_va_thickness_spin.setValue(1)


class CandlestickChart(QtWidgets.QWidget):
    """
    A complete candlestick chart widget with data fetching, settings, and visualization.

    This widget provides:
    - Symbol input and timeframe selection
    - Data fetching from Yahoo Finance
    - Candlestick rendering with customizable appearance
    - Volume profile display
    - Price level indicators
    - Auto-updating capability
    """

    def __init__(self, parent=None):
        super().__init__(parent)

        # Chart data
        self.data = pd.DataFrame()
        self.ohlc_data = []

        # Connect to parameter registry signals
        from parameter_registry import default_registry
        default_registry.ui_parameter_changed.connect(self.on_parameter_changed)

        # Chart items
        self.candlestick_item = None
        self.volume_profile_item = None
        self.price_levels = {}
        self.volatility_lines = []  # Store volatility level lines
        self.volatility_zones = []  # Store volatility zone fills
        self.ak_daily_vol_zones_lines = []  # Store AK daily vol zones lines
        self.ak_hl_zones_lines = []  # Store H/L matching lines separately
        self.ak_weekday_zones_lines = []  # Store weekday matching lines separately
        self.ak_intersect_zones_lines = []  # Store AK intersect zones lines
        self.ak_density_zones = []  # Store AK density zones from density graph

        # Chart colors
        self.chart_colors = {
            'background': '#1e1e1e',
            'text': '#e0e0e0',
            'grid': '#2d2d2d',
            'bullish': '#4CAF50',  # Material Design Green
            'bearish': '#F44336',  # Material Design Red
            'axis': '#666666',
            'atr_1d': '#2196F3',   # Blue for daily ATR
            'volume_profile': (100, 100, 100, 128),
            'poc_line': 'r',
            'va_bar': (0, 0, 255, 128)
        }

        # Display options
        self.display_options = {
            'show_atr': True,
            'show_grid': True,
            'grid_opacity': 0.1,
            'atr_period': 5,
            'update_interval': 60000,
            'show_volume_profile': True,
            'vp_use_visible_range': False,
            'vp_lookback_depth': 200,
            'vp_num_bars': 200,
            'vp_bar_thickness': 1,
            'vp_bar_length_mult': 10,
            'vp_right_offset': 5,
            'vp_volume_type': 'Both',
            'vp_display_poc': True,
            'vp_poc_line_thickness': 1,
            'vp_display_va': True,
            'vp_va_percent': 68,
            'vp_display_va_lines': True,
            'vp_va_lines_thickness': 1,
            'vp_right_aligned': True,
            'vp_poc_color': '#FF0000',
            'vp_va_color': '#0000FF',
            'vp_bar_color': '#666666',
            'show_peak_trough_rays': False,
            'show_vector': False,
            'vector_length': 10,
            'show_probability_bands': False,
            'show_imprints': False,
            'show_reversals': True,
            'show_pullbacks': True,
            'auto_fetch': False,
            'show_volatility_levels': False  # Disabled - only AK's weekly vol zones will show levels
        }

        # Create settings dialog
        self.settings_dialog = CandlestickChartSettingsDialog(self)
        self.settings_dialog.settingsChanged.connect(self.apply_settings)

        # Load saved settings
        self.load_chart_settings()

        # Initialize UI
        self.init_ui()

        # Create update timer
        self.update_timer = QtCore.QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.setInterval(60000)  # Update every minute

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        self.layout = QtWidgets.QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

        # Control panel
        control_panel = QtWidgets.QWidget()
        control_layout = QtWidgets.QHBoxLayout(control_panel)
        control_layout.setContentsMargins(5, 5, 5, 5)

        # Symbol input (hidden - now controlled by universal controls)
        self.symbol_input = QtWidgets.QLineEdit()
        self.symbol_input.setMaximumWidth(100)
        self.symbol_input.returnPressed.connect(self.fetch_data)
        self.symbol_input.setVisible(False)

        # Timeframe selector (hidden - now controlled by universal controls)
        self.timeframe_combo = QtWidgets.QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "30m", "60m", "1h", "1d", "1wk", "1mo"])
        self.timeframe_combo.setCurrentText("1d")
        self.timeframe_combo.setMaximumWidth(80)
        self.timeframe_combo.setVisible(False)

        # Days to load (hidden - now controlled by universal controls)
        self.days_spin = QtWidgets.QSpinBox()
        self.days_spin.setRange(1, 20000)  # Increased to allow 15k+ days
        self.days_spin.setValue(30)
        self.days_spin.setMaximumWidth(60)
        self.days_spin.setVisible(False)

        # Timeframe selector for candlestick chart
        timeframe_label = QtWidgets.QLabel("Timeframe:")
        self.candlestick_timeframe_combo = QtWidgets.QComboBox()
        self.candlestick_timeframe_combo.addItems(["1m", "5m", "15m", "30m", "60m", "1h", "1d", "1wk", "1mo"])
        self.candlestick_timeframe_combo.setCurrentText("1d")
        self.candlestick_timeframe_combo.setMaximumWidth(80)
        self.candlestick_timeframe_combo.currentTextChanged.connect(self.on_candlestick_timeframe_changed)
        control_layout.addWidget(timeframe_label)
        control_layout.addWidget(self.candlestick_timeframe_combo)

        # Candlestick type selector
        type_label = QtWidgets.QLabel("Type:")
        self.candlestick_type_selector = QtWidgets.QComboBox()
        self.candlestick_type_selector.addItems(["Regular", "Hollow", "Heikin-Ashi"])
        self.candlestick_type_selector.setMaximumWidth(100)
        self.candlestick_type_selector.currentTextChanged.connect(self.update_chart)
        control_layout.addWidget(type_label)
        control_layout.addWidget(self.candlestick_type_selector)

        # Volume profile checkbox
        self.show_volume_profile = QtWidgets.QCheckBox("Volume Profile")
        self.show_volume_profile.setChecked(True)
        self.show_volume_profile.stateChanged.connect(self.update_chart)
        control_layout.addWidget(self.show_volume_profile)

        # Plot Levels button - refreshes chart to update AK's weekly vol zones
        self.plot_levels_button = QtWidgets.QPushButton("Plot Levels")
        self.plot_levels_button.clicked.connect(self.plot_levels)
        self.plot_levels_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.plot_levels_button)

        # Toggle Line Indicator button
        # Initialize button text based on current line indicator state
        initial_line_state = self.display_options.get('show_vector', False)
        button_text = "Hide Line" if initial_line_state else "Show Line"
        self.toggle_line_button = QtWidgets.QPushButton(button_text)
        self.toggle_line_button.clicked.connect(self.toggle_line_indicator)
        self.toggle_line_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.toggle_line_button)

        # Data Table button for AK levels
        self.data_table_button = QtWidgets.QPushButton("Data Table")
        self.data_table_button.clicked.connect(self.show_ak_levels_table)
        self.data_table_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.data_table_button)

        # Plot AK Daily Vol Zones button
        self.plot_ak_daily_zones_button = QtWidgets.QPushButton("Plot AK Daily Vol Zones")
        self.plot_ak_daily_zones_button.clicked.connect(self.plot_ak_daily_vol_zones)
        self.plot_ak_daily_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.plot_ak_daily_zones_button)

        # Plot AK Intersect Zones button
        self.plot_ak_intersect_zones_button = QtWidgets.QPushButton("Plot AK Intersect Zones")
        self.plot_ak_intersect_zones_button.clicked.connect(self.plot_ak_intersect_zones)
        self.plot_ak_intersect_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.plot_ak_intersect_zones_button)

        # Plot AK Density Zones button
        self.plot_ak_density_zones_button = QtWidgets.QPushButton("Plot AK Density Zones")
        self.plot_ak_density_zones_button.clicked.connect(self.plot_ak_density_zones)
        self.plot_ak_density_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.plot_ak_density_zones_button)

        # Export Zones to TradingView button
        self.export_zones_button = QtWidgets.QPushButton("Export to TV")
        self.export_zones_button.clicked.connect(self.export_zones_to_tradingview)
        self.export_zones_button.setStyleSheet("""
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-weight: bold;
                font-size: 11px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3e3e3e;
                border: 1px solid white;
            }
            QPushButton:pressed {
                background-color: white;
                color: #2d2d2d;
            }
        """)
        control_layout.addWidget(self.export_zones_button)

        # AK Daily Vol Zones toggle checkboxes
        ak_zones_container = QtWidgets.QWidget()
        ak_zones_layout = QtWidgets.QHBoxLayout(ak_zones_container)
        ak_zones_layout.setContentsMargins(0, 0, 0, 0)
        ak_zones_layout.setSpacing(10)

        # H/L matching checkbox
        self.show_hl_zones_checkbox = QtWidgets.QCheckBox("H/L Zones")
        self.show_hl_zones_checkbox.setChecked(True)  # Default to checked
        self.show_hl_zones_checkbox.stateChanged.connect(self.toggle_hl_zones)
        self.show_hl_zones_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid #3e3e3e;
                border-radius: 2px;
                background-color: #2d2d2d;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 1px solid #4CAF50;
            }
        """)

        # Weekday matching checkbox
        self.show_weekday_zones_checkbox = QtWidgets.QCheckBox("Weekday Zones")
        self.show_weekday_zones_checkbox.setChecked(True)  # Default to checked
        self.show_weekday_zones_checkbox.stateChanged.connect(self.toggle_weekday_zones)
        self.show_weekday_zones_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid #3e3e3e;
                border-radius: 2px;
                background-color: #2d2d2d;
            }
            QCheckBox::indicator:checked {
                background-color: #FF9800;
                border: 1px solid #FF9800;
            }
        """)

        ak_zones_layout.addWidget(self.show_hl_zones_checkbox)
        ak_zones_layout.addWidget(self.show_weekday_zones_checkbox)
        control_layout.addWidget(ak_zones_container)

        # Volatility levels checkbox - REMOVED
        # Only AK's weekly vol zones will show levels now

        # The Line controls (hidden - now controlled by universal controls)
        self.show_vector_checkbox = QtWidgets.QCheckBox("The Line")
        self.show_vector_checkbox.setChecked(self.display_options.get('show_vector', False))
        self.show_vector_checkbox.stateChanged.connect(self.toggle_vector)
        self.show_vector_checkbox.setVisible(False)

        # The Line length spinner (hidden - now controlled by universal controls)
        self.vector_length_spinner = QtWidgets.QSpinBox()
        self.vector_length_spinner.setRange(1, 50)
        self.vector_length_spinner.setValue(self.display_options.get('vector_length', 10))
        self.vector_length_spinner.setMaximumWidth(50)
        self.vector_length_spinner.valueChanged.connect(self.update_vector_length)
        self.vector_length_spinner.setVisible(False)

        # Fetch button (hidden - now controlled by universal controls)
        self.fetch_button = QtWidgets.QPushButton("Fetch")
        self.fetch_button.clicked.connect(self.fetch_data)
        self.fetch_button.setStyleSheet("""
            QPushButton {
                background-color: #000000;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #333333;
                border: 1px solid #555555;
            }
            QPushButton:pressed {
                background-color: #111111;
                border: 1px solid #444444;
            }
        """)
        self.fetch_button.setVisible(False)

        # Settings button
        self.settings_button = QtWidgets.QPushButton("Settings")
        self.settings_button.clicked.connect(self.open_settings_dialog)
        self.settings_button.setStyleSheet("""
            QPushButton {
                background-color: #000000;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #333333;
                border: 1px solid #555555;
            }
            QPushButton:pressed {
                background-color: #111111;
                border: 1px solid #444444;
            }
        """)
        control_layout.addWidget(self.settings_button)

        # Add stretch to push everything to the left
        control_layout.addStretch(1)

        # Add control panel to main layout
        self.layout.addWidget(control_panel)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(self.chart_colors['background'])
        self.plot_widget.showGrid(x=True, y=True, alpha=0.2)

        # Set up axes
        self.plot_widget.getAxis('left').setPen(pg.mkPen(self.chart_colors['axis']))
        self.plot_widget.getAxis('left').setTextPen(pg.mkPen(self.chart_colors['text']))
        self.plot_widget.getAxis('bottom').setPen(pg.mkPen(self.chart_colors['axis']))
        self.plot_widget.getAxis('bottom').setTextPen(pg.mkPen(self.chart_colors['text']))

        # Create price plot
        self.price_plot = self.plot_widget.plotItem
        self.price_plot.setClipToView(True)

        # Add crosshair
        self.setup_crosshair()

        # Add plot widget to main layout
        self.layout.addWidget(self.plot_widget)

        # Status bar
        self.status_bar = QtWidgets.QLabel("Ready")
        self.status_bar.setStyleSheet(f"color: {self.chart_colors['text']};")
        self.layout.addWidget(self.status_bar)

    def setup_crosshair(self):
        """Set up crosshair for the chart."""
        # Create crosshair lines with more visible styling - white, dashed, width 2
        self.vLine = pg.InfiniteLine(
            angle=90,
            movable=False,
            pen=pg.mkPen(color='#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
        )
        self.hLine = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen(color='#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
        )
        self.price_plot.addItem(self.vLine, ignoreBounds=True)
        self.price_plot.addItem(self.hLine, ignoreBounds=True)

        # Create a legend for crosshair information
        if not hasattr(self, 'crosshair_legend') or self.crosshair_legend is None:
            # Position the legend in the top-right corner with some padding
            self.crosshair_legend = self.price_plot.addLegend(offset=(-20, 20))

            # Style the legend to make it more visible
            if hasattr(self.crosshair_legend, 'layout'):
                self.crosshair_legend.layout.setContentsMargins(5, 5, 5, 5)
                self.crosshair_legend.layout.setSpacing(5)

            # Set a semi-transparent background for better visibility
            if hasattr(self.crosshair_legend, 'setBackground'):
                self.crosshair_legend.setBackground(pg.mkBrush(QtGui.QColor(43, 43, 43, 200)))

        # Create dummy items for the legend
        self.time_item = pg.PlotDataItem(pen=pg.mkPen(color='#FFFFFF', width=0))
        self.price_item = pg.PlotDataItem(pen=pg.mkPen(color='#FFFFFF', width=0))
        self.ohlc_item = pg.PlotDataItem(pen=pg.mkPen(color='#FFFFFF', width=0))
        self.volume_item = pg.PlotDataItem(pen=pg.mkPen(color='#FFFFFF', width=0))
        self.vector_item_legend = pg.PlotDataItem(pen=pg.mkPen(color='purple', width=2))

        # Add dummy items to the legend
        self.crosshair_legend.addItem(self.time_item, "Time: ")
        self.crosshair_legend.addItem(self.price_item, "Price: ")
        self.crosshair_legend.addItem(self.ohlc_item, "OHLC: ")
        self.crosshair_legend.addItem(self.volume_item, "Volume: ")
        self.crosshair_legend.addItem(self.vector_item_legend, "Vector: ")

        # Connect mouse move signal
        self.proxy = pg.SignalProxy(
            self.plot_widget.scene().sigMouseMoved,
            rateLimit=60,
            slot=self.update_crosshair
        )

    def update_crosshair(self, event):
        """Update crosshair position and text."""
        pos = event[0]
        if self.price_plot.sceneBoundingRect().contains(pos):
            mouse_point = self.price_plot.vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()

            # Ensure crosshair lines have the correct style (white, dashed, width 2)
            white_dashed_pen = pg.mkPen('#FFFFFF', width=2, style=QtCore.Qt.PenStyle.DashLine)
            self.vLine.setPen(white_dashed_pen)
            self.hLine.setPen(white_dashed_pen)

            # Update crosshair lines
            self.vLine.setPos(x)
            self.hLine.setPos(y)

            # Format date and get candle info if we have data
            date_str = ""
            price_str = f"{y:.2f}"
            ohlc_str = ""
            vol_str = ""  # Initialize vol_str here to avoid UnboundLocalError
            vector_str = ""  # Initialize vector_str for the vector value

            # Use round() instead of int() for more accurate candlestick selection
            # This ensures that when hovering over a candlestick, we get the nearest one
            candle_idx = round(x)
            if not self.data.empty and 0 <= candle_idx < len(self.data):
                try:
                    # Get date and time
                    date = self.data.index[candle_idx]
                    date_str = date.strftime("%Y-%m-%d %H:%M")

                    # Get OHLC data for the candle
                    candle = self.data.iloc[candle_idx]
                    ohlc_str = (
                        f"O: {candle['Open']:.2f}  H: {candle['High']:.2f}  "
                        f"L: {candle['Low']:.2f}  C: {candle['Close']:.2f}"
                    )

                    # Add volume if available
                    if 'Volume' in candle:
                        volume = candle['Volume']
                        # Format volume with K, M, B suffixes for readability
                        if volume >= 1_000_000_000:
                            vol_str = f"{volume/1_000_000_000:.2f}B"
                        elif volume >= 1_000_000:
                            vol_str = f"{volume/1_000_000:.2f}M"
                        elif volume >= 1_000:
                            vol_str = f"{volume/1_000:.2f}K"
                        else:
                            vol_str = f"{volume:.0f}"

                    # Get The Line value if available
                    if hasattr(self, 'vector_item') and self.vector_item is not None:
                        try:
                            # Get The Line value at the current x position using the same candle_idx
                            if 0 <= candle_idx < len(self.vector_item.vector_values):
                                vector_value = self.vector_item.vector_values[candle_idx]
                                vector_str = f"{vector_value:.2f}"

                                # Add direction indicator (↑/↓) based on background color
                                if candle_idx > 0:
                                    prev_value = self.vector_item.vector_values[candle_idx-1]
                                    if vector_value > prev_value:
                                        vector_str += " ↑"  # Up arrow for rising Line
                                    elif vector_value < prev_value:
                                        vector_str += " ↓"  # Down arrow for falling Line
                        except Exception as e:
                            print(f"Error getting The Line value: {str(e)}")

                except (IndexError, AttributeError) as e:
                    date_str = f"Index: {candle_idx}"
                    print(f"Error getting candle data: {e}")

            # Update the legend items
            # First, clear the legend
            self.crosshair_legend.clear()

            # Re-add the items with updated information
            self.crosshair_legend.addItem(self.time_item, f"Time: {date_str}")
            self.crosshair_legend.addItem(self.price_item, f"Price: {price_str}")

            if ohlc_str:
                self.crosshair_legend.addItem(self.ohlc_item, f"{ohlc_str}")

            if vol_str:
                self.crosshair_legend.addItem(self.volume_item, f"Volume: {vol_str}")

            # Add vector value if available
            if vector_str:
                self.crosshair_legend.addItem(self.vector_item_legend, f"Vector: {vector_str}")

    def fetch_data(self):
        """Fetch data for the chart."""
        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            QtWidgets.QMessageBox.warning(self, "Error", "Please enter a symbol")
            return

        try:
            # Update status
            self.status_bar.setText(f"Fetching data for {symbol}...")

            # Set wait cursor
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

            # Fetch data
            ticker = yf.Ticker(symbol)

            # Use the candlestick chart's own timeframe selector
            interval = self.candlestick_timeframe_combo.currentText()

            # Adjust period based on timeframe to avoid Yahoo Finance limitations
            if interval in ["1m", "2m", "5m"]:
                # For minute intervals, use maximum 7 days (Yahoo Finance limit)
                period = "7d"
            elif interval in ["15m", "30m", "60m", "1h"]:
                # For hourly intervals, use maximum 60 days
                period = "60d"
            else:
                # For daily and above, use the days spinner value
                days = self.days_spin.value()
                period = f"{days}d"

            print(f"Fetching {symbol} data with interval={interval}, period={period}")
            self.data = ticker.history(period=period, interval=interval)

            if self.data.empty:
                QtWidgets.QMessageBox.warning(self, "Error", "No data returned for symbol")
                QtWidgets.QApplication.restoreOverrideCursor()
                return

            if self.data.index.tz is not None:
                self.data.index = self.data.index.tz_localize(None)

            # Update chart
            self.update_chart()

            # Start update timer
            self.update_timer.start()

            # Restore cursor
            QtWidgets.QApplication.restoreOverrideCursor()

            # Update status
            self.status_bar.setText(f"{symbol} - {interval} - Last update: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            # Handle errors
            QtWidgets.QMessageBox.critical(self, "Error", f"Error fetching data: {str(e)}")
            QtWidgets.QApplication.restoreOverrideCursor()
            self.status_bar.setText(f"Error: {str(e)}")

    def get_volatility_graph_tab(self):
        """
        Get the volatility graph tab from the main application.

        Returns:
            VolatilityGraphTab or None: The volatility graph tab if found
        """
        try:
            # Try to find the main window through the parent hierarchy
            widget = self
            while widget is not None:
                if hasattr(widget, 'Volatility_Statistics_tab'):
                    volatility_stats_tab = widget.Volatility_Statistics_tab
                    if hasattr(volatility_stats_tab, 'volatility_graph_tab'):
                        return volatility_stats_tab.volatility_graph_tab
                widget = widget.parent()

            # If not found through parent hierarchy, try to find MainWindow in top level widgets
            from PyQt6.QtWidgets import QApplication
            for widget in QApplication.topLevelWidgets():
                if type(widget).__name__ == 'MainWindow':
                    if hasattr(widget, 'Volatility_Statistics_tab'):
                        volatility_stats_tab = widget.Volatility_Statistics_tab
                        if hasattr(volatility_stats_tab, 'volatility_graph_tab'):
                            return volatility_stats_tab.volatility_graph_tab

            return None
        except Exception as e:
            print(f"Error getting volatility graph tab: {str(e)}")
            return None

    def add_volatility_lines(self):
        """Add horizontal lines for volatility levels from volatility_graph.py"""
        try:
            # Remove existing volatility lines first
            self.remove_volatility_lines()

            # Volatility levels are now controlled only by AK's weekly vol zones
            # No need to check display_options anymore

            # Get the volatility graph tab
            volatility_tab = self.get_volatility_graph_tab()
            if volatility_tab is None:
                print("Could not find volatility graph tab")
                return

            # Check if AK's weekly vol zones is active
            # Only show levels when AK's weekly vol zones button is pressed
            if not hasattr(volatility_tab, 'ak_weekly_vol_zones_active') or not volatility_tab.ak_weekly_vol_zones_active:
                print("AK's weekly vol zones not active - no volatility levels will be shown")
                return

            # Get the volatility levels (will be AK's levels when active)
            volatility_levels = volatility_tab.get_volatility_levels()
            if not volatility_levels:
                print("No AK weekly vol zones levels available")
                return

            # Define the levels we want to show as infinite horizontal lines
            # For AK's weekly vol zones, we show ALL calculated levels from both H/L and weekday calculations
            # Using the same color scheme as zones: Max=Purple, MaxAvg=Cyan, Avg=Green, Median=Yellow, MinAvg=White, Apex=Orange
            levels_to_show = {}

            # Add all available levels dynamically
            for level_key in volatility_levels.keys():
                if level_key.startswith('weekday_'):
                    # Weekday levels - use same colors as zones but distinguish with "WD" prefix
                    base_key = level_key.replace('weekday_', '')
                    if base_key == 'highest_high':
                        levels_to_show[level_key] = {'label': 'WD Max', 'color': '#9C27B0', 'style': 'solid'}  # Purple
                    elif base_key == 'maxavg_high':
                        levels_to_show[level_key] = {'label': 'WD MaxAvg', 'color': '#00BCD4', 'style': 'solid'}  # Cyan
                    elif base_key == 'avg_high_lowest_high':
                        levels_to_show[level_key] = {'label': 'WD High Med', 'color': '#FFEB3B', 'style': 'solid'}  # Yellow
                    elif base_key == 'minavg_high':
                        levels_to_show[level_key] = {'label': 'WD MinAvg', 'color': '#FFFFFF', 'style': 'solid'}  # White
                    elif base_key == 'true_avg_high':
                        levels_to_show[level_key] = {'label': 'WD Avg', 'color': '#4CAF50', 'style': 'solid'}  # Green
                    elif base_key == 'apex':
                        levels_to_show[level_key] = {'label': 'WD Apex', 'color': '#FF9800', 'style': 'solid'}  # Orange
                    elif base_key == 'true_avg_low':
                        levels_to_show[level_key] = {'label': 'WD Avg', 'color': '#4CAF50', 'style': 'solid'}  # Green
                    elif base_key == 'minavg_low':
                        levels_to_show[level_key] = {'label': 'WD MinAvg', 'color': '#FFFFFF', 'style': 'solid'}  # White
                    elif base_key == 'avg_low_highest_low':
                        levels_to_show[level_key] = {'label': 'WD Low Med', 'color': '#FFEB3B', 'style': 'solid'}  # Yellow
                    elif base_key == 'maxavg_low':
                        levels_to_show[level_key] = {'label': 'WD MaxAvg', 'color': '#00BCD4', 'style': 'solid'}  # Cyan
                    elif base_key == 'lowest_low':
                        levels_to_show[level_key] = {'label': 'WD Max', 'color': '#9C27B0', 'style': 'solid'}  # Purple
                else:
                    # H/L FWL5 levels - use same colors as zones
                    if level_key == 'highest_high':
                        levels_to_show[level_key] = {'label': 'HL Max', 'color': '#9C27B0', 'style': 'solid'}  # Purple
                    elif level_key == 'maxavg_high':
                        levels_to_show[level_key] = {'label': 'HL MaxAvg', 'color': '#00BCD4', 'style': 'solid'}  # Cyan
                    elif level_key == 'avg_high_lowest_high':
                        levels_to_show[level_key] = {'label': 'HL High Med', 'color': '#FFEB3B', 'style': 'solid'}  # Yellow
                    elif level_key == 'minavg_high':
                        levels_to_show[level_key] = {'label': 'HL MinAvg', 'color': '#FFFFFF', 'style': 'solid'}  # White
                    elif level_key == 'true_avg_high':
                        levels_to_show[level_key] = {'label': 'HL Avg', 'color': '#4CAF50', 'style': 'solid'}  # Green
                    elif level_key == 'apex':
                        levels_to_show[level_key] = {'label': 'HL Apex', 'color': '#FF9800', 'style': 'solid'}  # Orange
                    elif level_key == 'true_avg_low':
                        levels_to_show[level_key] = {'label': 'HL Avg', 'color': '#4CAF50', 'style': 'solid'}  # Green
                    elif level_key == 'minavg_low':
                        levels_to_show[level_key] = {'label': 'HL MinAvg', 'color': '#FFFFFF', 'style': 'solid'}  # White
                    elif level_key == 'avg_low_highest_low':
                        levels_to_show[level_key] = {'label': 'HL Low Med', 'color': '#FFEB3B', 'style': 'solid'}  # Yellow
                    elif level_key == 'maxavg_low':
                        levels_to_show[level_key] = {'label': 'HL MaxAvg', 'color': '#00BCD4', 'style': 'solid'}  # Cyan
                    elif level_key == 'lowest_low':
                        levels_to_show[level_key] = {'label': 'HL Max', 'color': '#9C27B0', 'style': 'solid'}  # Purple

                # Check for density graph peaks and troughs (H/L matching only)
                if 'density_' in level_key:
                    # Density graph peaks and troughs from H/L matching - use distinct colors and styles
                    if 'peak' in level_key:
                        label = f'HL Peak {level_key.split("_")[-1]}'
                        color = '#E91E63'  # Pink for H/L density peaks
                        levels_to_show[level_key] = {'label': label, 'color': color, 'style': 'dashed'}
                    elif 'trough' in level_key:
                        label = f'HL Trough {level_key.split("_")[-1]}'
                        color = '#3F51B5'  # Indigo for H/L density troughs
                        levels_to_show[level_key] = {'label': label, 'color': color, 'style': 'dashed'}

            # Add horizontal lines for each level
            for level_key, level_config in levels_to_show.items():
                if level_key in volatility_levels and volatility_levels[level_key] is not None:
                    price = volatility_levels[level_key]

                    # Create pen with appropriate style
                    pen_style = QtCore.Qt.PenStyle.DashLine if level_config.get('style') == 'dashed' else QtCore.Qt.PenStyle.SolidLine
                    pen = pg.mkPen(color=level_config['color'], width=1, style=pen_style)

                    # Create infinite horizontal line
                    line = pg.InfiniteLine(
                        pos=price,
                        angle=0,  # Horizontal line
                        pen=pen,
                        label=f"{level_config['label']}: {price:.2f}",
                        labelOpts={'color': level_config['color'], 'position': 0.05}
                    )

                    # Add line to plot
                    self.price_plot.addItem(line)

                    # Store reference to line for later removal
                    self.volatility_lines.append(line)

                    print(f"Added AK weekly vol zones line: {level_config['label']} at {price:.2f}")

            print(f"Added {len(self.volatility_lines)} AK weekly vol zones lines")

            # Add zones between corresponding weekday and H/L levels
            self.add_volatility_zones(volatility_levels)

        except Exception as e:
            print(f"Error adding volatility lines: {str(e)}")
            import traceback
            traceback.print_exc()

    def remove_volatility_lines(self):
        """Remove all volatility level lines and zones from the chart"""
        try:
            # Remove volatility lines
            for line in self.volatility_lines:
                self.price_plot.removeItem(line)
            self.volatility_lines.clear()

            # Remove volatility zones
            for zone in self.volatility_zones:
                self.price_plot.removeItem(zone)
            self.volatility_zones.clear()
        except Exception as e:
            print(f"Error removing volatility lines and zones: {str(e)}")

    def add_ak_daily_vol_zones_lines(self, volatility_levels, dtl_value, matching_type, solid=True, line_type="general"):
        """Add horizontal lines for AK daily vol zones from cached data"""
        try:
            lines_added = 0

            # Color scheme: Max=Purple, MaxAvg=Cyan, Avg=Green, Median=Yellow, MinAvg=White, Apex=Orange
            level_colors = {
                'highest_high': '#9C27B0',      # Purple - Max
                'maxavg_high': '#00BCD4',       # Cyan - MaxAvg
                'avg_high_lowest_high': '#FFEB3B',  # Yellow - Median
                'minavg_high': '#FFFFFF',       # White - MinAvg
                'true_avg_high': '#4CAF50',     # Green - Avg
                'apex': '#FF9800',              # Orange - Apex
                'true_avg_low': '#4CAF50',      # Green - Avg
                'minavg_low': '#FFFFFF',        # White - MinAvg
                'avg_low_highest_low': '#FFEB3B',   # Yellow - Median
                'maxavg_low': '#00BCD4',        # Cyan - MaxAvg
                'lowest_low': '#9C27B0'         # Purple - Max
            }

            # Determine line style based on matching type
            line_style = QtCore.Qt.PenStyle.SolidLine if solid else QtCore.Qt.PenStyle.DotLine
            style_text = "solid" if solid else "dotted"

            # Plot all levels that exist in the volatility_levels data
            for level_key, price in volatility_levels.items():
                if price is not None:
                    # Check if this is a greyed out level
                    is_greyed = level_key.endswith('_greyed')
                    base_level_key = level_key.replace('_greyed', '') if is_greyed else level_key

                    if base_level_key in level_colors:
                        color = level_colors[base_level_key]
                        label = f"DTL{dtl_value} {matching_type} {base_level_key.replace('_', ' ').title()}"

                        if is_greyed:
                            # Make greyed out levels visually distinct
                            color = '#808080'  # Grey color
                            label += " (Filtered)"
                            # Use thinner line and different style for greyed out levels
                            pen = pg.mkPen(color=color, width=1, style=QtCore.Qt.PenStyle.DashDotLine)
                        else:
                            # Normal level styling
                            pen = pg.mkPen(color=color, width=2, style=line_style)

                        # Create infinite horizontal line
                        line = pg.InfiniteLine(
                            pos=price,
                            angle=0,  # Horizontal line
                            pen=pen,
                            label=f"{label}: {price:.2f}",
                            labelOpts={'color': color, 'position': 0.95}
                        )

                        # Add line to plot
                        self.price_plot.addItem(line)

                        # Store reference to line in appropriate array
                        self.ak_daily_vol_zones_lines.append(line)  # General array
                        if line_type == "hl":
                            self.ak_hl_zones_lines.append(line)
                        elif line_type == "weekday":
                            self.ak_weekday_zones_lines.append(line)

                        lines_added += 1

                        status = "greyed out" if is_greyed else "active"
                        print(f"Added AK daily vol zones line: {label} at {price:.2f} (color: {color}, style: {style_text}, type: {line_type}, status: {status})")

            return lines_added

        except Exception as e:
            print(f"Error adding AK daily vol zones lines: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0

    def remove_ak_daily_vol_zones_lines(self):
        """Remove all AK daily vol zones lines from the chart"""
        try:
            # Remove all AK daily vol zones lines
            for line in self.ak_daily_vol_zones_lines:
                self.price_plot.removeItem(line)
            self.ak_daily_vol_zones_lines.clear()

            # Clear separate arrays as well
            self.ak_hl_zones_lines.clear()
            self.ak_weekday_zones_lines.clear()

            print("Removed all AK daily vol zones lines")
        except Exception as e:
            print(f"Error removing AK daily vol zones lines: {str(e)}")

    def toggle_hl_zones(self, state):
        """Toggle visibility of H/L matching zones"""
        try:
            show_hl = state == QtCore.Qt.CheckState.Checked.value
            print(f"Toggling H/L zones: {'show' if show_hl else 'hide'}")

            for line in self.ak_hl_zones_lines:
                line.setVisible(show_hl)

        except Exception as e:
            print(f"Error toggling H/L zones: {str(e)}")

    def toggle_weekday_zones(self, state):
        """Toggle visibility of weekday matching zones"""
        try:
            show_weekday = state == QtCore.Qt.CheckState.Checked.value
            print(f"Toggling weekday zones: {'show' if show_weekday else 'hide'}")

            for line in self.ak_weekday_zones_lines:
                line.setVisible(show_weekday)

        except Exception as e:
            print(f"Error toggling weekday zones: {str(e)}")

    def add_volatility_zones(self, volatility_levels):
        """Add filled zones between corresponding weekday and H/L levels"""
        try:
            # Define level pairs that should be connected with zones
            # Note: Apex levels are excluded from zone creation as requested
            level_pairs = [
                ('highest_high', 'weekday_highest_high', 'Max High Zone'),
                ('maxavg_high', 'weekday_maxavg_high', 'MaxAvg High Zone'),
                ('avg_high_lowest_high', 'weekday_avg_high_lowest_high', 'High Median Zone'),
                ('minavg_high', 'weekday_minavg_high', 'MinAvg High Zone'),
                ('true_avg_high', 'weekday_true_avg_high', 'Avg High Zone'),
                # ('apex', 'weekday_apex', 'Apex Zone'),  # REMOVED - No apex zones
                ('true_avg_low', 'weekday_true_avg_low', 'Avg Low Zone'),
                ('minavg_low', 'weekday_minavg_low', 'MinAvg Low Zone'),
                ('avg_low_highest_low', 'weekday_avg_low_highest_low', 'Low Median Zone'),
                ('maxavg_low', 'weekday_maxavg_low', 'MaxAvg Low Zone'),
                ('lowest_low', 'weekday_lowest_low', 'Max Low Zone')
            ]

            zones_created = 0

            for hl_key, wd_key, zone_name in level_pairs:
                # Check if both levels exist
                if hl_key in volatility_levels and wd_key in volatility_levels:
                    hl_value = volatility_levels[hl_key]
                    wd_value = volatility_levels[wd_key]

                    if hl_value is not None and wd_value is not None:
                        # Create zone between the two levels
                        self.create_zone(hl_value, wd_value, zone_name)
                        zones_created += 1

            print(f"Added {zones_created} volatility zones")

        except Exception as e:
            print(f"Error adding volatility zones: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_zone(self, level1, level2, zone_name):
        """Create a filled zone between two price levels using LinearRegionItem"""
        try:
            # Determine the bounds of the zone
            min_level = min(level1, level2)
            max_level = max(level1, level2)

            # Skip zones that are too small (less than 0.01 difference)
            if abs(max_level - min_level) < 0.01:
                print(f"Skipping zone {zone_name}: levels too close ({min_level:.4f} vs {max_level:.4f})")
                return

            # Determine zone color based on level type
            # Color scheme: Max=Purple, MaxAvg=Cyan, Avg=Green, Median=Yellow, MinAvg=White, Apex=Orange

            if 'max high' in zone_name.lower() or 'max low' in zone_name.lower():
                # Max levels - use purple with transparency
                zone_color = (156, 39, 176, 77)  # Purple with 30% opacity
                print(f"Zone {zone_name} - using PURPLE (Max level)")
            elif 'maxavg' in zone_name.lower():
                # MaxAvg levels - use cyan with transparency
                zone_color = (0, 188, 212, 77)  # Cyan with 30% opacity
                print(f"Zone {zone_name} - using CYAN (MaxAvg level)")
            elif 'avg' in zone_name.lower() and 'maxavg' not in zone_name.lower() and 'minavg' not in zone_name.lower():
                # Avg levels (but not MaxAvg or MinAvg) - use green with transparency
                zone_color = (76, 175, 80, 77)  # Green with 30% opacity
                print(f"Zone {zone_name} - using GREEN (Avg level)")
            elif 'median' in zone_name.lower():
                # Median levels - use yellow with transparency
                zone_color = (255, 235, 59, 77)  # Yellow with 30% opacity
                print(f"Zone {zone_name} - using YELLOW (Median level)")
            elif 'minavg' in zone_name.lower():
                # MinAvg levels - use white with transparency
                zone_color = (255, 255, 255, 77)  # White with 30% opacity
                print(f"Zone {zone_name} - using WHITE (MinAvg level)")
            elif 'apex' in zone_name.lower():
                # Apex levels - use orange with transparency
                zone_color = (255, 152, 0, 77)  # Orange with 30% opacity
                print(f"Zone {zone_name} - using ORANGE (Apex level)")
            else:
                # Default fallback - use gray with transparency
                zone_color = (158, 158, 158, 77)  # Gray with 30% opacity
                print(f"Zone {zone_name} - using GRAY (Unknown level type)")

            # Create a horizontal LinearRegionItem for the zone
            # This will automatically extend across the entire plot width
            zone_region = pg.LinearRegionItem(
                values=[min_level, max_level],
                orientation='horizontal',
                brush=pg.mkBrush(color=zone_color),
                pen=None,  # No border lines
                movable=False  # Make it non-interactive
            )

            # Add zone to plot
            self.price_plot.addItem(zone_region)

            # Store reference for later removal
            self.volatility_zones.append(zone_region)

            print(f"Created zone: {zone_name} between {min_level:.2f} and {max_level:.2f}")

        except Exception as e:
            print(f"Error creating zone {zone_name}: {str(e)}")
            import traceback
            traceback.print_exc()

    def add_ak_daily_zones(self, ak_daily_cache):
        """Add zones for isolated 200DTL/250DTL HL levels and extended counterpart zones"""
        try:
            zones_added = 0

            # Add isolated zones
            zones_added += self.add_isolated_zones(ak_daily_cache)

            # Add extended counterpart zones
            zones_added += self.add_extended_zones(ak_daily_cache)

            return zones_added

        except Exception as e:
            print(f"Error adding AK daily zones: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0

    def add_isolated_zones(self, ak_daily_cache):
        """Add zones for isolated 200DTL and 250DTL HL levels"""
        try:
            zones_added = 0

            # Check if isolated zones exist in cache
            if 'isolated_zones' not in ak_daily_cache:
                print("No isolated zones found in cache")
                return zones_added

            isolated_zones = ak_daily_cache['isolated_zones']
            if not isolated_zones:
                print("Isolated zones list is empty")
                return zones_added

            print(f"Found {len(isolated_zones)} isolated zones to plot")

            # Define colors for different level types (same as existing level colors)
            level_colors = {
                'highest_high': '#9C27B0',    # Purple - Max
                'maxavg_high': '#00BCD4',     # Cyan - MaxAvg
                'avg_high_lowest_high': '#FFEB3B',  # Yellow - Median
                'minavg_high': '#FFFFFF',     # White - MinAvg
                'true_avg_high': '#4CAF50',   # Green - Avg
                'apex': '#FF9800',            # Orange - Apex
                'true_avg_low': '#4CAF50',    # Green - Avg
                'minavg_low': '#FFFFFF',      # White - MinAvg
                'avg_low_highest_low': '#FFEB3B',  # Yellow - Median
                'maxavg_low': '#00BCD4',      # Cyan - MaxAvg
                'lowest_low': '#9C27B0'       # Purple - Max
            }

            # Create zones for each isolated level
            for zone_info in isolated_zones:
                level_key = zone_info['level_key']
                zone_lower = zone_info['zone_lower']
                zone_upper = zone_info['zone_upper']
                zone_name = zone_info['zone_name']

                # Get color for this level type
                zone_color = level_colors.get(level_key, '#808080')  # Default to gray

                # Create zone with 30% transparency
                zone_color_with_alpha = zone_color + '4D'  # Add 30% alpha (hex 4D = 77/255 ≈ 30%)

                # Create a horizontal LinearRegionItem for the isolated zone
                zone_region = pg.LinearRegionItem(
                    values=[zone_lower, zone_upper],
                    orientation='horizontal',
                    brush=pg.mkBrush(color=zone_color_with_alpha),
                    pen=pg.mkPen(color=zone_color, width=1, style=QtCore.Qt.PenStyle.DashLine),  # Dashed border
                    movable=False  # Make it non-interactive
                )

                # Add zone to plot
                self.price_plot.addItem(zone_region)

                # Store reference for later removal
                self.volatility_zones.append(zone_region)

                zones_added += 1
                print(f"Created isolated zone: {zone_name} from {zone_lower:.4f} to {zone_upper:.4f}")

            print(f"Successfully added {zones_added} isolated level zones")
            return zones_added

        except Exception as e:
            print(f"Error adding isolated level zones: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0

    def add_extended_zones(self, ak_daily_cache):
        """Add extended zones for 200DTL and 250DTL HL levels with counterparts"""
        try:
            zones_added = 0

            # Check if extended zones exist in cache
            if 'extended_zones' not in ak_daily_cache:
                print("No extended zones found in cache")
                return zones_added

            extended_zones = ak_daily_cache['extended_zones']
            if not extended_zones:
                print("Extended zones list is empty")
                return zones_added

            print(f"Found {len(extended_zones)} extended zones to plot")

            # Define colors for different level types (same as existing level colors)
            level_colors = {
                'highest_high': '#9C27B0',    # Purple - Max
                'maxavg_high': '#00BCD4',     # Cyan - MaxAvg
                'avg_high_lowest_high': '#FFEB3B',  # Yellow - Median
                'minavg_high': '#FFFFFF',     # White - MinAvg
                'true_avg_high': '#4CAF50',   # Green - Avg
                'apex': '#FF9800',            # Orange - Apex
                'true_avg_low': '#4CAF50',    # Green - Avg
                'minavg_low': '#FFFFFF',      # White - MinAvg
                'avg_low_highest_low': '#FFEB3B',  # Yellow - Median
                'maxavg_low': '#00BCD4',      # Cyan - MaxAvg
                'lowest_low': '#9C27B0'       # Purple - Max
            }

            # Create zones for each extended level
            for zone_info in extended_zones:
                level_key = zone_info['level_key']
                zone_lower = zone_info['zone_lower']
                zone_upper = zone_info['zone_upper']
                zone_name = zone_info['zone_name']
                counterpart_count = zone_info.get('counterpart_count', 0)

                # Get color for this level type
                zone_color = level_colors.get(level_key, '#808080')  # Default to gray

                # Create zone with 30% transparency (same as isolated zones)
                zone_color_with_alpha = zone_color + '4D'  # Add 30% alpha (hex 4D = 77/255 ≈ 30%)

                # Create a horizontal LinearRegionItem for the extended zone
                # Use solid border to distinguish from isolated zones (which use dashed)
                zone_region = pg.LinearRegionItem(
                    values=[zone_lower, zone_upper],
                    orientation='horizontal',
                    brush=pg.mkBrush(color=zone_color_with_alpha),
                    pen=pg.mkPen(color=zone_color, width=2, style=QtCore.Qt.PenStyle.SolidLine),  # Solid border
                    movable=False  # Make it non-interactive
                )

                # Add zone to plot
                self.price_plot.addItem(zone_region)

                # Store reference for later removal
                self.volatility_zones.append(zone_region)

                zones_added += 1
                print(f"Created extended zone: {zone_name} from {zone_lower:.4f} to {zone_upper:.4f} "
                      f"(includes {counterpart_count} counterparts)")

            print(f"Successfully added {zones_added} extended counterpart zones")
            return zones_added

        except Exception as e:
            print(f"Error adding extended zones: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0

    def get_current_derivation_price(self):
        """Get the current derivation price (latest close price) for zone color determination"""
        try:
            if hasattr(self, 'data') and not self.data.empty:
                # Use the latest close price as the derivation price
                current_price = self.data['Close'].iloc[-1]
                print(f"Current derivation price: {current_price:.2f}")
                return current_price
            else:
                # Fallback: try to get price from candlestick data
                if hasattr(self, 'candlestick_data') and len(self.candlestick_data) > 0:
                    # Get the last candlestick's close price
                    last_candle = self.candlestick_data[-1]
                    current_price = last_candle[4]  # Close price is at index 4
                    print(f"Current derivation price from candlestick: {current_price:.2f}")
                    return current_price
                else:
                    # Ultimate fallback: use a reasonable default
                    print("No price data available, using fallback price of 100")
                    return 100.0
        except Exception as e:
            print(f"Error getting current derivation price: {str(e)}")
            return 100.0  # Fallback price

    def update_data(self):
        """Update data periodically."""
        if not self.symbol_input.text().strip():
            return

        try:
            symbol = self.symbol_input.text().strip().upper()
            ticker = yf.Ticker(symbol)

            new_data = ticker.history(
                period="1d",
                interval=self.timeframe_combo.currentText()
            )

            if new_data.empty:
                return

            if new_data.index.tz is not None:
                new_data.index = new_data.index.tz_localize(None)

            if len(new_data) > 0 and (self.data is None or new_data.index[-1] > self.data.index[-1]):
                if self.data is not None:
                    combined = pd.concat([self.data, new_data])
                    self.data = combined[~combined.index.duplicated(keep='last')]
                else:
                    self.data = new_data

                self.update_chart()
                self.status_bar.setText(f"{symbol} - Last update: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"Error updating data: {str(e)}")
            self.update_timer.stop()

    def calculate_heikin_ashi(self, data):
        """Calculate Heikin-Ashi candles from regular OHLC data."""
        ha_data = pd.DataFrame(index=data.index)

        # Calculate Heikin-Ashi values
        ha_data['HA_Close'] = (data['Open'] + data['High'] + data['Low'] + data['Close']) / 4

        # Calculate HA_Open
        ha_data['HA_Open'] = data['Open'].copy()
        for i in range(1, len(data)):
            ha_data['HA_Open'].iloc[i] = (ha_data['HA_Open'].iloc[i-1] + ha_data['HA_Close'].iloc[i-1]) / 2

        # Calculate HA_High and HA_Low
        ha_data['HA_High'] = data[['High', 'Open', 'Close']].max(axis=1)
        ha_data['HA_Low'] = data[['Low', 'Open', 'Close']].min(axis=1)

        return ha_data

    def update_from_market_odds(self, *args):
        """
        Update the chart with data from the Market Odds tab or universal controls.

        This method can handle different signal signatures:
        - update_from_market_odds(symbol, timeframe, days_to_load) from market_odds.py
        - update_from_market_odds(symbol, data) from universal_controls.py
        """
        from parameter_registry import default_registry

        try:
            # Check the number of arguments to determine which signal was used
            if len(args) == 0:
                QtWidgets.QMessageBox.warning(self, "Error", "No arguments provided")
                return

            # Handle the case where we receive (symbol, data) from universal_controls.py
            if len(args) == 2 and isinstance(args[1], pd.DataFrame):
                symbol = args[0]
                data = args[1]

                # Update the symbol input
                if isinstance(symbol, str):
                    self.symbol_input.setText(symbol)

                # Use the data directly
                self.data = data
                self.update_chart()
                return

            # Handle the case where we receive (symbol, timeframe, days_to_load) from market_odds.py
            if len(args) == 3 and isinstance(args[0], str) and isinstance(args[1], str) and isinstance(args[2], int):
                symbol = args[0]
                timeframe = args[1]
                days_to_load = args[2]

                # Update the symbol input
                self.symbol_input.setText(symbol)

                # Fetch the data using the parameters
                try:
                    # Set wait cursor
                    QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

                    # Fetch data
                    ticker = yf.Ticker(symbol)
                    period = f"{days_to_load}d"

                    self.data = ticker.history(period=period, interval=timeframe)

                    if self.data.empty:
                        QtWidgets.QMessageBox.warning(self, "Error", "No data returned for symbol")
                        QtWidgets.QApplication.restoreOverrideCursor()
                        return

                    if self.data.index.tz is not None:
                        self.data.index = self.data.index.tz_localize(None)

                    # Update the chart
                    self.update_chart()

                except Exception as e:
                    QtWidgets.QMessageBox.warning(self, "Error", f"Failed to fetch data: {str(e)}")
                finally:
                    # Restore cursor
                    QtWidgets.QApplication.restoreOverrideCursor()
                return

            # If we get here, we have an unknown argument signature
            # Try to handle it as best we can
            symbol_or_data = args[0]
            timeframe = args[1] if len(args) > 1 and isinstance(args[1], str) else None
            days_to_load = args[2] if len(args) > 2 and isinstance(args[2], int) else None

            # If data is provided directly, use it
            if isinstance(symbol_or_data, pd.DataFrame):
                self.data = symbol_or_data
                # Try to get symbol from data attributes if available
                if hasattr(symbol_or_data, 'attrs') and 'symbol' in symbol_or_data.attrs:
                    self.symbol_input.setText(symbol_or_data.attrs['symbol'])
                self.update_chart()
                return

            # Otherwise, symbol_or_data should be a string symbol
            if not isinstance(symbol_or_data, str):
                QtWidgets.QMessageBox.warning(self, "Error", "Invalid symbol type")
                return

            # Update the symbol input
            symbol = symbol_or_data
            self.symbol_input.setText(symbol)

            # Fetch the data using the parameters
            try:
                # Set wait cursor
                QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

                # Get parameters from registry if not provided
                if timeframe is None:
                    timeframe = default_registry.get_value('timeframe')
                if days_to_load is None:
                    days_to_load = default_registry.get_value('days_to_load')

                # Fetch data
                ticker = yf.Ticker(symbol)
                period = f"{days_to_load}d"

                self.data = ticker.history(period=period, interval=timeframe)

                if self.data.empty:
                    QtWidgets.QMessageBox.warning(self, "Error", "No data returned for symbol")
                    QtWidgets.QApplication.restoreOverrideCursor()
                    return

                if self.data.index.tz is not None:
                    self.data.index = self.data.index.tz_localize(None)

                # Update the chart
                self.update_chart()

            except Exception as e:
                QtWidgets.QMessageBox.warning(self, "Error", f"Failed to fetch data: {str(e)}")
            finally:
                # Restore cursor
                QtWidgets.QApplication.restoreOverrideCursor()

            # Update status bar
            self.status_bar.setText(f"Updated chart - {datetime.now().strftime('%H:%M:%S')}")
        except Exception as e:
            print(f"Error updating from Market Odds: {str(e)}")
            traceback.print_exc()

    def update_chart(self):
        """Update the chart with current data."""
        if self.data.empty:
            return

        # We'll recreate the crosshair from scratch

        # Clear previous items
        self.price_plot.clear()

        # Clear reference arrays since plot.clear() removes all items
        self.volatility_lines.clear()
        self.volatility_zones.clear()
        self.ak_daily_vol_zones_lines.clear()
        self.ak_hl_zones_lines.clear()
        self.ak_weekday_zones_lines.clear()
        self.ak_intersect_zones_lines.clear()

        # Set up crosshair (either for the first time or re-create it)
        self.setup_crosshair()

        # Store references to items we'll need to access later
        self.vector_item = None
        self.vector_legend = None

        # Check if we have the required OHLC columns
        required_columns = ['Open', 'High', 'Low', 'Close']
        if not all(col in self.data.columns for col in required_columns):
            QtWidgets.QMessageBox.warning(self, "Warning", "Data does not contain required OHLC columns.")
            return

        # Get candlestick type
        candlestick_type = self.candlestick_type_selector.currentText()

        # Prepare data based on candlestick type
        if candlestick_type == "Heikin-Ashi":
            ha_data = self.calculate_heikin_ashi(self.data)
            plot_data = self.data.copy()
            plot_data['Open'] = ha_data['HA_Open']
            plot_data['High'] = ha_data['HA_High']
            plot_data['Low'] = ha_data['HA_Low']
            plot_data['Close'] = ha_data['HA_Close']
        else:
            plot_data = self.data.copy()

        # Create index values for PyQtGraph (each candle gets an integer x-coordinate)
        # This is used for all the plotting items

        # Prepare data for CandlestickItem
        self.ohlc_data = []
        for i, (_, row) in enumerate(plot_data.iterrows()):
            # Use index position as x-coordinate
            self.ohlc_data.append((i, row['Open'], row['High'], row['Low'], row['Close']))

        # Set axis range
        self.price_plot.setXRange(max(0, len(plot_data) - 100), len(plot_data) + 10)

        # Set colors
        bull_color = self.chart_colors['bullish']
        bear_color = self.chart_colors['bearish']

        # Create and add the candlestick item
        if candlestick_type == "Hollow":
            # For hollow candles, we need a custom CandlestickItem
            class HollowCandlestickItem(CandlestickItem):
                def generatePicture(self):
                    self.picture = QtGui.QPicture()
                    p = QtGui.QPainter(self.picture)
                    w = 0.4
                    for (t, open, high, low, close) in self.data:
                        # Make the first 2 candles invisible (skip rendering them)
                        if t < 2:
                            continue

                        if close >= open:
                            # Bull candle (hollow)
                            p.setPen(pg.mkPen(self.bullish_color))
                            p.setBrush(QtGui.QBrush(QtCore.Qt.BrushStyle.NoBrush))
                        else:
                            # Bear candle (hollow)
                            p.setPen(pg.mkPen(self.bearish_color))
                            p.setBrush(QtGui.QBrush(QtCore.Qt.BrushStyle.NoBrush))

                        # Draw the wick
                        p.drawLine(QtCore.QPointF(t, low), QtCore.QPointF(t, high))

                        # Draw the body
                        p.drawRect(QtCore.QRectF(t - w, open, w * 2, close - open))
                    p.end()

            self.candlestick_item = HollowCandlestickItem(self.ohlc_data, bullish_color=bull_color, bearish_color=bear_color)
        else:
            # Use regular candlestick item
            self.candlestick_item = CandlestickItem(self.ohlc_data, bullish_color=bull_color, bearish_color=bear_color)

        self.price_plot.addItem(self.candlestick_item)

        # Add vector if enabled (use the checkbox directly)
        if self.show_vector_checkbox.isChecked():
            try:
                # Import vector module
                import vector as vector_module

                # Calculate The Line using the same algorithm as in PineScript
                # Get vector length from parameter registry (this ensures it's synchronized with universal controls)
                from parameter_registry import default_registry
                wave_length = default_registry.get_value('vector_length')
                vector_values = vector_module.calculate_vector(plot_data, length=wave_length, column='Close')

                # Prepare data for VectorItem
                vector_data = []
                vector_values_list = []

                for i, (_, row) in enumerate(plot_data.iterrows()):
                    vector_data.append((i, row['Open'], row['High'], row['Low'], row['Close']))
                    vector_values_list.append(vector_values.iloc[i])

                # Create The Line item with background coloring
                self.vector_item = VectorItem(
                    data=vector_data,
                    vector_values=vector_values_list,
                    wave_length=wave_length,
                    line_color='purple',
                    line_width=2,
                    bg_up_color=(0, 100, 0, 15),  # Extremely transparent green
                    bg_down_color=(139, 0, 0, 15)  # Extremely transparent red
                )

                # Add The Line item to the plot
                self.price_plot.addItem(self.vector_item)

                # Add a legend to show The Line information
                if not hasattr(self, 'vector_legend') or self.vector_legend is None:
                    # Create a legend in the top-right corner
                    self.vector_legend = self.price_plot.addLegend(offset=(-10, 10))

                    # Create a dummy item for the legend
                    dummy_item = pg.PlotDataItem(pen=pg.mkPen(color='purple', width=2))

                    # Add the dummy item to the legend with The Line information
                    current_vector_price = vector_values.iloc[-1]
                    self.vector_legend.addItem(
                        dummy_item,
                        f"The Line ({wave_length}): {current_vector_price:.2f}"
                    )
                else:
                    # Update the legend text with current Line price
                    current_vector_price = vector_values.iloc[-1]

                    # Clear the legend and add the updated item
                    self.vector_legend.clear()

                    # Create a dummy item for the legend
                    dummy_item = pg.PlotDataItem(pen=pg.mkPen(color='purple', width=2))

                    # Add the dummy item to the legend with The Line information
                    self.vector_legend.addItem(
                        dummy_item,
                        f"The Line ({wave_length}): {current_vector_price:.2f}"
                    )

            except Exception as e:
                print(f"Error adding The Line: {str(e)}")
                traceback.print_exc()

        # Add volume profile if enabled
        if self.show_volume_profile.isChecked() and 'Volume' in self.data.columns:
            # Prepare data for VolumeProfileItem
            volume_data = []
            for i, (_, row) in enumerate(plot_data.iterrows()):
                volume_data.append((i, row['Open'], row['High'], row['Low'], row['Close'], row['Volume']))

            # Calculate price range
            price_range = (plot_data['High'].max(), plot_data['Low'].min())

            # Get volume profile settings from display options
            vp_options = {
                'num_bars': self.display_options.get('vp_num_bars', 200),
                'bar_thickness': self.display_options.get('vp_bar_thickness', 1),
                'bar_length_mult': self.display_options.get('vp_bar_length_mult', 10),
                'right_offset': self.display_options.get('vp_right_offset', 5),
                'volume_type': self.display_options.get('vp_volume_type', 'Both'),
                'display_poc': self.display_options.get('vp_display_poc', True),
                'poc_line_color': self.display_options.get('vp_poc_color', 'r'),
                'poc_line_thickness': self.display_options.get('vp_poc_line_thickness', 1),
                'display_va': self.display_options.get('vp_display_va', True),
                'va_percent': self.display_options.get('vp_va_percent', 68),
                'va_bar_color': self.display_options.get('vp_va_color', (0, 0, 255, 128)),
                'display_va_lines': self.display_options.get('vp_display_va_lines', True),
                'va_lines_thickness': self.display_options.get('vp_va_lines_thickness', 1),
                'right_aligned': self.display_options.get('vp_right_aligned', True),
                'bar_color': self.display_options.get('vp_bar_color', (100, 100, 100, 128))
            }

            # Create and add volume profile
            self.volume_profile_item = VolumeProfileItem(
                volume_data,
                price_range,
                num_bars=vp_options['num_bars'],
                bar_thickness=vp_options['bar_thickness'],
                bar_length_mult=vp_options['bar_length_mult'],
                right_offset=vp_options['right_offset'],
                volume_type=vp_options['volume_type'],
                bar_color=vp_options['bar_color'],
                display_poc=vp_options['display_poc'],
                poc_line_color=vp_options['poc_line_color'],
                poc_line_thickness=vp_options['poc_line_thickness'],
                display_va=vp_options['display_va'],
                va_percent=vp_options['va_percent'],
                va_bar_color=vp_options['va_bar_color'],
                display_va_lines=vp_options['display_va_lines'],
                va_lines_thickness=vp_options['va_lines_thickness'],
                right_aligned=vp_options['right_aligned']
            )

            self.price_plot.addItem(self.volume_profile_item)

        # Add date axis labels
        if len(plot_data) > 0:
            # Create a list of tuples (position, label) for major ticks
            ticks = []
            # Add a tick every 20 candles
            step = max(1, len(plot_data) // 10)
            for i in range(0, len(plot_data), step):
                if i < len(plot_data):
                    date = plot_data.index[i]
                    label = date.strftime("%m-%d")
                    ticks.append((i, label))

            # Set custom ticks
            self.price_plot.getAxis('bottom').setTicks([ticks, []])

        # Auto-scale Y axis to fit the data
        self.price_plot.enableAutoRange(axis='y')

        # Add current price line
        if len(plot_data) > 0:
            current_price = plot_data['Close'].iloc[-1]
            price_line = pg.InfiniteLine(
                pos=current_price,
                angle=0,
                pen=pg.mkPen(color='#FFEB3B', width=1, style=QtCore.Qt.PenStyle.DashLine),
                label=f"{current_price:.2f}",
                labelOpts={'color': '#FFEB3B', 'position': 0.95}
            )
            self.price_plot.addItem(price_line)

        # Add volatility levels as infinite horizontal lines
        self.add_volatility_lines()

    def open_settings_dialog(self):
        """Open the settings dialog"""
        if self.settings_dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            # Get new settings
            new_colors = self.settings_dialog.get_chart_colors()
            new_options = self.settings_dialog.get_display_options()

            # Apply settings
            self.apply_settings(new_colors, new_options)

            # Save settings
            self.save_chart_settings()

    def toggle_vector(self):
        """Toggle The Line display based on checkbox state"""
        # Update display options
        self.display_options['show_vector'] = self.show_vector_checkbox.isChecked()

        # Update chart
        self.update_chart()

    # toggle_volatility_levels method REMOVED
    # Volatility levels are now controlled only by AK's weekly vol zones button

    def plot_levels(self):
        """Plot Levels button - refreshes the chart to update volatility levels display"""
        try:
            print("Plot Levels button clicked - refreshing chart")
            self.update_chart()
            print("Chart refreshed successfully")
        except Exception as e:
            print(f"Error refreshing chart from Plot Levels button: {str(e)}")
            import traceback
            traceback.print_exc()

    def plot_ak_daily_vol_zones(self):
        """Plot AK Daily Vol Zones button - plots cached volatility levels from 200, 250, 500 DTL"""
        try:
            print("Plot AK Daily Vol Zones button clicked")

            # Get the volatility graph tab
            volatility_tab = self.get_volatility_graph_tab()
            if volatility_tab is None:
                print("Could not find volatility graph tab")
                QtWidgets.QMessageBox.information(self, "AK Daily Vol Zones", "Could not find volatility graph tab")
                return

            # Check if AK's daily vol zones is active
            if not hasattr(volatility_tab, 'ak_daily_vol_zones_active') or not volatility_tab.ak_daily_vol_zones_active:
                QtWidgets.QMessageBox.information(self, "AK Daily Vol Zones",
                    "AK's daily vol zones is not active.\nPlease activate it first from the Volatility Statistics tab.")
                return

            # Check if we have cached data
            if not hasattr(volatility_tab, 'ak_daily_vol_zones_cache') or not volatility_tab.ak_daily_vol_zones_cache:
                QtWidgets.QMessageBox.information(self, "AK Daily Vol Zones",
                    "No cached volatility data available.\nPlease ensure AK's daily vol zones has finished loading.")
                return

            # Debug: Print cache contents
            print(f"AK Daily Vol Zones Cache contents: {list(volatility_tab.ak_daily_vol_zones_cache.keys())}")
            for key, value in volatility_tab.ak_daily_vol_zones_cache.items():
                if 'volatility' in key:
                    print(f"Cache key {key}: {len(value) if value else 0} levels")
                    if value:
                        print(f"  Available levels: {list(value.keys())}")

            # Remove existing AK daily vol zones lines first
            self.remove_ak_daily_vol_zones_lines()

            # Plot volatility levels for each DTL
            dtl_values = [200, 250, 500]
            total_lines_added = 0

            for i, dtl_value in enumerate(dtl_values):
                # Plot H/L matching levels (solid lines) if checkbox is checked
                if self.show_hl_zones_checkbox.isChecked():
                    hl_cache_key = f"dtl_{dtl_value}_volatility"
                    print(f"Looking for H/L cache key: {hl_cache_key}")
                    if hl_cache_key in volatility_tab.ak_daily_vol_zones_cache:
                        volatility_levels = volatility_tab.ak_daily_vol_zones_cache[hl_cache_key]
                        print(f"Found H/L cache for DTL {dtl_value}: {len(volatility_levels) if volatility_levels else 0} levels")
                        if volatility_levels:
                            lines_added = self.add_ak_daily_vol_zones_lines(volatility_levels, dtl_value, "H/L", solid=True, line_type="hl")
                            total_lines_added += lines_added
                            print(f"Added {lines_added} H/L lines for DTL {dtl_value}")
                        else:
                            print(f"DTL {dtl_value}: H/L volatility_levels is empty or None")
                    else:
                        print(f"H/L cache key {hl_cache_key} not found in cache")
                else:
                    print(f"H/L zones checkbox unchecked, skipping H/L levels for DTL {dtl_value}")

                # Plot weekday matching levels (dotted lines) if checkbox is checked
                if self.show_weekday_zones_checkbox.isChecked():
                    weekday_cache_key = f"dtl_{dtl_value}_weekday_volatility"
                    print(f"Looking for weekday cache key: {weekday_cache_key}")
                    if weekday_cache_key in volatility_tab.ak_daily_vol_zones_cache:
                        weekday_levels = volatility_tab.ak_daily_vol_zones_cache[weekday_cache_key]
                        print(f"Found weekday cache for DTL {dtl_value}: {len(weekday_levels) if weekday_levels else 0} levels")
                        if weekday_levels:
                            lines_added = self.add_ak_daily_vol_zones_lines(weekday_levels, dtl_value, "Weekday", solid=False, line_type="weekday")
                            total_lines_added += lines_added
                            print(f"Added {lines_added} weekday lines for DTL {dtl_value}")
                        else:
                            print(f"DTL {dtl_value}: weekday volatility_levels is empty or None")
                    else:
                        print(f"Weekday cache key {weekday_cache_key} not found in cache")
                else:
                    print(f"Weekday zones checkbox unchecked, skipping weekday levels for DTL {dtl_value}")

            # Plot isolated level zones and extended zones if they exist
            zones_added = self.add_ak_daily_zones(volatility_tab.ak_daily_vol_zones_cache)

            if total_lines_added > 0 or zones_added > 0:
                message = f"Successfully plotted {total_lines_added} volatility levels"
                if zones_added > 0:
                    message += f" and {zones_added} isolated level zones"
                message += " from cached DTL data."
                print(message)
                QtWidgets.QMessageBox.information(self, "AK Daily Vol Zones", message)
            else:
                QtWidgets.QMessageBox.warning(self, "AK Daily Vol Zones",
                    "No volatility levels found in cached data.")

        except Exception as e:
            print(f"Error plotting AK daily vol zones: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Error", f"Error plotting AK daily vol zones: {str(e)}")

    def plot_ak_intersect_zones(self):
        """Plot AK Intersect Zones button - plots cached theoretical intersects from odds zones"""
        try:
            print("Plot AK Intersect Zones button clicked")

            # Get the volatility statistics tab to access cached intersects
            volatility_stats_tab = self.get_volatility_statistics_tab()
            if volatility_stats_tab is None:
                print("Could not find volatility statistics tab")
                QtWidgets.QMessageBox.information(self, "AK Intersect Zones", "Could not find volatility statistics tab")
                return

            # Check if AK's odds zones is active
            if not hasattr(volatility_stats_tab, 'ak_odds_zones_active') or not volatility_stats_tab.ak_odds_zones_active:
                QtWidgets.QMessageBox.information(self, "AK Intersect Zones",
                    "AK's odds zones is not active.\nPlease activate it first from the Volatility Statistics tab.")
                return

            # Check if we have cached intersect data
            if not hasattr(volatility_stats_tab, 'ak_odds_zones_cache') or not volatility_stats_tab.ak_odds_zones_cache:
                QtWidgets.QMessageBox.warning(self, "AK Intersect Zones",
                    "No cached intersect data found.\nPlease run AK's Odds Zones first.")
                return

            # Remove existing intersect zone lines
            self.remove_ak_intersect_zones_lines()

            # Plot intersects from cached data
            total_lines_added = 0
            intersect_cache = volatility_stats_tab.ak_odds_zones_cache

            # Plot H/L matching intersects (solid lines)
            for dtl_value in [200, 250, 500]:
                cache_key = f"dtl_{dtl_value}_hl_intersects"
                if cache_key in intersect_cache and intersect_cache[cache_key]:
                    intersects = intersect_cache[cache_key]
                    lines_added = self.add_intersect_lines(intersects, f"DTL{dtl_value} H/L", "white", solid=True)
                    total_lines_added += lines_added
                    print(f"Added {lines_added} H/L intersect lines for DTL {dtl_value}")

            # Plot weekday matching intersects (dashed lines)
            for dtl_value in [200, 250, 500]:
                cache_key = f"dtl_{dtl_value}_weekday_intersects"
                if cache_key in intersect_cache and intersect_cache[cache_key]:
                    intersects = intersect_cache[cache_key]
                    lines_added = self.add_intersect_lines(intersects, f"DTL{dtl_value} Weekday", "white", solid=False)
                    total_lines_added += lines_added
                    print(f"Added {lines_added} weekday intersect lines for DTL {dtl_value}")

            if total_lines_added > 0:
                message = f"Successfully plotted {total_lines_added} theoretical intersect lines from cached odds zones data."
                print(message)
                QtWidgets.QMessageBox.information(self, "AK Intersect Zones", message)
            else:
                QtWidgets.QMessageBox.warning(self, "AK Intersect Zones",
                    "No theoretical intersects found in cached data.")

        except Exception as e:
            print(f"Error plotting AK intersect zones: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Error", f"Error plotting AK intersect zones: {str(e)}")

    def get_volatility_statistics_tab(self):
        """Get reference to the volatility statistics tab"""
        try:
            # Navigate up the widget hierarchy to find the main window
            parent = self.parent()
            while parent is not None:
                if hasattr(parent, 'volatility_statistics_tab'):
                    return parent.volatility_statistics_tab
                parent = parent.parent()

            # Alternative: Look for QTabWidget and find the volatility statistics tab
            parent = self.parent()
            while parent is not None:
                if isinstance(parent, QtWidgets.QTabWidget):
                    for i in range(parent.count()):
                        widget = parent.widget(i)
                        if hasattr(widget, 'ak_odds_zones_active'):
                            return widget
                parent = parent.parent()

            return None
        except Exception as e:
            print(f"Error getting volatility statistics tab: {str(e)}")
            return None

    def remove_ak_intersect_zones_lines(self):
        """Remove all AK intersect zones lines from the chart"""
        try:
            for line in self.ak_intersect_zones_lines:
                self.price_plot.removeItem(line)
            self.ak_intersect_zones_lines.clear()
            print(f"Removed all AK intersect zones lines")
        except Exception as e:
            print(f"Error removing AK intersect zones lines: {str(e)}")

    def add_intersect_lines(self, intersects, label_prefix, color, solid=True):
        """Add horizontal lines for theoretical intersects"""
        try:
            lines_added = 0

            for i, intersect_y in enumerate(intersects):
                # Use white color for all intersect lines to match other levels
                line_style = QtCore.Qt.PenStyle.SolidLine if solid else QtCore.Qt.PenStyle.DashLine
                pen = pg.mkPen(color='white', width=2, style=line_style)

                # Create descriptive label showing it's an odds intersect zone
                line = pg.InfiniteLine(
                    pos=intersect_y,
                    angle=0,  # Horizontal line
                    pen=pen,
                    label=f"{label_prefix} Odds Intersect: {intersect_y:.2f}",  # Clear zone identification
                    labelOpts={'color': 'white', 'position': 0.95}  # Match other levels styling
                )

                # Add line to plot
                self.price_plot.addItem(line)

                # Store reference for later removal
                self.ak_intersect_zones_lines.append(line)
                lines_added += 1

                print(f"Added {label_prefix} intersect line at {intersect_y:.2f}")

            return lines_added

        except Exception as e:
            print(f"Error adding intersect lines: {str(e)}")
            return 0

    def plot_ak_density_zones(self):
        """Plot AK Density Zones button - plots cached density zones from volatility statistics tab"""
        try:
            print("Plot AK Density Zones button clicked")

            # Get the volatility statistics tab to access cached zones
            volatility_stats_tab = self.get_volatility_statistics_tab()
            if volatility_stats_tab is None:
                print("Could not find volatility statistics tab")
                QtWidgets.QMessageBox.information(self, "AK Density Zones", "Could not find volatility statistics tab")
                return

            # Check if AK's density zones is active
            if not hasattr(volatility_stats_tab, 'ak_density_zones_active') or not volatility_stats_tab.ak_density_zones_active:
                QtWidgets.QMessageBox.information(self, "AK Density Zones",
                    "AK's density zones is not active.\nPlease activate it first from the Volatility Statistics tab.")
                return

            # Check if we have cached density zones data
            if not hasattr(volatility_stats_tab, 'ak_density_zones_cache') or not volatility_stats_tab.ak_density_zones_cache:
                QtWidgets.QMessageBox.warning(self, "AK Density Zones",
                    "No cached density zones found.\nPlease use 'Save Zones to Cache' button first.")
                return

            # Check if zones exist in cache
            if 'zones' not in volatility_stats_tab.ak_density_zones_cache or not volatility_stats_tab.ak_density_zones_cache['zones']:
                QtWidgets.QMessageBox.warning(self, "AK Density Zones",
                    "No zones found in cache.\nPlease use 'Save Zones to Cache' button first.")
                return

            # Remove existing density zone regions
            self.remove_ak_density_zones()

            # Get cached zones (they're already in the correct format)
            cached_zones = volatility_stats_tab.ak_density_zones_cache['zones']
            print(f"Found {len(cached_zones)} cached density zones")

            # Import zones to candlestick chart
            success_count = self.import_density_zones(cached_zones)

            if success_count > 0:
                print(f"Successfully plotted {success_count} AK density zones from cache")
                QtWidgets.QMessageBox.information(self, "AK Density Zones",
                    f"Successfully plotted {success_count} AK density zones from cache.")
            else:
                QtWidgets.QMessageBox.warning(self, "AK Density Zones",
                    "No zones were successfully plotted on the chart.")

        except Exception as e:
            print(f"Error plotting AK density zones: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Error", f"Error plotting AK density zones: {str(e)}")

    def get_density_graph_tab(self):
        """Get the density graph tab from the volatility statistics tab"""
        try:
            # First try to get the volatility statistics tab
            volatility_stats_tab = self.get_volatility_statistics_tab()
            if volatility_stats_tab and hasattr(volatility_stats_tab, 'density_graph_tab'):
                return volatility_stats_tab.density_graph_tab
            return None
        except Exception as e:
            print(f"Error getting density graph tab: {str(e)}")
            return None

    def remove_ak_density_zones(self):
        """Remove all AK density zones from the chart"""
        try:
            # Remove all density zones from the plot
            for zone in self.ak_density_zones:
                self.price_plot.removeItem(zone)

            # Clear the density zones list
            zones_count = len(self.ak_density_zones)
            self.ak_density_zones.clear()

            print(f"Removed {zones_count} AK density zones")
        except Exception as e:
            print(f"Error removing AK density zones: {str(e)}")

    def export_zones_to_tradingview(self):
        """Export all zones plotted on the candlestick chart to TradingView Pine Script format"""
        try:
            print("Export Zones to TradingView button clicked")

            # Collect all zones and levels from different sources
            all_zones = []
            all_levels = []

            print("Collecting zones and levels for export...")

            # 1. Get volatility zones (horizontal LinearRegionItems)
            for i, zone_item in enumerate(self.volatility_zones):
                try:
                    if hasattr(zone_item, 'getRegion'):
                        min_val, max_val = zone_item.getRegion()
                        # Get zone color
                        brush = zone_item.brush
                        if brush and hasattr(brush, 'color'):
                            color = brush.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.gray'

                        zone_data = {
                            'name': f'Volatility Zone {i + 1}',
                            'top': max_val,
                            'bottom': min_val,
                            'color': color_name,
                            'zone_type': 'volatility',
                            'description': 'Volatility Zone'
                        }
                        all_zones.append(zone_data)
                except Exception as e:
                    print(f"Error processing volatility zone {i}: {str(e)}")
                    continue

            # 2. Get AK density zones
            for i, zone_item in enumerate(self.ak_density_zones):
                try:
                    if hasattr(zone_item, 'getRegion'):
                        min_val, max_val = zone_item.getRegion()
                        # Get zone color
                        brush = zone_item.brush
                        if brush and hasattr(brush, 'color'):
                            color = brush.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.blue'

                        zone_data = {
                            'name': f'AK Density Zone {i + 1}',
                            'top': max_val,
                            'bottom': min_val,
                            'color': color_name,
                            'zone_type': 'density',
                            'description': 'AK Density Zone'
                        }
                        all_zones.append(zone_data)
                except Exception as e:
                    print(f"Error processing AK density zone {i}: {str(e)}")
                    continue

            # 3. Get volatility lines (horizontal price levels)
            for i, line_item in enumerate(self.volatility_lines):
                try:
                    if hasattr(line_item, 'value'):
                        level_value = line_item.value()
                        # Get line color
                        pen = line_item.pen
                        if pen and hasattr(pen, 'color'):
                            color = pen.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.white'

                        level_data = {
                            'name': f'Volatility Level {i + 1}',
                            'price': level_value,
                            'color': color_name,
                            'level_type': 'volatility',
                            'description': 'Volatility Level'
                        }
                        all_levels.append(level_data)
                except Exception as e:
                    print(f"Error processing volatility line {i}: {str(e)}")
                    continue

            # 4. Get AK daily vol zones lines
            for i, line_item in enumerate(self.ak_daily_vol_zones_lines):
                try:
                    if hasattr(line_item, 'value'):
                        level_value = line_item.value()
                        # Get line color
                        pen = line_item.pen
                        if pen and hasattr(pen, 'color'):
                            color = pen.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.cyan'

                        level_data = {
                            'name': f'AK Daily Vol Level {i + 1}',
                            'price': level_value,
                            'color': color_name,
                            'level_type': 'ak_daily_vol',
                            'description': 'AK Daily Vol Level'
                        }
                        all_levels.append(level_data)
                except Exception as e:
                    print(f"Error processing AK daily vol line {i}: {str(e)}")
                    continue

            # 5. Get AK intersect zones lines
            for i, line_item in enumerate(self.ak_intersect_zones_lines):
                try:
                    if hasattr(line_item, 'value'):
                        level_value = line_item.value()
                        # Get line color
                        pen = line_item.pen
                        if pen and hasattr(pen, 'color'):
                            color = pen.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.orange'

                        level_data = {
                            'name': f'AK Intersect Level {i + 1}',
                            'price': level_value,
                            'color': color_name,
                            'level_type': 'ak_intersect',
                            'description': 'AK Intersect Level'
                        }
                        all_levels.append(level_data)
                except Exception as e:
                    print(f"Error processing AK intersect line {i}: {str(e)}")
                    continue

            # 6. Get price levels from price_levels dictionary
            for level_name, level_item in self.price_levels.items():
                try:
                    if hasattr(level_item, 'value'):
                        level_value = level_item.value()
                        # Get line color
                        pen = level_item.pen
                        if pen and hasattr(pen, 'color'):
                            color = pen.color()
                            color_name = self._get_pine_color_from_qt(color)
                        else:
                            color_name = 'color.yellow'

                        level_data = {
                            'name': level_name,
                            'price': level_value,
                            'color': color_name,
                            'level_type': 'price_level',
                            'description': f'Price Level - {level_name}'
                        }
                        all_levels.append(level_data)
                except Exception as e:
                    print(f"Error processing price level {level_name}: {str(e)}")
                    continue

            total_items = len(all_zones) + len(all_levels)

            if total_items == 0:
                QtWidgets.QMessageBox.information(self, "Export Zones & Levels",
                    "No zones or levels found to export.\nPlease plot some zones or levels first.")
                return

            print(f"Found {len(all_zones)} zones and {len(all_levels)} levels for export")

            # Generate Pine Script
            pine_script = self._generate_pine_script_with_levels(all_zones, all_levels)

            # Show Pine Script in popup dialog
            self._show_pine_script_dialog(pine_script, total_items)

        except Exception as e:
            print(f"Error exporting zones to TradingView: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Export Error",
                f"Error exporting zones: {str(e)}")

    def _get_pine_color_from_qt(self, qt_color):
        """Convert Qt color to Pine Script color name"""
        try:
            # Get RGB values
            r, g, b, a = qt_color.getRgb()

            # Map common colors to Pine Script color names
            color_map = {
                (255, 0, 0): 'color.red',      # Red
                (0, 255, 0): 'color.green',    # Green
                (0, 0, 255): 'color.blue',     # Blue
                (255, 255, 0): 'color.yellow', # Yellow
                (255, 165, 0): 'color.orange', # Orange
                (128, 0, 128): 'color.purple', # Purple
                (0, 255, 255): 'color.aqua',   # Cyan/Aqua
                (255, 255, 255): 'color.white', # White
                (0, 0, 0): 'color.black',      # Black
                (128, 128, 128): 'color.gray', # Gray
            }

            # Find closest color match
            min_distance = float('inf')
            closest_color = 'color.gray'

            for (cr, cg, cb), color_name in color_map.items():
                distance = ((r - cr) ** 2 + (g - cg) ** 2 + (b - cb) ** 2) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    closest_color = color_name

            # If very close match (within 50 units), use it
            if min_distance < 50:
                return closest_color
            else:
                # Create custom color with RGB values
                return f'color.rgb({r}, {g}, {b})'

        except Exception as e:
            print(f"Error converting color: {str(e)}")
            return 'color.gray'

    def _generate_pine_script(self, zones_data):
        """Generate Pine Script code for TradingView indicator"""
        try:
            from datetime import datetime

            # Get current symbol if available
            symbol = self.symbol_input.text().strip().upper() if hasattr(self, 'symbol_input') and self.symbol_input.text().strip() else 'Unknown'

            # Pine Script header
            script_lines = [
                "//@version=6",
                f"indicator('{symbol} Zones Export', overlay = true, max_boxes_count = 500)",
                "",
                f"// Generated on {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                f"// Exported {len(zones_data)} zones from candlestick chart",
                "",
                "// Variables to store boxes and labels",
                "var box_array = array.new<box>()",
                "var label_array = array.new<label>()",
                "",
                "// Clear previous boxes and labels to prevent trail",
                "if barstate.islast",
                "    if array.size(box_array) > 0",
                "        for i = 0 to array.size(box_array) - 1 by 1",
                "            box.delete(array.get(box_array, i))",
                "        array.clear(box_array)",
                "    if array.size(label_array) > 0",
                "        for i = 0 to array.size(label_array) - 1 by 1",
                "            label.delete(array.get(label_array, i))",
                "        array.clear(label_array)",
                ""
            ]

            # Add zones as boxes
            for i, zone in enumerate(zones_data):
                zone_name = zone.get('name', f'Zone {i+1}')
                top = zone.get('top', 0)
                bottom = zone.get('bottom', 0)
                color = zone.get('color', 'color.gray')
                zone_type = zone.get('zone_type', 'zone')

                # Ensure top > bottom
                if top < bottom:
                    top, bottom = bottom, top

                script_lines.extend([
                    f"// {zone_name} ({zone_type.title()}) from {bottom:.4f} to {top:.4f}",
                    "if barstate.islast",
                    f"    zone_box = box.new(bar_index - 100, {bottom:.4f}, bar_index + 100, {top:.4f}, ",
                    f"                      border_color = {color}, bgcolor = color.new({color}, 85), ",
                    f"                      border_width = 1, extend = extend.both)",
                    "    array.push(box_array, zone_box)",
                    f"    zone_label = label.new(bar_index, {((top + bottom) / 2):.4f}, ",
                    f"                          text = '{zone_name}', style = label.style_none, ",
                    f"                          color = color.new(color.white, 100), textcolor = {color}, ",
                    "                          size = size.small)",
                    "    array.push(label_array, zone_label)",
                    ""
                ])

            # Add summary
            script_lines.extend([
                f"// Summary: Generated {len(zones_data)} zones",
                "// Zones included in this script:"
            ])

            for zone in zones_data:
                zone_name = zone.get('name', 'Unknown Zone')
                top = zone.get('top', 0)
                bottom = zone.get('bottom', 0)
                script_lines.append(f"// - {zone_name}: {bottom:.4f} to {top:.4f}")

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"Error generating Pine Script: {str(e)}")
            return f"// Error generating Pine Script: {str(e)}"

    def _generate_pine_script_with_levels(self, zones_data, levels_data):
        """Generate Pine Script code for TradingView indicator with both zones and levels"""
        try:
            from datetime import datetime

            # Get current symbol if available
            symbol = self.symbol_input.text().strip().upper() if hasattr(self, 'symbol_input') and self.symbol_input.text().strip() else 'Unknown'

            total_items = len(zones_data) + len(levels_data)

            # Pine Script header
            script_lines = [
                "//@version=6",
                f"indicator('{symbol} Zones & Levels Export', overlay = true, max_boxes_count = 500, max_lines_count = 500)",
                "",
                f"// Generated on {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                f"// Exported {len(zones_data)} zones and {len(levels_data)} levels from candlestick chart",
                "",
                "// Variables to store boxes, lines and labels",
                "var box_array = array.new<box>()",
                "var line_array = array.new<line>()",
                "var label_array = array.new<label>()",
                "",
                "// Clear previous items to prevent trail",
                "if barstate.islast",
                "    if array.size(box_array) > 0",
                "        for i = 0 to array.size(box_array) - 1 by 1",
                "            box.delete(array.get(box_array, i))",
                "        array.clear(box_array)",
                "    if array.size(line_array) > 0",
                "        for i = 0 to array.size(line_array) - 1 by 1",
                "            line.delete(array.get(line_array, i))",
                "        array.clear(line_array)",
                "    if array.size(label_array) > 0",
                "        for i = 0 to array.size(label_array) - 1 by 1",
                "            label.delete(array.get(label_array, i))",
                "        array.clear(label_array)",
                ""
            ]

            # Add zones as boxes
            for i, zone in enumerate(zones_data):
                zone_name = zone.get('name', f'Zone {i+1}')
                top = zone.get('top', 0)
                bottom = zone.get('bottom', 0)
                color = zone.get('color', 'color.gray')
                zone_type = zone.get('zone_type', 'zone')

                # Ensure top > bottom
                if top < bottom:
                    top, bottom = bottom, top

                script_lines.extend([
                    f"// {zone_name} ({zone_type.title()}) from {bottom:.4f} to {top:.4f}",
                    "if barstate.islast",
                    f"    zone_box = box.new(bar_index - 100, {bottom:.4f}, bar_index + 100, {top:.4f}, ",
                    f"                      border_color = {color}, bgcolor = color.new({color}, 85), ",
                    f"                      border_width = 1, extend = extend.both)",
                    "    array.push(box_array, zone_box)",
                    f"    zone_label = label.new(bar_index, {((top + bottom) / 2):.4f}, ",
                    f"                          text = '{zone_name}', style = label.style_none, ",
                    f"                          color = color.new(color.white, 100), textcolor = {color}, ",
                    "                          size = size.small)",
                    "    array.push(label_array, zone_label)",
                    ""
                ])

            # Add levels as horizontal lines
            for i, level in enumerate(levels_data):
                level_name = level.get('name', f'Level {i+1}')
                price = level.get('price', 0)
                color = level.get('color', 'color.white')
                level_type = level.get('level_type', 'level')

                script_lines.extend([
                    f"// {level_name} ({level_type.title()}) at {price:.4f}",
                    "if barstate.islast",
                    f"    level_line = line.new(bar_index - 100, {price:.4f}, bar_index + 100, {price:.4f}, ",
                    f"                         color = {color}, width = 2, extend = extend.both)",
                    "    array.push(line_array, level_line)",
                    f"    level_label = label.new(bar_index + 50, {price:.4f}, ",
                    f"                           text = '{level_name}: {price:.4f}', style = label.style_none, ",
                    f"                           color = color.new(color.white, 100), textcolor = {color}, ",
                    "                           size = size.small)",
                    "    array.push(label_array, level_label)",
                    ""
                ])

            # Add summary
            script_lines.extend([
                f"// Summary: Generated {len(zones_data)} zones and {len(levels_data)} levels",
                "// Items included in this script:"
            ])

            # List zones
            if zones_data:
                script_lines.append("// ZONES:")
                for zone in zones_data:
                    zone_name = zone.get('name', 'Unknown Zone')
                    top = zone.get('top', 0)
                    bottom = zone.get('bottom', 0)
                    script_lines.append(f"// - {zone_name}: {bottom:.4f} to {top:.4f}")

            # List levels
            if levels_data:
                script_lines.append("// LEVELS:")
                for level in levels_data:
                    level_name = level.get('name', 'Unknown Level')
                    price = level.get('price', 0)
                    script_lines.append(f"// - {level_name}: {price:.4f}")

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"Error generating Pine Script with levels: {str(e)}")
            return f"// Error generating Pine Script with levels: {str(e)}"

    def _show_pine_script_dialog(self, pine_script, item_count):
        """Show Pine Script in a popup dialog with copy functionality"""
        try:
            # Create dialog
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("TradingView Pine Script Export")
            dialog.setModal(True)
            dialog.resize(800, 600)

            # Create layout
            layout = QtWidgets.QVBoxLayout(dialog)

            # Add title label
            title_label = QtWidgets.QLabel(f"Exported {item_count} zones & levels to TradingView Pine Script")
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #e0e0e0;
                    padding: 10px;
                }
            """)
            layout.addWidget(title_label)

            # Add instructions label
            instructions = QtWidgets.QLabel(
                "Instructions:\n"
                "1. Copy the script below (Ctrl+A to select all, Ctrl+C to copy)\n"
                "2. Open TradingView Pine Editor\n"
                "3. Paste the script and click 'Add to Chart'"
            )
            instructions.setStyleSheet("""
                QLabel {
                    color: #b0b0b0;
                    padding: 5px 10px;
                    background-color: #2d2d2d;
                    border-radius: 4px;
                    margin: 5px;
                }
            """)
            layout.addWidget(instructions)

            # Create text area for Pine Script
            text_area = QtWidgets.QTextEdit()
            text_area.setPlainText(pine_script)
            text_area.setFont(QtGui.QFont("Consolas", 10))
            text_area.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                    border: 1px solid #3e3e3e;
                    border-radius: 4px;
                    padding: 10px;
                    font-family: 'Consolas', 'Courier New', monospace;
                }
            """)
            # Select all text for easy copying
            text_area.selectAll()
            layout.addWidget(text_area)

            # Create button layout
            button_layout = QtWidgets.QHBoxLayout()

            # Copy to clipboard button
            copy_button = QtWidgets.QPushButton("Copy to Clipboard")
            copy_button.clicked.connect(lambda: self._copy_to_clipboard(pine_script))
            copy_button.setStyleSheet("""
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0098ff;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
            """)
            button_layout.addWidget(copy_button)

            # Save to file button (optional)
            save_button = QtWidgets.QPushButton("Save to File")
            save_button.clicked.connect(lambda: self._save_pine_script_to_file(pine_script))
            save_button.setStyleSheet("""
                QPushButton {
                    background-color: #2d2d2d;
                    color: white;
                    border: 1px solid #3e3e3e;
                    border-radius: 4px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #3e3e3e;
                    border: 1px solid white;
                }
            """)
            button_layout.addWidget(save_button)

            # Close button
            close_button = QtWidgets.QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #2d2d2d;
                    color: white;
                    border: 1px solid #3e3e3e;
                    border-radius: 4px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #3e3e3e;
                    border: 1px solid white;
                }
            """)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            # Show dialog
            dialog.exec()

        except Exception as e:
            print(f"Error showing Pine Script dialog: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Dialog Error",
                f"Error showing Pine Script dialog: {str(e)}")

    def _copy_to_clipboard(self, text):
        """Copy text to system clipboard"""
        try:
            clipboard = QtWidgets.QApplication.clipboard()
            clipboard.setText(text)

            # Show brief confirmation
            QtWidgets.QMessageBox.information(self, "Copied",
                "Pine Script copied to clipboard!\n\nYou can now paste it in TradingView Pine Editor.")

        except Exception as e:
            print(f"Error copying to clipboard: {str(e)}")
            QtWidgets.QMessageBox.warning(self, "Copy Error",
                f"Failed to copy to clipboard: {str(e)}")

    def _save_pine_script_to_file(self, pine_script):
        """Save Pine Script to file (optional functionality)"""
        try:
            filename, _ = QtWidgets.QFileDialog.getSaveFileName(
                self, "Save TradingView Pine Script", "zones_export.pine",
                "Pine Script Files (*.pine);;Text Files (*.txt);;All Files (*)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(pine_script)

                QtWidgets.QMessageBox.information(self, "File Saved",
                    f"Pine Script saved to:\n{filename}")

        except Exception as e:
            print(f"Error saving Pine Script file: {str(e)}")
            QtWidgets.QMessageBox.critical(self, "Save Error",
                f"Error saving file: {str(e)}")

    def on_candlestick_timeframe_changed(self, timeframe):
        """Handle timeframe change from candlestick chart's own timeframe selector"""
        try:
            print(f"Candlestick chart timeframe changed to: {timeframe}")
            # Update the hidden timeframe combo to keep it in sync
            self.timeframe_combo.setCurrentText(timeframe)

            # Show appropriate period info for the user
            if timeframe in ["1m", "2m", "5m"]:
                print(f"Using 7-day period for {timeframe} interval (Yahoo Finance limit)")
            elif timeframe in ["15m", "30m", "60m", "1h"]:
                print(f"Using 60-day period for {timeframe} interval")
            else:
                days = self.days_spin.value()
                print(f"Using {days}-day period for {timeframe} interval")

            # Fetch new data with the selected timeframe
            if hasattr(self, 'symbol_input') and self.symbol_input.text().strip():
                self.fetch_data()
        except Exception as e:
            print(f"Error changing candlestick timeframe: {str(e)}")
            import traceback
            traceback.print_exc()

    def toggle_line_indicator(self):
        """Toggle the line indicator (The Line) display"""
        try:
            # Toggle the checkbox state
            current_state = self.show_vector_checkbox.isChecked()
            new_state = not current_state
            self.show_vector_checkbox.setChecked(new_state)

            # Update button text based on new state
            if new_state:
                self.toggle_line_button.setText("Hide Line")
                print("Line indicator enabled")
            else:
                self.toggle_line_button.setText("Show Line")
                print("Line indicator disabled")

            # Update the chart to reflect the change
            self.update_chart()

        except Exception as e:
            print(f"Error toggling line indicator: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_ak_levels_table(self):
        """Show a data table with AK levels values sorted by matching type"""
        try:
            # Get the volatility graph tab to access AK levels
            volatility_tab = self.get_volatility_graph_tab()
            if volatility_tab is None:
                QtWidgets.QMessageBox.information(self, "Data Table", "Could not find volatility graph tab")
                return

            # Check if AK's weekly vol zones is active
            if not hasattr(volatility_tab, 'ak_weekly_vol_zones_active') or not volatility_tab.ak_weekly_vol_zones_active:
                QtWidgets.QMessageBox.information(self, "Data Table", "AK's weekly vol zones is not active.\nPlease activate it first to view the data table.")
                return

            # Get the AK levels
            if not hasattr(volatility_tab, 'ak_fwl5_levels') or not hasattr(volatility_tab, 'ak_weekday_levels'):
                QtWidgets.QMessageBox.information(self, "Data Table", "No AK levels data available.\nPlease calculate AK's weekly vol zones first.")
                return

            # Create and show the data table dialog
            self.create_ak_levels_dialog(volatility_tab.ak_fwl5_levels, volatility_tab.ak_weekday_levels)

        except Exception as e:
            print(f"Error showing AK levels table: {str(e)}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "Error", f"Error showing data table: {str(e)}")

    def create_ak_levels_dialog(self, hl_levels, weekday_levels):
        """Create and display the AK levels data table dialog"""
        try:
            # Create dialog
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("AK's Weekly Vol Zones - Data Table")
            dialog.setModal(True)
            dialog.resize(600, 400)

            # Set dialog style
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                }}
                QTableWidget {{
                    background-color: #2d2d2d;
                    color: #e0e0e0;
                    gridline-color: #3e3e3e;
                    selection-background-color: #007acc;
                    border: 1px solid #3e3e3e;
                }}
                QTableWidget::item {{
                    padding: 5px;
                    border-bottom: 1px solid #3e3e3e;
                }}
                QHeaderView::section {{
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    padding: 5px;
                    border: 1px solid #1e1e1e;
                    font-weight: bold;
                }}
                QPushButton {{
                    background-color: #2d2d2d;
                    color: white;
                    border: 1px solid #3e3e3e;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #3e3e3e;
                    border: 1px solid white;
                }}
            """)

            # Create layout
            layout = QtWidgets.QVBoxLayout(dialog)

            # Create table widget
            table = QtWidgets.QTableWidget()
            table.setColumnCount(3)
            table.setHorizontalHeaderLabels(["Level Type", "Matching Type", "Value"])

            # Prepare data for the table
            table_data = []

            # Add H/L FWL5 levels
            for key, value in hl_levels.items():
                if value is not None and key not in ['high_values', 'low_values']:
                    level_name = self.format_level_name(key)
                    table_data.append([level_name, "H/L FWL5", f"{value:.4f}"])

            # Add Weekday FWL5 levels
            for key, value in weekday_levels.items():
                if value is not None and key not in ['high_values', 'low_values']:
                    level_name = self.format_level_name(key)
                    table_data.append([level_name, "Weekday FWL5", f"{value:.4f}"])

            # Sort data by matching type, then by level type
            table_data.sort(key=lambda x: (x[1], x[0]))

            # Populate table
            table.setRowCount(len(table_data))
            for row, (level_type, matching_type, value) in enumerate(table_data):
                table.setItem(row, 0, QtWidgets.QTableWidgetItem(level_type))
                table.setItem(row, 1, QtWidgets.QTableWidgetItem(matching_type))
                table.setItem(row, 2, QtWidgets.QTableWidgetItem(value))

            # Adjust column widths
            table.resizeColumnsToContents()
            table.horizontalHeader().setStretchLastSection(True)

            # Add table to layout
            layout.addWidget(table)

            # Add close button
            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addStretch()
            close_button = QtWidgets.QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            button_layout.addWidget(close_button)
            layout.addLayout(button_layout)

            # Show dialog
            dialog.exec()

        except Exception as e:
            print(f"Error creating AK levels dialog: {str(e)}")
            import traceback
            traceback.print_exc()

    def format_level_name(self, key):
        """Format level key names for display in the table"""
        name_mapping = {
            'highest_high': 'Max High',
            'maxavg_high': 'MaxAvg High',
            'avg_high_lowest_high': 'High Median',
            'minavg_high': 'MinAvg High',
            'true_avg_high': 'Avg High',
            'apex': 'Apex',
            'true_avg_low': 'Avg Low',
            'minavg_low': 'MinAvg Low',
            'avg_low_highest_low': 'Low Median',
            'maxavg_low': 'MaxAvg Low',
            'lowest_low': 'Max Low'
        }
        return name_mapping.get(key, key.replace('_', ' ').title())

    def update_vector_length(self):
        """Update The Line length based on spinner value"""
        # Update display options
        new_length = self.vector_length_spinner.value()
        self.display_options['vector_length'] = new_length

        # Update the parameter registry to keep it in sync with universal controls
        from parameter_registry import default_registry
        default_registry.set_value('vector_length', new_length)

        # Only update chart if The Line is visible
        if self.display_options.get('show_vector', False):
            self.update_chart()

    def apply_settings(self, chart_colors, display_options):
        """Apply settings from the dialog"""
        # Update chart colors
        self.chart_colors = chart_colors

        # Update display options
        self.display_options = display_options

        # Update UI controls to match settings
        self.show_volume_profile.setChecked(display_options.get('show_volume_profile', True))
        self.show_vector_checkbox.setChecked(display_options.get('show_vector', False))
        self.vector_length_spinner.setValue(display_options.get('vector_length', 10))
        # show_volatility_levels checkbox removed - only AK's weekly vol zones controls levels now

        # Update timer interval
        self.update_timer.setInterval(display_options.get('update_interval', 60000))

        # Update chart
        self.update_chart()

    def save_chart_settings(self):
        """Save settings to QSettings"""
        settings = QtCore.QSettings('CandlestickChartApp', 'ChartAppearance')

        # Save colors
        for key, value in self.chart_colors.items():
            settings.setValue(f'chart_color_{key}', value)

        # Save display options
        for key, value in self.display_options.items():
            settings.setValue(f'display_option_{key}', value)

        settings.sync()

    def load_chart_settings(self):
        """Load settings from QSettings"""
        settings = QtCore.QSettings('CandlestickChartApp', 'ChartAppearance')

        # Load colors
        for key in self.chart_colors.keys():
            setting_key = f'chart_color_{key}'
            if settings.contains(setting_key):
                self.chart_colors[key] = settings.value(setting_key)

        # Load display options
        for key in self.display_options.keys():
            setting_key = f'display_option_{key}'
            if settings.contains(setting_key):
                # Handle boolean values correctly
                if isinstance(self.display_options[key], bool):
                    value = settings.value(setting_key, type=bool)
                # Handle numeric values correctly
                elif isinstance(self.display_options[key], (int, float)):
                    value = float(settings.value(setting_key))
                    if key != 'grid_opacity' and int(value) == value:
                        value = int(value)
                else:
                    value = settings.value(setting_key)
                self.display_options[key] = value

    def on_parameter_changed(self, name, value):
        """Handle parameter changes from the parameter registry."""
        if name == 'vector_length':
            # Update the spinner value if it's different
            if hasattr(self, 'vector_length_spinner') and self.vector_length_spinner.value() != value:
                self.vector_length_spinner.setValue(value)

            # Update display options
            self.display_options['vector_length'] = value

            # Update the chart if The Line is visible
            if self.display_options.get('show_vector', False) and hasattr(self, 'data') and not self.data.empty:
                self.update_chart()

    def import_density_zones(self, zones_data):
        """
        Import density zones from the density graph and display them as horizontal zones.

        Args:
            zones_data: List of zone dictionaries from density graph

        Returns:
            int: Number of zones successfully imported
        """
        try:
            print(f"Candlestick Chart: Importing {len(zones_data)} density zones")
            success_count = 0

            for zone_data in zones_data:
                try:
                    # Extract zone properties
                    zone_name = zone_data.get('name', 'Density Zone')
                    top_price = zone_data.get('top')
                    bottom_price = zone_data.get('bottom')
                    zone_color = zone_data.get('color', '#FFC107')  # Default to amber

                    # Validate zone data
                    if top_price is None or bottom_price is None:
                        print(f"Skipping zone {zone_name}: missing price data")
                        continue

                    if top_price <= bottom_price:
                        print(f"Skipping zone {zone_name}: invalid price range")
                        continue

                    # Convert color to RGB with transparency for zone fill
                    zone_color_with_alpha = self.convert_color_to_rgba(zone_color, alpha=50)

                    # Create a horizontal LinearRegionItem for the density zone
                    zone_region = pg.LinearRegionItem(
                        values=[bottom_price, top_price],
                        orientation='horizontal',
                        brush=pg.mkBrush(color=zone_color_with_alpha),
                        pen=pg.mkPen(color=zone_color, width=2, style=QtCore.Qt.PenStyle.SolidLine),
                        movable=False  # Make it non-interactive
                    )

                    # Mark the zone with source information for later removal
                    zone_region.source = zone_data.get('source', 'density_graph')
                    zone_region.zone_name = zone_name

                    # Add zone to plot
                    self.price_plot.addItem(zone_region)

                    # Store reference for later removal in dedicated density zones list
                    self.ak_density_zones.append(zone_region)

                    success_count += 1
                    print(f"Imported density zone: {zone_name} from {bottom_price:.2f} to {top_price:.2f}")

                except Exception as e:
                    print(f"Error importing individual zone: {str(e)}")
                    continue

            print(f"Successfully imported {success_count} density zones to candlestick chart")
            return success_count

        except Exception as e:
            print(f"Error importing density zones: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0

    def convert_color_to_rgba(self, color_str, alpha=50):
        """
        Convert a color string to RGBA tuple with specified alpha.

        Args:
            color_str: Color string (hex, named color, etc.)
            alpha: Alpha value (0-255)

        Returns:
            tuple: RGBA color tuple
        """
        try:
            # Handle hex colors
            if color_str.startswith('#'):
                hex_color = color_str.lstrip('#')
                if len(hex_color) == 6:
                    r = int(hex_color[0:2], 16)
                    g = int(hex_color[2:4], 16)
                    b = int(hex_color[4:6], 16)
                    return (r, g, b, alpha)

            # Handle named colors
            color_map = {
                'orange': (255, 165, 0, alpha),
                'red': (255, 0, 0, alpha),
                'cyan': (0, 255, 255, alpha),
                'green': (0, 255, 0, alpha),
                'blue': (0, 0, 255, alpha),
                'yellow': (255, 255, 0, alpha),
                'purple': (128, 0, 128, alpha),
                'gray': (128, 128, 128, alpha),
                'grey': (128, 128, 128, alpha)
            }

            if color_str.lower() in color_map:
                return color_map[color_str.lower()]

            # Default fallback
            return (255, 193, 7, alpha)  # Amber color

        except Exception as e:
            print(f"Error converting color {color_str}: {str(e)}")
            return (255, 193, 7, alpha)  # Amber fallback