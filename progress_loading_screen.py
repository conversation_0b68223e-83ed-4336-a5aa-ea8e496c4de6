"""
Progress Loading Screen Module

This module provides an enhanced loading screen with progress indication
for better user experience during long-running operations.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
import math

class ProgressLoadingScreen(QtWidgets.QWidget):
    """
    A semi-transparent overlay with a spinner animation, progress bar, and message.
    
    This class provides a modern loading screen that can be displayed over any widget
    to indicate that a time-consuming operation is in progress, with visual feedback
    on the operation's progress.
    """

    def __init__(self, parent=None, message="Loading...", theme_colors=None):
        """
        Initialize the progress loading screen.
        
        Args:
            parent: Parent widget
            message: Message to display
            theme_colors: Dictionary of theme colors
        """
        super().__init__(parent)
        
        # Store parameters
        self.parent = parent
        self.message = message
        self.progress = 0  # Progress value (0-100)
        self.sub_message = ""  # Additional message for progress details
        
        # Use provided theme colors or defaults
        self.theme_colors = theme_colors or {
            'background': '#1e1e1e',
            'text': '#E0E0E0',
            'primary_accent': '#007acc',
            'progress_bg': '#2d2d2d',
            'progress_fill': '#007acc'
        }
        
        # Set up the widget
        self.setObjectName("ProgressLoadingScreen")
        self.setup_ui()
        
        # Animation properties
        self.angle = 0
        self.animation_timer = QtCore.QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(16)  # ~60 FPS
        
    def setup_ui(self):
        """Set up the user interface."""
        # Make the widget cover its parent
        if self.parent:
            self.resize(self.parent.size())
            self.parent.resizeEvent = self.handle_parent_resize
        
        # Set up appearance
        self.setStyleSheet(f"""
            QWidget#ProgressLoadingScreen {{
                background-color: rgba(30, 30, 30, 180);
                border-radius: 10px;
            }}
        """)
        
        # Create layout
        self.layout = QtWidgets.QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(15)
        self.layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Create container widget with rounded corners
        self.container = QtWidgets.QWidget()
        self.container.setObjectName("LoadingContainer")
        self.container.setStyleSheet(f"""
            QWidget#LoadingContainer {{
                background-color: rgba(45, 45, 45, 220);
                border-radius: 10px;
                border: 1px solid rgba(60, 60, 60, 220);
            }}
        """)
        self.container.setFixedSize(300, 200)
        self.layout.addWidget(self.container, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Container layout
        container_layout = QtWidgets.QVBoxLayout(self.container)
        container_layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(15)
        
        # Spinner placeholder (will be drawn in paintEvent)
        self.spinner_widget = QtWidgets.QWidget()
        self.spinner_widget.setFixedSize(80, 80)
        container_layout.addWidget(self.spinner_widget, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Message label
        self.message_label = QtWidgets.QLabel(self.message)
        self.message_label.setStyleSheet(f"color: {self.theme_colors['text']}; font-size: 14px;")
        self.message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.message_label)
        
        # Progress bar
        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: {self.theme_colors['progress_bg']};
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                text-align: center;
                color: {self.theme_colors['text']};
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {self.theme_colors['progress_fill']};
                border-radius: 3px;
            }}
        """)
        container_layout.addWidget(self.progress_bar)
        
        # Sub-message label (for detailed progress info)
        self.sub_message_label = QtWidgets.QLabel(self.sub_message)
        self.sub_message_label.setStyleSheet(f"color: {self.theme_colors['text']}; font-size: 12px;")
        self.sub_message_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.sub_message_label)
        
        # Set the widget to be semi-transparent
        self.setWindowOpacity(0.9)
        
        # Ensure this widget stays on top
        self.raise_()
        
    def handle_parent_resize(self, event):
        """Handle parent widget resize events."""
        if self.parent:
            self.resize(self.parent.size())
        if hasattr(self.parent, 'resizeEvent'):
            self.parent.resizeEvent(event)
            
    def set_message(self, message):
        """Set the main message text."""
        self.message = message
        self.message_label.setText(message)
        
    def set_sub_message(self, message):
        """Set the sub-message text for detailed progress info."""
        self.sub_message = message
        self.sub_message_label.setText(message)
        
    def set_progress(self, value):
        """Set the progress value (0-100)."""
        self.progress = max(0, min(100, value))
        self.progress_bar.setValue(self.progress)
        
    def update_progress(self, value, message=None):
        """Update both progress value and sub-message."""
        self.set_progress(value)
        if message:
            self.set_sub_message(message)
            
    def update_animation(self):
        """Update the spinner animation."""
        self.angle = (self.angle + 5) % 360
        self.spinner_widget.update()
        
    def paintEvent(self, event):
        """Paint the widget."""
        super().paintEvent(event)
        
        # Draw spinner on the spinner widget
        spinner_painter = QtGui.QPainter(self.spinner_widget)
        spinner_painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)
        
        # Calculate spinner parameters
        center = self.spinner_widget.rect().center()
        radius = min(self.spinner_widget.width(), self.spinner_widget.height()) // 2 - 10

        # Create gradient for spinner (convert QPoint to QPointF)
        center_f = QtCore.QPointF(center)
        gradient = QtGui.QConicalGradient(center_f, self.angle)
        gradient.setColorAt(0, QtGui.QColor(self.theme_colors['primary_accent']))
        gradient.setColorAt(0.5, QtGui.QColor(self.theme_colors['primary_accent']).lighter(150))
        gradient.setColorAt(1, QtGui.QColor(self.theme_colors['primary_accent']))
        
        # Draw spinner
        spinner_painter.translate(center)
        spinner_painter.rotate(-self.angle)
        spinner_painter.translate(-center)
        
        pen = QtGui.QPen(QtGui.QBrush(gradient), 6)
        pen.setCapStyle(QtCore.Qt.PenCapStyle.RoundCap)
        spinner_painter.setPen(pen)
        spinner_painter.drawArc(center.x() - radius, center.y() - radius, 
                               radius * 2, radius * 2, 0, 300 * 16)
        
    def showEvent(self, event):
        """Start animation when shown."""
        self.animation_timer.start()
        super().showEvent(event)
        
    def hideEvent(self, event):
        """Stop animation when hidden."""
        self.animation_timer.stop()
        super().hideEvent(event)
